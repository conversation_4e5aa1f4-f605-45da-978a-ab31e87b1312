<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js p-md-5 order-management">
    <div class="container">
        <div class="d-flex justify-content-center align-items-center mt-5 mt-md-0">
            <h3><strong>Users upload</strong></h3>
        </div>
            <div class="form-group">
                <div class="form-group">
                    <input type="hidden" name="mode" value="submit">
                    <input id="FileInputElement" type="file" class="form-control" name="FileInputElement" accept=".xlsx" />
                    <small><em>(Max 200 users at once)</em></small>
                </div>

                <button class="btn btn-primary btadd" onclick="javascript:uploadUsers()">Upload Users</button>
            </div>
            <div style="margin-top: 10px;" class="p-2">
                <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display:none;background: none;"></div>
                <div id="successmsg" class="d-none"></div>
                <div id="batchUsers" class=" d-none p-2"></div>
                <h5 id="successmsgnew" class="d-none" style="color: green"></h5>
            </div>


    </div>
</section>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    function uploadUsers() {
        $("#errormsg").hide();
        $("#successmsg").hide();
        console.log("calling");
        var qImg = document.getElementById("FileInputElement").files[0];
        if(qImg==undefined){
            document.getElementById("errormsg").innerText="Please upload the file to proceed.";
            $("#errormsg").show();
            $("#errormsg").removeClass('d-none')
        }else {
            $('.loading-icon').removeClass('hidden');
            var formData = new FormData();
            formData.append('file', qImg);
            formData.append('poType', "specimen");
            $.ajax({
                type: 'POST',
                url: '/institute/addUsersAndBooks',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    if(data.status=="FILE_ERROR"){
                        document.getElementById("errormsg").innerText="Please check the file and try again.";
                        $("#errormsg").show();
                        $("#errormsg").removeClass('d-none');
                        $('.loading-icon').addClass('hidden');
                    }else{

                        var html="Upload successful";

                        document.getElementById("successmsg").innerHTML=html;
                        $("#successmsg").show();
                        $("#successmsg").removeClass('d-none')
                        $('.loading-icon').addClass('hidden');
                    }

                },
                error: function (data) {
                    console.log("error");
                }
            });
        }
    }
</script>
