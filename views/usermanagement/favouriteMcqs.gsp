<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<style>
    ::-webkit-scrollbar {
        width: 0;  /* Remove scrollbar space */
        background: transparent;  /* Optional: just make scrollbar invisible */
    }
    * {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
    footer{
        display: none;
    }
    .g-blue{ color:#4285F4; }
    .o-red{ color:#DB4437; }
    .o-yellow{ color:#F4B400; }
    .l-green{ color:#0F9D58; }
    .e-red { display:inline-block;transform:rotate(-20deg); }

    .fav_mcqs-card_question,
    .fav_mcqs-card_option,
    .fav_mcqs-card_exp{
        overflow-x: scroll;
    }
    .explanationBtns button {
        background: transparent;
        color: #F79420;
    }
    .video_iframe-fav {
        width: 100%;
        height: 350px;
    }
    .fav_title,
    .fav_subjects{
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
    .mcqChatBtns {
        gap: 0.5rem;
        margin-left: -20px;
        margin-right: -20px;
        background: #f6f6f6;
        padding: 10px;
        margin-bottom: -5px;
        border-radius: 5px;
    }
    .mcqChatBtns .exp {
        background: #8854fe;
        color: #fff;
        transition: all 0.4s ease;
    }
    .mcqChatBtns .exp img {
        width: 20px;
        margin-right: 6px;
    }
    .mcqChatBtns .exp:hover {
        background: #F79420 !important;
        color: #fff;
    }
    .mcqChatBtns .squ {
        background: #525152;
        transition: all 0.4s ease;
        color: #fff;
    }
    .mcqChatBtns .squ img {
        width: 20px;
        margin-right: 6px;
    }
    .mcqChatBtns .squ:hover {
        background: #F79420 !important;
        color: #fff;
    }
    @media (max-width: 768px) {
        .mcqChatBtns {
            bottom: -5px;
        }
    }
    .exp:hover .button-icon {
        filter: grayscale(100%) brightness(100) sepia(100%) hue-rotate(45deg) saturate(500%) contrast(1);
    }
    .squ:hover .button-icon2 {
        filter: grayscale(100%) brightness(100) sepia(100%) hue-rotate(45deg) saturate(500%) contrast(1);
    }
    .ibookgpt-section,
    .footer-menus{
        display: none;
    }
    .fav_mcqs-card{
        width: 100%;
    }
    html,body{
        overflow: hidden;
    }
    .fav_mcqsList{
        height: 70vh;
        overflow: scroll;
    }

</style>
<!----- Container ---->
<div class="container">
    <div class="row">
        <section class="col-12 col-lg-12 fav_title" title="Favourite MCQs">
            <h2 class="fav_title-text">Favourite MCQs</h2>
        </section>
        <section class="col-12 col-lg-12 fav_subjects" title="Favourite MCQs Subjects">
            <select name="favSubjectList" id="favSubList" class="fav_subjects-dropdown">
                <option value="">All Subject</option>
            </select>
            <button class="clearFilterIcon" disabled>
                <img src="${assetPath(src: 'resource/filter.png')}" class="mr-2" >
            </button>
        </section>

        <section class="col-12 col-lg-7 fav_mcqsList">
            <div class="fav_mcqs-card">
                <div class="fav_mcqs-card_star d-none">
                    <i class="fa-regular fa-star"></i>
                </div>
                <div class="fav_mcqs-card_question pre-loaderDiv">
                    <p></p>
                </div>
                <div class="fav_mcqs-card_options">
                    <div class="fav_mcqs-card_option shimmerLoader">
                        <p></p>
                    </div>
                    <div class="fav_mcqs-card_option shimmerLoader">
                        <p></p>
                    </div>
                    <div class="fav_mcqs-card_option shimmerLoader">
                        <p></p>
                    </div>
                    <div class="fav_mcqs-card_option shimmerLoader">
                        <p></p>
                    </div>
                </div>
            </div>
            <div class="fav_mcqs-card">
                <div class='fav_mcqs-card_star d-none'>
                    <i class="fa-regular fa-star"></i>
                </div>
                <div class="fav_mcqs-card_question pre-loaderDiv">
                    <p></p>
                </div>
                <div class="fav_mcqs-card_options">
                    <div class="fav_mcqs-card_option shimmerLoader">
                        <p></p>
                    </div>
                    <div class="fav_mcqs-card_option shimmerLoader">
                        <p></p>
                    </div>
                    <div class="fav_mcqs-card_option shimmerLoader">
                        <p></p>
                    </div>
                    <div class="fav_mcqs-card_option shimmerLoader">
                        <p></p>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>
<g:render template="/prompt/chatModule"></g:render>


<!--------  VIDEO EXPLANATION MODAL --------->
<div class="modal fade modal-modifier" id="videoExplanation">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close mr-2 text-white" data-dismiss="modal" aria-label="Close" style="text-align: end">
                <span aria-hidden="true">x</span>
            </button>
            <div class="modal-body modal-body-modifier text-center">
                <div id="videoExplanationFrame">
                    <iframe class="video_iframe-fav" src="" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>

    var filter=false;
    var questions;
    let ibookgpt;
    function getAllFavouritesList(subject){
        var url = "";
        if (subject==""){
            url = '/funlearn/quizQuestionAnswers?favouriteMCQs=true'
        }else{
            url = "/funlearn/quizQuestionAnswers?favouriteMCQs=true&subject="+subject;
        }
        $.ajax({
            url: url,
            type: "GET",
            dataType: "json",
            contentType: "application/json",
            success: function (result) {
                lazyLoader();
                displayFavouritesUI(result)
            },
            error: function (error) {
                lazyLoader();
                displayFavouritesUI(error)
            },
        });
    }

    getAllFavouritesList("");
    function displayFavouritesUI(data){
        ibookgpt = initIbookgpt({
            chatEndPoint:"/prompt/retrieveData", // /api/retrieveData
            docType:"mcq", // "pdf" "txt", "mcq", "epub"
            enableHistory:false,
            enableToken:false,
            enableSnip:false,
            questions:data.results
        })
        questions = data.results;
        var subjects = data.subject;
        var status = data.status;
        var questionHTML = "";
        var subjectsHTML = "";
        var questionText;
        var option1;
        var option2;
        var option3;
        var option4;
        var option5;
        if (status!=500){
            for (var q=0;q<questions.length;q++){
                questionText = questions[q].ps;
                option1 = questions[q].op1;
                option2 = questions[q].op2;
                option3 = questions[q].op3;
                option4 = questions[q].op4;
                questionHTML += "<div class='fav_mcqs-card' id='qid-"+questions[q].id+"'>"+
                    "<div class='fav_mcqs-card_star d-none' title='Un-favourite'>"+
                        "<img src=\"${assetPath(src: 'mobile/language1.svg')}\" class='fa-language d-none lang1' style='font-size: 2rem;cursor: pointer;margin-right: 10px;' onclick='switchResultLanguage()' id='lang1-"+questions[q].id+"'>"+
                        "<img src=\"${assetPath(src: 'mobile/language.svg')}\" class='fa-language1 d-none lang2' style='font-size: 2rem;cursor: pointer;margin-right: 10px;' onclick='switchResultLanguage()' id='lang2-"+questions[q].id+"'>"+
                        "<i class='fa-solid fa-star starBtn star' style='color: #ffdc64;' id='"+questions[q].id+"'></i>"+
                    "</div>"+
                    "<div class='fav_mcqs-card_question shimmerLoader pre-loaderDiv' id='que-"+questions[q].id+"'>"+
                        "<p class='que_text'>"+questionText+"</p>"+
                    "</div>"+
                    "<div class='fav_mcqs-card_options'>";

                        if (questions[q].ans1=='Yes'){
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-1 correctAnsHl shimmerLoader'>"+
                                "<p>"+option1+"</p>"+
                            "</div>";
                        }else{
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-1  shimmerLoader'>"+
                                "<p>"+option1+"</p>"+
                            "</div>";
                        }

                        if (questions[q].ans2=='Yes'){
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-2 correctAnsHl shimmerLoader'>"+
                                "<p>"+option2+"</p>"+
                            "</div>";
                        }else{
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-2 shimmerLoader'>"+
                                "<p>"+option2+"</p>"+
                            "</div>";
                        }

                        if (questions[q].ans3=='Yes'){
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-3 correctAnsHl shimmerLoader'>"+
                                "<p>"+option3+"</p>"+
                            "</div>";
                        }else{
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-3 shimmerLoader'>"+
                                "<p>"+option3+"</p>"+
                            "</div>";
                        }

                        if (questions[q].ans4=='Yes'){
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-4 correctAnsHl shimmerLoader'>"+
                                "<p>"+option4+"</p>"+
                            "</div>";
                        }else{
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-4 shimmerLoader'>"+
                                "<p>"+option4+"</p>"+
                            "</div>";
                        }

                    if(questions[q].op5 !=null && questions[q].op5!=""){
                        option5 = questions[q].op5;
                        if (questions[q].ans5=='Yes'){
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-5 correctAnsHl shimmerLoader'>"+
                                "<p>"+option5+"</p>"+
                            "</div>";
                        }else{
                            questionHTML +=
                            "<div class='fav_mcqs-card_option option-5 shimmerLoader'>"+
                                "<p>"+option5+"</p>"+
                            "</div>";
                        }
                    }
                    if (questions[q].answerDescription!=null && questions[q].answerDescription!=""){
                        questionHTML +=
                           " <div class='fav_mcqs-card_exp'>" +
                                "<h5>Explanation</h5>"+
                                "<div>"+questions[q].answerDescription+"</div>"+
                            "</div>";
                    }
                    questionHTML+=
                        "<div class='explanationBtns mt-3 d-flex justify-content-center align-items-center flex-column flex-lg-row flex-md-row'>";

                    if (questions[q].explainLink!=null && questions[q].explainLink!=""){
                        questionHTML+="<button class='btn d-flex align-items-center show-video-explanation' onclick='videoExplanation("+questions[q].id+")'>" +
                            "<i class='material-icons mr-1'>ondemand_video</i>Video Explanation"+
                            "</button>";
                    }
                    questionHTML+="</div>";
                    questionHTML+="</div>"+
                "</div>";
            }
            document.querySelector('.fav_mcqsList').innerHTML = questionHTML;

            document.querySelectorAll('.fav_mcqs-card_star').forEach(star=>{
                star.classList.remove('d-none');
            });
            addMathjax();
            document.querySelectorAll('.starBtn').forEach(starBtn=>{
                starBtn.addEventListener('click',function (){
                    if (starBtn.classList.contains('star')){
                        unFavourite(starBtn.id)
                    }
                })
            })
        }else{
            var str = "";
            str+= '<div class="text-center d-flex flex-column align-items-center justify-content-center"><h4 class="text-center" style="margin-top: 5rem;">No Favourites found :(</h4>'+
                '<p style="margin-top: 3px;">Add your favourite MCQs by taking tests</p>';
            str+='</div>';
            document.querySelector('.fav_mcqsList').innerHTML =str;
            document.querySelector('.fav_subjects-dropdown').style.display = 'none';
            document.querySelector('.clearFilterIcon').style.display = 'none';
        }

        document.querySelectorAll('.fav_mcqs-card_option').forEach(option=>{
            option.classList.remove('shimmerLoader');
        });
        document.querySelectorAll('.fav_mcqs-card_question').forEach(option=>{
            option.classList.remove('pre-loaderDiv','shimmerLoader');
        });

        if(!filter){
            subjectsHTML = '<option value="">All Subject</option>'
            for (var s=0;s<subjects.length;s++){
                subjectsHTML +="<option value='"+subjects[s]+"'>"+subjects[s]+"</option>";
            }
            document.getElementById('favSubList').innerHTML = subjectsHTML;
        }
    }


    function unFavourite(id){
        var quizId = localStorage.getItem('favMcq');
        if (quizId!=null && quizId!=undefined){
            quizId = JSON.parse(quizId);
            quizId = quizId.filter(item => item !== Number(id))
            localStorage.setItem('favMcq',JSON.stringify(quizId));
        }
        <g:remoteFunction controller="usermanagement" action="removeUserMCQFavourite" params="'questionId='+id" onSuccess="removeFavouriteCard(data)" />
    }

    function removeFavouriteCard(data){
        lazyLoader();
        filter=false;
        getAllFavouritesList("");
    }

    document.getElementById('favSubList').addEventListener('change',function (){
        var subject = document.getElementById('favSubList').value;
        filter=true;
        getAllFavouritesList(subject);
        if (subject!=""){
            document.querySelector('.clearFilterIcon').removeAttribute('disabled');
        }else{
            document.querySelector('.clearFilterIcon').setAttribute('disabled','disabled');
        }
    })

    document.querySelector('.clearFilterIcon').addEventListener('click',function (){
        filter=false;
        getAllFavouritesList("");
        document.querySelector('.clearFilterIcon').setAttribute('disabled','disabled');
    })

    function lazyLoader(){
        var loaderHtml="";
        for (var q=0;q<2;q++){
            loaderHtml += "<div class='fav_mcqs-card '>"+
                "<div class='fav_mcqs-card_star d-none'>"+
                "<i class='fa-solid fa-star starBtn star d-none'></i>"+
                "</div>"+
                "<div class='fav_mcqs-card_question shimmerLoader pre-loaderDiv'>"+
                "<p></p>"+
                "</div>"+
                "<div class='fav_mcqs-card_options'>"+
                "<div class='fav_mcqs-card_option shimmerLoader'>"+
                "<p></p>"+
                "</div>"+
                "<div class='fav_mcqs-card_option shimmerLoader'>"+
                "<p></p>"+
                "</div>"+
                "<div class='fav_mcqs-card_option shimmerLoader'>"+
                "<p></p>"+
                "</div>"+
                "<div class='fav_mcqs-card_option shimmerLoader'>"+
                "<p></p>"+
                "</div>"+
                "</div>"+
                "</div>";
        }
        document.querySelector('.fav_mcqsList').innerHTML = loaderHtml;
    }

    function addMathjax(){
        MathJax = { mml: { forceReparse: true } }
        $('head').append('<script src="https://polyfill.io/v3/polyfill.min.js?features=es6">')
        $('head').append('<script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">')
    }

    function showGoogleExplanation(id){
        var query = $('#que-'+id).text();
        var url ='http://www.google.com/search?q=' + query;
        window.open(url,'_blank');
    }

    function videoExplanation(id){
        var videoLink="";
        questions.map(question=>{
            if (id==question.id){
                if (question.explainLink.includes("/")){
                    videoLink = question.explainLink;
                }else{
                    videoLink = "https://www.youtube.com/embed/" + question.explainLink;
                }
                document.querySelector('.video_iframe-fav').setAttribute('src',videoLink);
                $('#videoExplanation').modal('show')
            }
        })

        $('#videoExplanation').on('hidden.bs.modal', function (e) {
            document.querySelector('.video_iframe-fav').setAttribute('src',"");
        })
    }

    function splitLanguage(quizItem,id){
        var quizItemAfterSplit = quizItem;
        if (quizItem.includes("~~")){
            if (id!=undefined){

                $('#lang1-'+id).removeClass('d-none');
            }
            quizItemAfterSplit=quizItem.split("~~")[0]
        }
        return quizItemAfterSplit;
    }

    function askDoubt(type,index) {
        ibookgpt.mcqChat(0, type, index)
    }

</script>