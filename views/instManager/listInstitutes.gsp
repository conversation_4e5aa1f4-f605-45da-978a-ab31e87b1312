<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>

<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
}

.pagination li {
    display: inline;
}

.pagination li a, .pagination li span {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    text-decoration: none;
}

.pagination li a:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination li.active span {
    z-index: 1;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.pagination li.disabled span {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
    border-color: #dee2e6;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container"><br>
    <h2 class="text-center">Institutes Manager</h2>
    <hr/>

    <!-- Add Institute Button -->
    <div class="text-right">
        <g:link action="addInstitute" class="btn btn-primary">Add New Institute</g:link>
    </div>
    <br/>

    <!-- Search and Filters -->
    <form id="searchForm" class="form-inline">
        <div class="form-group">
            <label for="search">Search:</label>&nbsp;
            <input type="text" id="search" name="search" value="${search}" class="form-control" placeholder="Institute Name" size="50"/>
        </div>

    </form>
    <br/>

    <!-- Institutes Table -->
    <div id="instituteList">
        <table class="table table-striped">
            <thead>
            <tr>
                <th>Name</th>
                <th>Contact Name</th>
                <th>Contact Email</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th>Status</th>
                <th>Actions</th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            <g:if test="${institutes}">
                <g:each in="${institutes}" var="institute">
                    <tr>
                        <td>${institute.name}</td>
                        <td>${institute.contactName}</td>
                        <td>${institute.contactEmail}</td>
                        <td>${institute.startDate}</td>
                        <td>${institute.endDate}</td>
                        <td>${institute.status}</td>
                        <td>
                            <g:link controller="instManager" action="addInstitute" params="[id: institute.id, institutionEdit: 'true']" class="btn btn-info btn-sm">Edit</g:link>
                            <g:link controller="instManager" action="adminDashboard" params="[instituteId: institute.id]" class="btn btn-primary btn-sm">Manage</g:link>
                        </td>
                    </tr>
                </g:each>
            </g:if>
            <g:else>
                <tr>
                    <td colspan="7">No institutes found.</td>
                </tr>
            </g:else>
            </tbody>
        </table>
        <!-- Pagination Controls -->

    </div>
    <div id="paginationControls">
        <g:paginate total="${totalCount}" max="${max}" offset="${offset}" params="${params}"/>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
<script type="text/javascript">
    $(document).ready(function() {

        var max = ${max};
        var offset = ${offset};

        // Function to update institute list
        function updateInstituteList(offsetValue) {
            var search = $('#search').val();
            var status = $('#status').val();
            var offsetParam = offsetValue !== undefined ? offsetValue : 0;
            $.ajax({
                url: '${createLink(controller: 'instManager', action: 'searchInstitutes')}',
                data: {
                    search: search,
                    status: status,
                    max: max,
                    offset: offsetParam
                },
                dataType: 'json',
                success: function(data) {
                    var html = '<table class="table table-striped"><thead><tr><th>Name</th><th>Contact Name</th><th>Contact Email</th><th>Start Date</th><th>End Date</th><th>Status</th><th>Actions</th><th></th></tr></thead><tbody>';
                    if (data.institutes.length > 0) {
                        $.each(data.institutes, function(index, institute) {
                            html += '<tr>';
                            html += '<td>' + institute.name + '</td>';
                            html += '<td>' + institute.contactName + '</td>';
                            html += '<td>' + institute.contactEmail + '</td>';
                            html += '<td>' + (institute.startDate || '') + '</td>';
                            html += '<td>' + (institute.endDate || '') + '</td>';
                            html += '<td>' + institute.status + '</td>';
                            html += '<td><a href="${createLink(controller: 'instManager', action: 'addInstitute')}?id=' + institute.id + '&institutionEdit=true" class="btn btn-info btn-sm">Edit</a></td>';
                            html += '<td><a href="${createLink(controller: 'instManager', action: 'adminDashboard')}?instituteId=' + institute.id + '" class="btn btn-primary btn-sm">Manage</a></td>';
                            html += '</tr>';
                        });
                    } else {
                        html += '<tr><td colspan="7">No institutes found.</td></tr>';
                    }
                    html += '</tbody></table>';
                    $('#instituteList').html(html);

                    // Update pagination
                    var totalCount = data.totalCount;
                    var totalPages = Math.ceil(totalCount / max);
                    var currentPage = Math.floor(offsetParam / max) + 1;



                    var paginationHtml = '<ul class="pagination">';
                    //make the pagination right aligned
                    paginationHtml += '<li class="disabled"><span>Page ' + currentPage + ' of ' + totalPages + '</span></li>';

                    if (currentPage > 1) {
                        paginationHtml += '<li><a href="#" data-offset="' + (offsetParam - max) + '">&laquo;</a></li>';
                    } else {
                        paginationHtml += '<li class="disabled"><span>&laquo;</span></li>';
                    }

                    for (var i = 1; i <= totalPages; i++) {
                        var pageOffset = (i - 1) * max;
                        if (i == currentPage) {
                            paginationHtml += '<li class="active"><span>' + i + '</span></li>';
                        } else {
                            paginationHtml += '<li><a href="#" data-offset="' + pageOffset + '">' + i + '</a></li>';
                        }
                    }

                    if (currentPage < totalPages) {
                        paginationHtml += '<li><a href="#" data-offset="' + (offsetParam + max) + '">&raquo;</a></li>';
                    } else {
                        paginationHtml += '<li class="disabled"><span>&raquo;</span></li>';
                    }

                    paginationHtml += '</ul>';
                    console.log("paginationHtml: ", paginationHtml);
                    $('#paginationControls').html(paginationHtml);

                }
            });
        }

        // Bind keyup event on search input
        $('#search').on('keyup', function() {
            updateInstituteList();
        });

        // Bind change event on status select
        $('#status').on('change', function() {
            updateInstituteList();
        });

        // Handle pagination clicks
        $(document).on('click', '#paginationControls a', function(e) {
            e.preventDefault();
            var offsetValue = $(this).data('offset');
            updateInstituteList(offsetValue);
        });

        // Initial load
         updateInstituteList(offset); // Uncomment if you want to load the institutes via AJAX on page load

    });
</script>
