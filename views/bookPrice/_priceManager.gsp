<style>
    .priceListTable tr th,
    .priceListTable tr td{
        padding: 10px;
    }
    .priceListTable tr td > *{
        width: 100%;
    }
    .add-btn,
    .delete-btn{
        display: block;
        text-align: center;
        padding: 5px 5px;
        border-radius: 5px;
        color: rgba(0,0,0,0.6);
    }
    .priceCusIcons{
        width: 25px;
    }
    .add-btn{
        border-color: rgba(0,0,0,0.5);
    }
    .delete-btn{
        color: red;
    }
    .priceListTable tr td select,
    .priceListTable tr td input{
        width: 135px;
        padding: 4px;
        border-radius: 5px;
        border: 1px solid rgba(0,0,0,0.5);
        text-align: center;
        font-size: 14px;
    }
    .priceListTable tr td select:focus-visible,
    .priceListTable tr td input:focus-visible{
        outline: 1px solid rgba(0,0,0,0.5);
    }
    .priceListForm{
        overflow-x: scroll;
    }
    .priceListTable tbody{
        text-align: center;
    }
    .priceListTable tbody tr:hover {
        background-color: transparent;
    }

    @media (max-width: 768px){

    }
</style>

<script>
    var bookTypes = [];
    var priceBookId;
    <% bookTypes.each{ bookType ->%>
    bookTypes.push("${bookType.bookType}");
    <% }%>
    function manageBookPrices(bookId){
        $('.loading-icon').removeClass('hidden');
        priceBookId = bookId;
        <g:remoteFunction controller="bookPrice" action="getBookAllPrices" params="'bookId='+bookId" onSuccess="receivedPrices(data)"></g:remoteFunction>
    }

    function receivedPrices(data){

        var priceTable="<table border='1' class='w-100 priceListTable table table-sm table-bordered table-hover dataTable '>\n" +
            "                            <tr style='background-color: rgba(0,0,0,.075);'>\n" +
            "                            <th>Book type</th>\n" +
            "                            <th>List price</th>\n" +
            "                            <th>Sell price</th>\n" +
            "                            <th>Currency</th>\n" +
            "                            <th>Free Tokens</th>\n" +
            "                            <th></th>\n" +
            "                            <th></th>\n" +
            "                            </tr>";
        var bookPrices = data.bookPrices;
        var remainingBookTypes= bookTypes.slice();
        for(var i=0;i<bookPrices.length;i++){
            var index = remainingBookTypes.indexOf(bookPrices[i].bookType);
            priceTable +="<tr>\n" +
                "                                <td>"+bookPrices[i].bookType+"</td>\n" +
                "                                <input type='hidden' id='bookType_"+bookPrices[i].id+"' value='"+bookPrices[i].bookType+"'>"+
                "                                <td><input type=\"number\" id=\"listPrice_"+bookPrices[i].id+"\" name=\"listPrice_"+bookPrices[i].id+"\" step=\"0.01\" value='"+bookPrices[i].listPrice+"' placeholder='List Price'></td>\n" +
                "                                <td><input type=\"number\" id=\"sellPrice_"+bookPrices[i].id+"\" name=\"sellPrice_"+bookPrices[i].id+"\" step=\"0.01\" value='"+bookPrices[i].sellPrice+"' placeholder='Sell Price' required></td>\n" +
                "                                <td>"+bookPrices[i].currencyCd+"</td>\n" +
                "                                <input type='hidden' id='currencyCd_"+bookPrices[i].id+"' value='"+bookPrices[i].currencyCd+"'>"+
                "                                 <td><input type=\"number\" id=\"freeChatTokens_"+bookPrices[i].id+"\" name=\"freeChatTokens_"+bookPrices[i].id+"\" step=\"0.01\" value='"+bookPrices[i].freeChatTokens+"' placeholder='Sell Price' required></td>\n" +
                "                               <td><a href=\"javascript:priceUpdate("+bookPrices[i].id+")\" class='add-btn'>" +
                "                                   <img src='${assetPath(src: 'ws/editicon.svg')}' class='priceCusIcons''>"+
                "                                 </a></td>\n" +
                "                                <td><a href=\"javascript:priceDelete("+bookPrices[i].id+")\" class='delete-btn'>" +
                "                                   <img src='${assetPath(src: 'ws/delete.svg')}' class='priceCusIcons'>"+
                "                                   </a></td>\n" +
                "                            </tr>";

            if (index !== -1) {
                remainingBookTypes.splice(index, 1);
            }
        }


        priceTable +="<tr>\n" +
            "                                <td><select id=\"bookType_add_"+priceBookId+"\">\n" +
            "                                </select></td>\n" +
            "                                <td><input type=\"number\" id=\"listPrice_add_"+priceBookId+"\" name=\"listPrice__add_"+priceBookId+"\" step=\"0.01\" placeholder='List Price'></td>\n" +
            "                                <td><input type=\"number\" id=\"sellPrice_add_"+priceBookId+"\" name=\"sellPrice__add_"+priceBookId+"\" step=\"0.01\" placeholder='Sell Price' required></td>\n" +
            "                                <td><select id=\"currencyCd_add_"+priceBookId+"\">\n" +
            "                                    <option>Select Currency</option>\n" +
            "                                    <option value=\"INR\">INR</option>\n" +
            "                                    <option value=\"USD\">USD</option>\n" +
            "                                </select></td>\n" +
            "                                <td><input type=\"number\" id=\"freeChatTokens_"+priceBookId+"\" name=\"freeChatTokens_"+priceBookId+"\"  placeholder='Free Tokens'></td>\n" +
            "                                <td><a href=\"javascript:priceAdd("+priceBookId+")\" class='add-btn'>" +
            " <img src='${assetPath(src: 'ws/add.svg')}' class='priceCusIcons'>"+
            "</a></td>\n" +
            "                                <td>" +
            "                            </tr>";

        priceTable +="</table>";
        document.getElementById("pricesList").innerHTML=priceTable;
        var select;
        select = document.getElementById("bookType_add_"+priceBookId);
        select.options.length = 1;
        for (var i = 0; i < remainingBookTypes.length; i++) {
            var el = document.createElement("option");
            el.textContent = remainingBookTypes[i];
            el.value = remainingBookTypes[i];

            select.appendChild(el);
        }
        $('.loading-icon').addClass('hidden');


        $('#priceModal').modal('show');
    }

    function priceUpdate(bookPriceId){
        var bookType = document.getElementById("bookType_"+bookPriceId);
        var sellPrice = document.getElementById("sellPrice_"+bookPriceId);
        var listPrice = document.getElementById("listPrice_"+bookPriceId);
        var currencyCd = document.getElementById("currencyCd_"+bookPriceId);
        var freeChatTokens = document.getElementById("freeChatTokens_"+bookPriceId).value;

        if(!sellPrice.value){
            alert("Sell price cannot be empty.");
            sellPrice.focus();
        }
        else{
            <g:remoteFunction controller="bookPrice" action="updateBookPrice" params="'bookId='+priceBookId+'&bookPriceId='+bookPriceId+'&sellPrice='+sellPrice.value+'&listPrice='+listPrice.value+'&freeChatTokens='+freeChatTokens" onSuccess="priceEdited(data)"></g:remoteFunction>
        }
    }

    function priceEdited(data){
        alert("Price succesfully edited");
        receivedPrices(data);
    }
    function priceAdd(bookPriceId){
         var bookType = document.getElementById("bookType_add_"+bookPriceId);
        var sellPrice = document.getElementById("sellPrice_add_"+bookPriceId);
        var listPrice = document.getElementById("listPrice_add_"+bookPriceId);
        var currencyCd = document.getElementById("currencyCd_add_"+bookPriceId);
        var freeChatTokens = document.getElementById("freeChatTokens_"+bookPriceId).value;

        if(!sellPrice.value){
            alert("Sell price cannot be empty.");
            sellPrice.focus();
        }
        else if(!bookType.value){
            alert("Please select book type.");
            bookType.focus();
        }
        else if(currencyCd.selectedIndex==0){
            alert("Please select currency type.");
            currencyCd.focus();
        }
        else{
            <g:remoteFunction controller="bookPrice" action="addBookPrice" params="'bookId='+bookPriceId+'&bookType='+bookType.value+'&sellPrice='+sellPrice.value+'&listPrice='+listPrice.value+'&currencyCd='+currencyCd.value+'&freeChatTokens='+freeChatTokens" onSuccess="receivedPrices(data)"></g:remoteFunction>
        }
    }

    function priceDelete(bookPriceId){
        if(confirm("Are you sure to delete this price?")){
            <g:remoteFunction controller="bookPrice" action="deleteBookPrice" params="'bookPriceId='+bookPriceId" onSuccess="receivedPrices(data)"></g:remoteFunction>

        }
    }
</script>

<div class="modal fade" id="priceModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body p-4">
                <button type="button" class="close" data-dismiss="modal">X</button>
                <div class="modal-form-content">
                    <h4 class="modal-title head-title">Price Manager</h4>
                    <form class="mt-3 mb-4 text-center priceListForm" method="post">
                        <div id="pricesList">

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
