<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/5.0.0/normalize.min.css">
<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<link rel="stylesheet" id="webmcqStyle" href="/assets/prepJoy/prepjoyWebsites/leaderBoard.css">

<%if("true".equals(session['prepjoySite'])){%>
    <style>
        body {
            background: #04001D !important;
        }
        footer {
            display: block !important;
        }
        .leaderboard-admin {
            margin-top: 10rem;
            display: block;
        }
        .leaderboard-admin h2, .leaderboard-admin b {
            color: #FFF !important;
        }
        .leaderboard-admin .btn-primary-modifier {
            border-color: #E83500 !important;
            background-color: #E83500 !important;
        }
        .ui-datepicker thead tr {
            background-color: transparent !important;
        }
    </style>
<%}%>
<!-------- LOADER --------->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<scetion class="leaderboard-admin">
<div class="container mb-4">
    <div class="daily__test-header mt-5">
        <div class="daily__test-header__title d-flex align-items-center">
            <h3>
                <strong>Leaderboard</strong>
            </h3>
        </div>
    </div>

</div>

<div class="container">


    <div class="col-md-5 d-flex align-items-center px-0">
        <select name="institutes" id="institutes" class="form-control admin" style=" ">
            <option value="">All India Ranking</option>
            <g:each in="${institutes}" var="institute" status="i">
                <option value="${institute.id}">${institute.name}</option>
            </g:each>
        </select>
        <div class="custom__date-wrapper mb-0 ml-3">
            <input type="text" class="datepicker bg-white p-1"  maxlength="10" id="datepicker" placeholder="DD-MM-YYYY">
        </div>
    </div>
    <p class="text-danger"  id="dateError" style="display: none">Please Enter a valid date (DD-MM-YYYY)</p>
    <div class="form-group mt-3">
        <a class="btn btn-lg btn-primary btn-primary-modifier mb-3" href="javascript:getRanks();">Get Ranks</a>
    </div>
</div>

<div class="container mb-5" id="nextList">

</div>

</scetion>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script>

    var currentDate = new Date().toISOString().split("T")[0];
    document.querySelector('.datepicker').value = currentDate.split('-').reverse().join('-');

    $('.datepicker').datepicker({
        dateFormat: 'dd-mm-yy',
        autoclose:true,
        endDate: "today",
        maxDate:'0'
    }).on('changeDate', function (ev) {
        $(this).datepicker('hide');
    });
    $('.datepicker').on('change',function (e){
        currentDate = e.target.value;
        currentDate = currentDate.split('-').reverse().join('-');
            currentDate = e.target.value.split('-').reverse().join('-')


    })

    var wage = document.getElementById("datepicker");

    wage.addEventListener("keydown", function (e) {
        if (e.code === "Enter") {
            var valid = moment(e.target.value, 'DD-MM-YYYY',true).isValid();
            if(valid){
                currentDate = e.target.value.split('-').reverse().join('-');

            }
        }
    });
    function getRanks(){
        $('.loading-icon').removeClass('hidden');
        if(document.getElementById("institutes").selectedIndex==0) {
            <g:remoteFunction controller="analytics" action="getLeaderBoardForAdmins" params="'rankDate='+currentDate+'&siteId=${session["siteId"]}'" onSuccess="dailyRankUI(data)" />
        }else {
            var instituteId = document.getElementById("institutes")[document.getElementById("institutes").selectedIndex].value;
            <g:remoteFunction controller="analytics" action="getLeaderBoardForAdmins" params="'rankDate='+currentDate+'&siteId=${session["siteId"]}&instituteId='+instituteId" onSuccess="dailyRankUI(data)" />
        }
    }




    function dailyRankUI(data){
        $('.loading-icon').addClass('hidden');
        if (data.dailyRanks !=='No Ranks'&&data.dailyRanks!=='null'&&data.dailyRanks!=null){
            var leaderBoardData = data.dailyRanks;
            var nextLeaders = [];
            var lbHtml = "";
            var tHtml = "";

            for(var n=0;n<leaderBoardData.length;n++){
                    nextLeaders.push(leaderBoardData[n]);
            }

            for (var i=0;i<nextLeaders.length;i++){

                lbHtml +="<div class='d-flex align-items-center lb-wrapper mb-3'>\n"+
                    "<div class='d-flex align-items-center t-position'>\n"+
                    "<h3>#</h3>\n"+
                    "<h3>"+nextLeaders[i].rank+"</h3>\n"+
                    "</div>";
                lbHtml += "<div class='lb-content'>\n"+
                    "<div class='lb-content__img-wrapper'>";
                if(nextLeaders[i].profilePic !=null && nextLeaders[i].profilePic !="" && nextLeaders[i].profilePic!=undefined){
                    lbHtml +="<img src='/funlearn/showProfileImage?id="+nextLeaders[i].userId+"&fileName="+nextLeaders[i].profilePic+"&type=user&imgType=passport'>";
                }else{
                    lbHtml +="<img src='/assets/landingpageImages/img_avatar3.png'>";
                }
                lbHtml +=  "</div>";

                lbHtml += "<div class='lb-content__list'>";
                lbHtml +="<div>\n"+
                    "<h3>"+nextLeaders[i].name+" - "+nextLeaders[i].username+"</h3>";
                if (nextLeaders[i].state !=undefined && nextLeaders[i].state !=null && nextLeaders[i].state !=""){
                    lbHtml +=  "<p>"+nextLeaders[i].state+"</p>";
                }
                lbHtml += "</div>";
                lbHtml += "<div class='lb-content__list-votes text-center'>\n"+
                    "<p>"+nextLeaders[i].userPoints+"</p>\n"+
                    "<p>Points</p>\n"+
                    "</div>";
                lbHtml+="</div>";
                lbHtml+=" </div>";
                lbHtml+=" </div>";

            }
            //weekly ranks
            leaderBoardData = data.weeklyRanks;
            nextLeaders = [];

            for(var n=0;n<leaderBoardData.length;n++){
                nextLeaders.push(leaderBoardData[n]);
            }

            lbHtml +="<br> <b>Weekly Leaderboard</b><br>";
            for (var i=0;i<nextLeaders.length;i++){

                lbHtml +="<div class='d-flex align-items-center lb-wrapper mb-3'>\n"+
                    "<div class='d-flex align-items-center t-position'>\n"+
                    "<h3>#</h3>\n"+
                    "<h3>"+nextLeaders[i].rank+"</h3>\n"+
                    "</div>";
                lbHtml += "<div class='lb-content'>\n"+
                    "<div class='lb-content__img-wrapper'>";
                if(nextLeaders[i].profilePic !=null && nextLeaders[i].profilePic !="" && nextLeaders[i].profilePic!=undefined){
                    lbHtml +="<img src='/funlearn/showProfileImage?id="+nextLeaders[i].userId+"&fileName="+nextLeaders[i].profilePic+"&type=user&imgType=passport'>";
                }else{
                    lbHtml +="<img src='/assets/landingpageImages/img_avatar3.png'>";
                }
                lbHtml +=  "</div>";

                lbHtml += "<div class='lb-content__list'>";
                lbHtml +="<div>\n"+
                    "<h3>"+nextLeaders[i].name+" - "+nextLeaders[i].username+"</h3>";
                if (nextLeaders[i].state !=undefined && nextLeaders[i].state !=null && nextLeaders[i].state !=""){
                    lbHtml +=  "<p>"+nextLeaders[i].state+"</p>";
                }
                lbHtml += "</div>";
                lbHtml += "<div class='lb-content__list-votes text-center'>\n"+
                    "<p>"+nextLeaders[i].userPoints+"</p>\n"+
                    "<p>Points</p>\n"+
                    "</div>";
                lbHtml+="</div>";
                lbHtml+=" </div>";
                lbHtml+=" </div>";

            }

//weekly ranks
            leaderBoardData = data.monthlyRanks;
            nextLeaders = [];

            for(var n=0;n<leaderBoardData.length;n++){
                nextLeaders.push(leaderBoardData[n]);
            }

            lbHtml +="<br> <b>Monthly Leaderboard</b><br>";
            for (var i=0;i<nextLeaders.length;i++){

                lbHtml +="<div class='d-flex align-items-center lb-wrapper mb-3'>\n"+
                    "<div class='d-flex align-items-center t-position'>\n"+
                    "<h3>#</h3>\n"+
                    "<h3>"+nextLeaders[i].rank+"</h3>\n"+
                    "</div>";
                lbHtml += "<div class='lb-content'>\n"+
                    "<div class='lb-content__img-wrapper'>";
                if(nextLeaders[i].profilePic !=null && nextLeaders[i].profilePic !="" && nextLeaders[i].profilePic!=undefined){
                    lbHtml +="<img src='/funlearn/showProfileImage?id="+nextLeaders[i].userId+"&fileName="+nextLeaders[i].profilePic+"&type=user&imgType=passport'>";
                }else{
                    lbHtml +="<img src='/assets/landingpageImages/img_avatar3.png'>";
                }
                lbHtml +=  "</div>";

                lbHtml += "<div class='lb-content__list'>";
                lbHtml +="<div>\n"+
                    "<h3>"+nextLeaders[i].name+" - "+nextLeaders[i].username+"</h3>";
                if (nextLeaders[i].state !=undefined && nextLeaders[i].state !=null && nextLeaders[i].state !=""){
                    lbHtml +=  "<p>"+nextLeaders[i].state+"</p>";
                }
                lbHtml += "</div>";
                lbHtml += "<div class='lb-content__list-votes text-center'>\n"+
                    "<p>"+nextLeaders[i].userPoints+"</p>\n"+
                    "<p>Points</p>\n"+
                    "</div>";
                lbHtml+="</div>";
                lbHtml+=" </div>";
                lbHtml+=" </div>";

            }

            document.querySelector('#nextList').innerHTML = lbHtml;

        }else{
            var tHtml = "";
            tHtml +="<div class='d-flex flex-column justify-content-center align-items-center'><h2>Not many leaders?</h2>\n"+
                "<p>Play and top the chart.</p></div>";
            document.querySelector('#nextList').innerHTML = tHtml;;

        }

        $("#item-1").addClass('one');
        $("#item-2").addClass('two');
        $("#item-3").addClass('three');
    }

    $( window ).on( "load", function (){
        $('.loading-icon').removeClass('hidden');
    });
    $( document ).ready(function() {
        $('.loading-icon').addClass('hidden');
    });
</script>
</body>
</html>
