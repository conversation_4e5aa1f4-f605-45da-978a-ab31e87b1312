<%  String siteName = grailsApplication.config.grails.appServer.siteName; %>
<style>
    .g-signin2{
        background: url('../../images/landingpageImages/ic_google.svg') no-repeat;
        background-position: 14px;
        font-size: 16px;
        color: #444444;
        font-family: 'Rubik', sans-serif;
        border: none;
        background-color: #ffffff;
        width: 296px;
        height: 48px;
        box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
        border-radius: 4px;
        position: relative;
        z-index: 999;
    }
</style>
<div class="modal fade" id="loginSignup" data-page="login-google" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <div class="login-back-btn">
                    <a id="back-btn"><i class="material-icons">arrow_back</i></a>
                </div>
                <h4 class="login">Login</h4>
                <h4 class="signup">Sign Up</h4>
            <h4 class="reset">Reset Password</h4>
            <h4 class="otp-header">OTP Verification</h4>
                <button type="button" class="close"  id="closeSignupModal"><i class="material-icons">close</i> </button>
            </div>
            <!-- Modal body -->
            <div class="modal-body text-center mx-auto">
                <div class="sign-content">
                    <div class="signup-bg"></div>
                    <h1>Welcome to <%= siteName%></h1>
                    <p class="mt-3">Shop & Read <br> next generation eBooks</p>
                       <button type="button" class="sign-google mt-4" id="googleSignInButton" onclick="googleSignInCalled();">Sign in with Google</button>
                    <p class="mt-4">or use email address</p>

                    <div class="mt-3 button-wrapper">
                        <a id="login" class="login">Login</a>
                        <a id="sign-up" class="signup">Sign Up</a>
                        <div id="loginFailed" style="display:none; color: #F05A2A; margin-top: 15px;">Login failed. Please try again! <%=cookie(name:'SimulError')=='Fail'?"Sorry, you have already signed in from  ["+grailsApplication.config.grails.appServer.maximumSessions+"] devices. To login here, please sign out from other device!":""%></div>
                    </div>
                </div>
                <div class="login-content">
                    <form method="post" action="/login/authenticate">
                        <span class="input-login">
                            <input class="input-field input-field-login" type="text" id="email" name="username" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="email">
                                <span class="input-label-content input-label-content-login">Email</span>
                            </label>
                        </span>
                        <span class="input-login">
                            <input class="input-field input-field-login" type="password" id="password" name="password" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="password">
                                <span class="input-label-content input-label-content-login">Password</span>
                            </label>
                        </span>                        
                        <input type="submit" id="sign-in" onclick="javascript:userSignIn();" class="btn btn-lg continue mt-2" value="Continue">
                        <a id="forgot-paswd" class="forgot-password">Forgot Password ?</a>
                    </form>
                    <div class="email-error" id="signInError" style="display: none;">
                        <p class="email-error-text">All fields are mandatory.</p>
                    </div>
                </div>
                <div class="signup-content">
                    <g:form name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off">
                        <span class="input-login">
                            <input class="input-field input-field-login" type="text" id="name" name="name" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="name">
                                <span class="input-label-content input-label-content-login">Name</span>
                            </label>
                        </span>
                        <span class="input-login">
                            <input class="input-field input-field-login mobile-input" type="text" name="mobile" id="mobile" required maxlength="10" minlength="10">
                            <label class="input-label input-label-login input-label-login-color-1" for="mobile">
                                <span class="input-label-content input-label-content-login">Phone Number</span>
                            </label>
                        </span>
                           <div class="input-error-tooltip" style="display: none;">
                               <div class="input-error-tooltip-inner">Please enter 10 digit mobile number.</div>
                           </div>
                        <span class="input-login">
                            <input class="input-field input-field-login" type="text" id="username" name="username" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="username">
                                <span class="input-label-content input-label-content-login">Email</span>
                            </label>
                        </span>
                        <div class="email-error" id="emailexists" style="display: none;">
                            <p class="email-error-text">This email is already taken. Please sign in.</p>
                        </div>
                        <span class="input-login">
                            <input class="input-field input-field-login" type="password" id="signup-password" name="password" required>
                            <label class="input-label input-label-login input-label-login-color-1" for="signup-password">
                                <span class="input-label-content input-label-content-login">Password</span>
                            </label>
                        </span>
                        <span class="selectbox">
                            <select name="state" id="state" required>
                            <option value="">State</option>
                            <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                            <option value="Andhra Pradesh">Andhra Pradesh</option>
                            <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                            <option value="Assam">Assam</option>
                            <option value="Bihar">Bihar</option>
                            <option value="Chandigarh">Chandigarh</option>
                            <option value="Chhattisgarh">Chhattisgarh</option>
                            <option value="Dadra and Nagar Haveli">Dadra and Nagar Haveli</option>
                            <option value="Goa">Goa</option>
                            <option value="Gujarat">Gujarat</option>
                            <option value="Haryana">Haryana</option>
                            <option value="Himachal">Himachal Pradesh</option>
                            <option value="Jammu & Kashmir">Jammu & Kashmir</option>
                            <option value="Jharkhand">Jharkhand</option>
                            <option value="Karnataka">Karnataka</option>
                            <option value="Kerala">Kerala</option>
                            <option value="Ladakh">Ladakh</option>
                            <option value="Lakshadweep">Lakshadweep</option>
                            <option value="Madhya Pradesh">Madhya Pradesh</option>
                            <option value="Maharashtra">Maharashtra</option>
                            <option value="Manipur">Manipur</option>
                            <option value="Meghalaya">Meghalaya</option>
                            <option value="Mizoram">Mizoram</option>
                            <option value="Nagaland">Nagaland</option>
                            <option value="Odisha">Odisha</option>
                            <option value="Puducherry">Puducherry</option>
                            <option value="Punjab">Punjab</option>
                            <option value="Rajasthan">Rajasthan</option>
                            <option value="Sikkim">Sikkim</option>
                            <option value="Tamil Nadu">Tamil Nadu</option>
                            <option value="Telangana">Telangana</option>
                            <option value="The Government of NCT of Delhi">The Government of NCT of Delhi</option>
                            <option value="Tripura">Tripura</option>
                            <option value="Uttarakhand">Uttarakhand</option>
                            <option value="Uttar Pradesh">Uttar Pradesh</option>
                            <option value="West Bengal">West Bengal</option>
                        </select>
                    </span>
                   <input type="hidden" name="email">
                   <input type="hidden" name="otp_finished" id="otp_finished">
                   <input type="button" id="signup" onclick="javascript:formSubmit();" class="btn btn-lg continue mt-2" value="Continue">
                </g:form>
                </div>
                <div class="reset-password">
                <p>To recieve a link to reset your password, please enter your <%=siteName%> account email address.</p>
                <g:form name="forgotpassword" class="form-horizontal mt-4" method="post" autocomplete="off">
                    <span class="input-login">
                        <input class="input-field input-field-login" type="text"  name="username" id="fPemail" required>
                        <label class="input-label input-label-login input-label-login-color-1" for="email">
                            <span class="input-label-content input-label-content-login">Email</span>
                        </label>
                    </span>
                        <input type="button" id="fPbtn" onclick="javascript:formFPSubmit();" class="btn btn-lg reset mt-2" value="Send reset link">
                </g:form>
            </div>
            <div id="reset-password-completed">
                    <p>Password reset link sent.</p>
                    <p>We’ve sent instructions on how to reset your password to <span id="fp-user-email"></span>. If you haven’t received an email from  <%=siteName%> within a couple of minutes, please check your spam folder.</p>
                <a id="back-login"><i class="material-icons">arrow_back</i>Back to Login</a>
            </div>
            <div id="reset-google-paswd">
                <p>Password reset not possible!</p>
                 <p>We see that you have registered using Google, so it will not possible to change the password for your account (<span id="fp-user-email1"></span>) with us. <br><br>Kindly continue to login using Google</p>
                <a id="back-login"><i class="material-icons">arrow_back</i>Back to Login</a>
            </div>
            <div id="account-exists">
                <p class="notRegister">This Email is not registered.<br> Please signup!</p>
                <a id="back-signup"><i class="material-icons">arrow_back</i>Back to Signup</a>
            </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <p class="terms">By continuing you agree to our &nbsp;<a href="/smartebook/termsandconditions">Terms and Conditions.</a></p>
            </div>

        </div>
    </div>
</div>

<asset:javascript src="jquery-1.11.2.min.js"/>

<script>
    var otpReg = "${otpReg}";

    var flds = new Array (
        'name',
        'username',
        'signup-password',
        'mobile',
        'state'
    );

    function userSignIn() {
        if($('#email').val()=="" || $('#password').val()=="") {
            $('#loginSignup').modal('show');
            $('#signInError').show();
        } else {
            $('#sign-in-div').hide();
            $('#connecting-div').show();
        }
    }

    function formSubmit() {
        if (validateSignUp()) {
            document.adduser.username.value = document.adduser.username.value.toLowerCase();
            document.adduser.email.value = document.adduser.username.value.toLowerCase();
          checkUsernameExists();
        }
    }

    function validateSignUp(){
         var allFilled=true;
        document.getElementById('emailexists').style.display = 'none';

        for (i=0; i<flds.length; i++) {
            if( !$("#"+flds[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                allFilled = false;
            } else {
                $("#"+flds[i]).css('border', 'none');
                $("#"+flds[i]).css('border', 'none');
            }
        }

        if(!allFilled) {
            $('.alert').show();
        } else {
            var email = $("#username").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                $("#username").css('border', '1px solid rgba(240, 90, 40, 0.5)');
                return false;
            }
        }

        return allFilled;
    }

    function checkUsernameExists() {
        var username = $("#username").val();
        <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);' params="'username='+username" />
    }

    function userNameExistsResult(data) {
        if(data=="0") {
            $('#connecting-div').show();

            if(otpReg=="true") {
                $('#loginSignup').modal('hide');
                $('#otp-mobile').val($('#mobile').val());
                $('#otp-email-txt').text($('#username').val());
                $('#otp-email-txt1').text($('#username').val());
                $('#otp-mobile1').text($('#otp-mobile').val());
                $('#otp-next').modal('show');
            } else {
                document.adduser.submit();
            }

            $('#sign-up-div').hide();
        } else {
            $('#loginSignup').modal('show');
            document.getElementById('emailexists').style.display = 'block';
        }
    }

    function formFPSubmit() {
        $("#emailidnf").hide();

        if( !$("#fPemail").val() ) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        } else {
            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                return false;
            } else {
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
            }
        }
    }

    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if("OK"==data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-completed');
            $('#fp-user-email').html("“"+userEmail+"”");
        } else if("Google"==data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-google-paswd');
            $('#fp-user-email1').html("“"+userEmail+"”");
        }
        else if("Fail"==data.status){
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'account-exist');
        }
    }

    <%  if(params.loginFailed=="true" && "12"!=""+session["siteId"]) {%>
    $(window).on('load',function() {
        $('#loginSignup').modal('show');
    });
    <%  } %>


    $('#back-signup').on('click',function () {
        $('#loginSignup').modal('show');
        $('#loginSignup').attr('data-page', 'signup');
    });
    $('#closeSignupModal').on('click',function () {
        $('#loginSignup').modal('hide');
        $('#loginSignup').attr('data-page', 'login-google');
    });
</script>
