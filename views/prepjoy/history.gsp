<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="prepJoy/prepjoyWebsites/analyticsStyles.css" async="true" media="all"/>


<!-------- LOADER --------->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="container an-header">
    <div class="daily__test-header mt-2 mt-lg-5">
        <div class="daily__test-header__title d-flex align-items-center">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.close()">keyboard_backspace</button>
            <h3 class="text-primary-modifier">
                <strong>History</strong>
            </h3>
        </div>
        <div id="history__header-title" class="text-center">

        </div>
        <div id="history__header-content" class="text-center mt-5 d-none">
            <div class="d-flex ds">
                <h6>Date</h6>
                <h6>Score</h6>
            </div>
        </div>
    </div>
</div>

<div class="container listContainer">
    <div id="historyList"></div>
    <button class="btn btn-primary d-none justify-content-center align-items-center" style="margin: 0 auto" onclick="paginate()" id="moreBtn">Load More</button>
</div>

<div class="hidden-footer">
<g:render template="/${session['entryController']}/footer_new"></g:render>
</div>
<script>
    var urlParams = new URL(window.location.href);
    var siteId = urlParams.searchParams.get('siteId');
    var resId = urlParams.searchParams.get('resId');
    var quizType = urlParams.searchParams.get('quizType');
    var allFetchedData;
    var pageNo = 10;

    function getUsersHistoryForAQuiz(){
        showLoader();
        <g:remoteFunction controller="analytics" action="getUsersHistoryForAQuiz" params="'siteId='+siteId+'&resId='+resId+'&quizType='+quizType" onSuccess="showUserHistoryUI(data)" />
    }
    getUsersHistoryForAQuiz();
    function showUserHistoryUI(data){
        allFetchedData = data;
        if (data.lastQuizDetails != 'no records'){
            var paginatedArr = JSON.parse(data.lastQuizDetails);
        }else {
            var paginatedArr = [];
        }
        if (paginatedArr!=null){
            var userHistoryArr = paginatedArr.splice(0,pageNo);
        }else{
            var userHistoryArr = [];
        }
        var uHtml = "";
        if (userHistoryArr.length>0){
            document.getElementById('history__header-title').innerHTML = "<h4>"+userHistoryArr[0].quizName+"</h4>";
            document.getElementById('moreBtn').classList.remove('d-none');
            document.getElementById('moreBtn').classList.add('d-flex');

            for (var u=0;u<userHistoryArr.length;u++){
                var [month,date,year] = userHistoryArr[u].dateCreated.split(" ");
                var formattedDate = [month,date,year].join(" ");
                var dateNm = date.replace(',','');

        uHtml += "<div class='history__card' id='history__card-"+(u+1)+"' onclick='getHistoryData("+userHistoryArr[u].quizRecId+")'>\n"+
                    "<div class='history__card-wrap d-flex align-items-center justify-content-between'>\n"+
                        "<div class='datesDiv'>\n"+
                            "<p>"+month+"</p>"+
                            "<p class='dateDigit'>"+dateNm+"</p>\n"+
                            "<p>"+year+"</p>\n"+
                        "</div>\n"+
                        "<div class='pointsSummary'>"+
                            "<p class='correct-ans points-details-card'><span class='mr-2'>Correct :</span>"+userHistoryArr[u].correctAnsCount+"</p>"+
                            "<p class='incorrect-ans points-details-card mr-2'><span class='mr-2'>In-Correct :</span>"+userHistoryArr[u].inCorrectAnsCount+"</p>"+
                            "<p class='skipped-ans points-details-card'><span class='mr-2'> Skipped :</span>"+userHistoryArr[u].skippedAnsCount+"</p>"+
                            "<p class='total-points mr-2'><span class='mr-2'>Total Points :</span><span style='font-weight: 600'>"+userHistoryArr[u].points+"</span></p>"+
                        "</div>"+
                        "<div class='d-flex align-items-center justify-content-end'>\n"+
                            "<button class='btn getHistoryData-btn' style='font-size: 18px' ><i class='material-icons-round'>chevron_right</i></button>\n"+
                        "</div>\n"+
                    "</div>\n"+
                "</div>";
            }
        }else{
            uHtml+="<h4 class='text-center mt-5'>No History Found.</h4>";
        }

        document.getElementById('historyList').innerHTML = uHtml;
        hideLoader();

        var targetElm = document.querySelector('#history__card-'+(pageNo-9));
        targetElm.scrollIntoView();

        if (paginatedArr.length == 0){
            document.getElementById('moreBtn').classList.add('d-none');
            document.getElementById('moreBtn').classList.remove('d-flex');
        }
    }


    function getHistoryData(id){
        window.open('/prepjoy/prepJoyGame?siteId='+siteId+'&quizRecId='+id+'&historyPage=true&learn=false','_blank');
    }

    function paginate(){
        pageNo+=10;
        showUserHistoryUI(allFetchedData);
    }

    function showLoader(){
        document.querySelector('.loading-icon').classList.remove('hidden');
    }

    function hideLoader(){
        document.querySelector('.loading-icon').classList.add('hidden')
    }

</script>