<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>
.table-bordered th,td {
    padding: 10px;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Find Scratch Code Usage</h3>
                    <div class="form-group table-responsive" id="intrst-area">
                        <form id="searchForm" class="mb-4">
                            <div class="form-group">
                                <label for="scratchCode">Enter Scratch Code:</label>
                                <input type="text" class="form-control" id="scratchCode" name="scratchCode" required>
                            </div>
                            <input type="hidden" name="mode" value="submit">
                            <input type="submit" class="btn btn-primary" value="Search">
                        </form>
                        
                        <div id="results">
                            <!-- Results will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    $(document).ready(function() {
        $("#searchForm").submit(function(event) {
            event.preventDefault(); // Prevent form submission
            document.getElementById("results").innerHTML="";
            var formData = $(this).serialize(); // Serialize form data
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: "${createLink(controller: 'support', action: 'getScratchCodeDetails')}",
                type: "post",
                data: formData,
                success: function(data) {
                    // Update results with the data
                    scratchCodeDataReceived(data);
                },
                error: function() {
                    $('.loading-icon').addClass('hidden');
                    alert("Failed to retrieve scratch code data.");
                }
            });
        });
    });
    
    function scratchCodeDataReceived(data) {
        $('.loading-icon').addClass('hidden');
        var resultsDiv = document.getElementById("results");
        
        if (data.status === "OK" && data.usageDetails && data.usageDetails.length > 0) {
            var html = '<h4>Scratch Code Usage Details</h4>';
            html += '<table class="table table-bordered table-striped">';
            html += '<thead><tr><th>User Name</th><th>Mobile</th><th>Book Title</th><th>Date Added</th></tr></thead>';
            html += '<tbody>';
            
            for (var i = 0; i < data.usageDetails.length; i++) {
                var detail = data.usageDetails[i];
                html += '<tr>';
                html += '<td>' + (detail.name || 'N/A') + '</td>';
                html += '<td>' + (detail.mobile || 'N/A') + '</td>';
                html += '<td>' + (detail.bookTitle || 'N/A') + '</td>';
                html += '<td>' + (detail.dateAdded || 'N/A') + '</td>';
                html += '</tr>';
            }
            
            html += '</tbody></table>';
            resultsDiv.innerHTML = html;
        } else {
            resultsDiv.innerHTML = '<div class="alert alert-info">No records found for the provided scratch code.</div>';
        }
    }
</script>

</body>
</html>
