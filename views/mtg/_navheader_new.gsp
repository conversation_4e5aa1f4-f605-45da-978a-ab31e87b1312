<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required meta tags -->
  <title><%= title!=null?title:"Publisher of Best Books for NEET, JEE, Olympiads and School Textbooks - MTG Learning Media\n" +
          "Exams"%></title>
  <%if(seoDesc!=null){%>
  <meta name="description" content="${seoDesc}">
  <%}else{%>
  <meta name="description" content="MTG Learning Media is a trusted name for creating quality books for the preparation of Olympiads, NEET, JEE Mains & Advanced, BITSAT, and Other Engineering Entrance Exams.Buy best school and competitive Exams books and online test series. Best way to prepare for your exams.">
  <%}%>

  <meta charset="utf-8">
  <%if(keywords!=null){%>
  <meta name="keywords" content="${keywords}"/>
  <%}else{%>
  <meta name="keywords" content="mtg books for neet, mtg books for jee, mtg books for class 10, mtg books for kcet, mtg books for neet 2024, mtg books for olympiad, mtg books for cet, mtg books for physics, mtg books for neet fingertips, mtg workbooks, mtg books class 9, mtg books for chemistry, mtg books for biology, mtg books for science olympiad, mtg books for maths olympiad, olympiad books for class 5, olympiad books for class 4, olympiad books for class 3, olympiad books for class 2, olympiad books for class 1, olympiad books for class 6, olympiad books for class 7, olympiad books for class 8, olympiad books for class 10, imo olympiad books, imo olympiad workbook, imo olympiad workbook class 1-12." />
  <%}%>
  <meta name="generator" content="MTGBooks" />
  <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="author" content="webmaster - MTG Books and Learning Pvt Ltd">
  <meta name="MTG Books and Learning Pvt Ltd" content="https://mtgbooks.com">
  <meta name="subject" content="Book Store Online : Buy Books Online from MTG Books Store">
  <meta name="keyphrase" content="mtg books for neet, mtg books for jee, mtg books for class 10, mtg books for kcet, mtg books for neet 2024, mtg books for olympiad, mtg books for cet, mtg books for physics, mtg books for neet fingertips, mtg workbooks, mtg books class 9, mtg books for chemistry, mtg books for biology, mtg books for science olympiad, mtg books for maths olympiad, olympiad books for class 5, olympiad books for class 4, olympiad books for class 3, olympiad books for class 2, olympiad books for class 1, olympiad books for class 6, olympiad books for class 7, olympiad books for class 8, olympiad books for class 10, imo olympiad books, imo olympiad workbook, imo olympiad workbook class 1-12.">
  <meta name="abstract" content="Find large collection of Best Books for NEET, JEE, Olympiads and School Textbooks.">
  <link rel="shortcut icon" href="${assetPath(src: 'mtg/favicon.ico')}" type="image/x-icon"/>
  <link rel="icon"  href="${assetPath(src: 'mtg/favicon.ico')}" type="image/x-icon">
  <link rel="android-touch-icon" href="${assetPath(src: 'mtg/favicon.ico')}"/>
  <link rel="windows-touch-icon" href="${assetPath(src: 'mtg/favicon.ico')}" />

  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" referrerpolicy="no-referrer" async/>
  <!-- Bootstrap CSS -->
  <asset:stylesheet href="arihant/style.css"/>
  <asset:stylesheet href="arihant/responsive.css" />
  <asset:stylesheet href="font-awesome.min.css"/>
  <asset:stylesheet href="arihant/animate.css"/>
  <asset:stylesheet href="landingpage/fonts/flaticon.css"/>
  <asset:stylesheet href="katex.min.css"/>

  <asset:stylesheet href="wonderslate/material.css" async="true"/>
  <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
  <asset:stylesheet href="wonderslate/headerws.css" async="true"/>
  <asset:stylesheet href="landingpage/slick.min.css" async="true"/>
  <asset:stylesheet href="landingpage/slick.theme.css" async="true"/>
  <asset:stylesheet href="landingpage/homepageStyle.css" async="true" data-role="baseline" />
  <asset:stylesheet href="mtg/mtgStyles.css" async="true"/>

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,700|Oswald:200,300,400,500,600,700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Playfair+Display:400,400i&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css?family=Fira+Sans:300,400,500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Rubik:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
  <asset:javascript src="jquery-1.11.2.min.js"/>
  <script src="/assets/katex.min.js"></script>
  <script src="/assets/auto-render.min.js"></script>
  <!-- General styles for admin & user pages -->
  <%if("true".equals(commonTemplate)){%>
  <asset:stylesheet href="mtg/mtgTemplate.css" async="true"/>
  <% }%>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-X349QPRYKG"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-X349QPRYKG'); //mtg GA code
    gtag('config', 'G-2Y9D9ELSN2');  //WS GA code
  </script>

</head>
<body class="arihant mtg_books ws-whitelabel">
<sec:ifNotLoggedIn>
  <g:render template="/books/signIn"></g:render>
</sec:ifNotLoggedIn>
<%if(params.tokenId==null&&session["appType"]==null){%>
<header>
<div id="wrapper" class="wrp-cart">



  <div class="menu-main-min-height">
    <div class="main-menu-wrp" id="nav">

      <div class="container-fluid">
        <div class="row">
          <div class="pl-3 posi-static-respons">
            <div class="user-menu-wrp">
              <div class="wrp-new-posi-changes-arihant">
                <a href="javascript:void(0)" class="menu-dots-img-wrp">
                  <img src="${assetPath(src: 'arihant/menu-dots.svg')}" class="menu-dots-img">
                  <img src="${assetPath(src: 'arihant/menu-close-menuss.png')}" class="menu-close-btn-menub" />
                </a>
                <div class="main-menu-wrp-arihant-big">
                  <div class="container-fluid">
                    <div class="row">
                      <div class="col-12 col-md-3 col-xl-2 background-white-main">
                        <div class="manage-logo-big-menu-arihant">
                          <img src="${assetPath(src: 'mtg/mtg-logo.png')}" alt="MTG Logo">
                        </div>
                        <div class="main-side-bar-side-menu-big-wrp-wala-arihant">
                          <ul class="this-is-side-wrp-ul-big-menu-arihant">
                            <li class="active-menuss"><a href="javascript:void(0)">Books</a></li>
                          </ul>
                          <ul class="this-is-side-wrp-ul-big-menu-arihant">
                          <sec:ifNotLoggedIn>
                            <li><a href="javascript:loginOpen()">My Account</a></li>
                          </sec:ifNotLoggedIn>
                            <sec:ifLoggedIn>
                              <li><a href="/creation/userProfile">My Account</a></li>
                              <li><a href="javascript:logout()">Logout</a></li>
                            </sec:ifLoggedIn>
                          </ul>

                        </div>
                      </div>
                      <div class="col-md-9 col-xl-10 arihant-big-menu-side-wrp">
                        <div class="book-wrapper-sec-part-main-menu-big">
                          <div class="row" id="topMenuItems">
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>
              </div>
              <div class="menu-overlay-big-menus"></div>

            </div>
            <div class="logo-white-small-wrp">
              <a href="/mtg/index" class="white-logo-anchor-white">
                <img src="${assetPath(src: 'mtg/mtg-logo.png')}" alt="MTG Logo"/>
              </a>
            </div>

            <div class="overlay-menu-close"></div>
            <div class="clearfix"></div>
          </div>

          <div class="d-flex justify-content-start global-search pr-0 pr-md-3 col">
            <form class="form-inline rounded rounded-modifier col-12 col-lg-10 col-xl-8 pl-0 px-md-3 mb-0">
              <input type="text" class="form-control form-control-modifier border-0 typeahead w-100" name="search" id="search-book-header" autocomplete="off" placeholder="Search title, subject, author, ISBN, language etc.">
              <button type="button" class="btn bg-transparent text-primary text-primary-modifier" onclick="submitSearchHeader()" id="search-btn-header"><i class="material-icons">search</i></button>
            </form>
          </div>

          <div class="pr-md-3">
            <div class="menu-wrp-all-users-com">
              <ul>
                <li class="link-menu-li d-none d-md-block"><a href="/mtg/store" class="menu-link-anchor"><span>Store</span></a></li>
                <li class="link-menu-li navbar_cart"><a href="/wsshop/cart" class="menu-link-anchor mobile_cart_icon"><img src="${assetPath(src: 'arihant/shopping-cart.png')}" alt="" title="" /><span class="hide_mobile">Cart</span> <span class="cart_count" id="navbarCartCount">0</span></a></li>
                <sec:ifNotLoggedIn>
                <li class="none-add-class-responsive"><a href="javascript:loginOpen()"><img src="${assetPath(src: 'arihant/user-login.png')}" alt="" title="" /><span class="hide_mobile">Login</span></a></li>
                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                 <li class="none-add-class-responsive d-none d-md-block"><a   href="/wsLibrary/myLibrary"><img src="${assetPath(src: 'arihant/user-login.png')}" alt="" title="" /><span class="hide_mobile">My Books</span></a></li>
                </sec:ifLoggedIn>
                <sec:ifAllGranted roles="ROLE_FINANCE">
                  <li class="header-menu-item d-none d-md-block">
                    <a href="/publishing-sales" class="header-menu-item-link">Sales</a>
                  </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_CLIENT_ORDER_MANAGER">
                  <li class="header-menu-item d-none d-md-block">
                    <a href="/excel/addBulkUsersAndBooks" class="header-menu-item-link">User Upload</a>
                  </li>
                  <li class="header-menu-item d-none d-md-block">
                    <a href="/reports/getBulkUsersAddedReportInput" class="header-menu-item-link">User Upload Report</a>
                  </li>
                </sec:ifAllGranted>
                <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
                  <li class="nav-item dropdown d-none d-md-block">
                    <a class="nav-link dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                      Publishing
                    </a>
                    <div class="dropdown-menu">
                      <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
                      <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tags</a>
                      <a class="dropdown-item" href="/wonderpublish/manageExams">Manage MCQs Templates</a>
                    </div>
                  </li>
                </sec:ifAllGranted>
                <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_NOTIFICATION,ROLE_USER_LOGIN_RESET_MANAGER,ROLE_APP_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER,ROLE_WEB_CHAT,ROLE_WS_EDITOR,ROLE_LIBRARY_ADMIN,ROLE_MASTER_LIBRARY_ADMIN,ROLE_CLIENT_ORDER_MANAGER">

                  <li class="nav-item dropdown d-none d-md-block">
                    <a class="nav-link dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                      Admin
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                      <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                        <a class="dropdown-item" href="/admin/priceList">Price List</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                        <a class="dropdown-item" href="/excel/fileUploader">File Uploader</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                        <a class="dropdown-item" href="/admin/discountManager">Discount Management</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_DELETE_USER">
                        <a class="dropdown-item" href="/admin/deleteuser">Delete User</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                        <a class="dropdown-item" href="/institute/admin">eClass+</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                        <a class="dropdown-item" href="/institute/libAdmin">Library Management</a>
                      </sec:ifAllGranted>
                      <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER">
                        <a class="dropdown-item" href="/reports/instituteReport">Institute reports</a>
                      </sec:ifAnyGranted>
                      <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                        <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
                      </sec:ifAnyGranted>
                      <sec:ifAllGranted roles="ROLE_PUBLISHER">
                        <a class="dropdown-item" href="/log/quizissues">Quiz Issues</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                        <a class="dropdown-item" href="/log/notification">Notification</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                        <a class="dropdown-item" href="/log/userManagement">Force Logout</a>
                        <a class="dropdown-item" href="/log/deleteUserBooks">User Books </a>
                      </sec:ifAllGranted>

                      <sec:ifAllGranted roles="ROLE_ACCESS_CONTROLL">
                        <a class="dropdown-item" href="/log/userAccess">User Access</a>
                      </sec:ifAllGranted>

                      <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                        <a class="dropdown-item" href="/wonderpublish/wseditor">WS Editor</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                        <a class="dropdown-item" href="/log/notificationManagement">Notification Management</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR">
                        <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
                      </sec:ifAllGranted>
                      <%if(session["userdetails"].publisherId!=null){%>
                      <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                        <a class="dropdown-item" href="/publisherManagement/publisherReport">Publisher Report</a>
                      </sec:ifAllGranted>
                      <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                        <a class="dropdown-item" href="/admin/accessCodeUsage">Access Code Report</a>
                      </sec:ifAllGranted>
                      <%}%>
                      <sec:ifAllGranted roles="ROLE_LIBRARY_ADMIN">
                        <a class="dropdown-item" href="/institute/userManagement">Library Management</a>
                        <a class="dropdown-item" href="/institute/manageInstitutePage">Institute Page Management</a>
                        <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
                      </sec:ifAllGranted>
                      <sec:ifAnyGranted roles="ROLE_BOOK_CREATOR,ROLE_DIGITAL_MARKETER">
                        <a class="dropdown-item" href="/wonderpublish/bannerManagement">Banner Management</a>
                      </sec:ifAnyGranted>
                      <sec:ifAnyGranted roles="ROLE_CLIENT_ORDER_MANAGER">
                        <a class="dropdown-item" href="/wsshop/orderManagement">Order Management</a>
                        <a class="dropdown-item" href="/log/deleteUserBooks">User Books </a>
                      </sec:ifAnyGranted>
                    </div>
                  </li>
                </sec:ifAnyGranted>
                <div class="clearfix"></div>
              </ul>
              <div class="clearfix"></div>
            </div>
          </div>

        </div><!--row End-->
        <div class="row search-item-rows">
          <div class="this-div-use-box-search">
            <div class="search-view-bar-arihant">
              <form class="form-search-box-arihant">
                <div class="wrapper-form-header-search-input">
                  <input class="input-box-search-in-header" type="text" placeholder="Search MTG Books">
                  <button class="btn-in-open-search-header-search"></button>
                </div>

                <button class="close-all-searchdiv-box" type="button">
                  <span class="wrapper-close-btn-search-header-all">
                    <span class="close-btn-search-header-one-part"></span>
                    <span class="close-btn-search-header-second-part"></span>
                  </span>
                </button>
              </form>
            </div>
            <aside class="search-result-arihant">
              <section class="search-result-in-wrapper-one">
                <div class="search-result-arihant-one-on">
                  <h3 class="search-result-quick-disc">Quick Links</h3>
                  <ul class="arihant-all-list-view-serach-result">
                    <li class="result-all-item-here-animated"><a href="javascript:void(0)">New Release</a></li>
                    <li class="result-all-item-here-animated"><a href="javascript:void(0)">Best Selling Books</a></li>
                    <li class="result-all-item-here-animated"><a href="javascript:void(0)">Engineering Entrances</a></li>
                    <li class="result-all-item-here-animated"><a href="javascript:void(0)">Medical Entrances</a></li>
                    <li class="result-all-item-here-animated"><a href="javascript:void(0)">School Curriculum</a></li>
                    <li class="result-all-item-here-animated"><a href="javascript:void(0)">Government Exams</a></li>
                    <li class="result-all-item-here-animated"><a href="javascript:void(0)">ITI & Polytechnic</a></li>
                  </ul>
                </div>
              </section>
            </aside>
          </div>
        </div>
      </div>
      <div class="headerCategoriesMenu">
        <div class="header__categories">
          <ul class="header__categories-list" id="catList"></ul>
        </div>
        <div class="header__submenus" id="headerCategorySubMenus">
          <p class="text-center subMenuTitleText"><strong id="subMenuTitle"></strong></p>
          <div class="submenuLists">
            <ul class="syllabusList"></ul>
            <div class="listScrollArrow">
              <div class="listScrollArrowBall"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-backdrop fade show headerBackdrop d-none" id="categoryMenuBackdrop"></div>
    </div>
  </div>
</div>
</header>
<% }%>
<script>
  var activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
  function logout(){
    window.location.href = '/logoff';
  }
  var levelTags = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
  var syllabusTags = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
  function updateHeaderCategories(){
    let catItems="";
    const catList = levelTags;
    const catListElement = document.getElementById('catList');
    catList.forEach((cat,index)=>{
      const catName = cat.level.replaceAll(' ','-');
      catItems += "<li class='header__categories-list__item'>" +
              "<a href='/${session["entryController"]}/store?level="+catName+"' class='headerLevel' target='_blank'>"+cat.level+"</a>"+
              "</li>";
    });
    catListElement.innerHTML = catItems;
    const hoverElement = document.querySelectorAll('.headerLevel');
    const showDiv = document.getElementById('headerCategorySubMenus');
    const handleMouseLeave = () => {
      addRemoveBackDrop('hide',showDiv);
    };
    hoverElement.forEach(elem=>{
      elem.addEventListener('mouseover', () => {
        updateSubMenus(elem.textContent)
        addRemoveBackDrop('show',showDiv);
      });
      showDiv.addEventListener('mouseout', handleMouseLeave);
      showDiv.addEventListener('mouseover', ()=>{
        addRemoveBackDrop('show',showDiv);
      });
      document.querySelector('#nav .container-fluid').addEventListener('mouseover',handleMouseLeave);
      document.addEventListener('click',handleMouseLeave);
    });
  }
  function updateSubMenus(hoveredText){
    let syllabusListHTML = "";
    const syllabusListDiv =  document.querySelector('.syllabusList');
    const headerCategorySubMenus = document.getElementById('headerCategorySubMenus');
    document.getElementById('subMenuTitle').innerHTML = hoveredText;
    const listScrollArrow = document.querySelector('.listScrollArrow');
    let syllabusCount = 0;
    syllabusTags.forEach(syllabus=>{
      if (syllabus.level === hoveredText){
        let syllabusLink = syllabus.level.replaceAll(" ",'-');
        syllabusLink += "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-');
        syllabusListHTML += "<li><a href='/${session["entryController"]}/store?level="+syllabusLink+"' target='_blank'>"+syllabus.syllabus+"</a></li>";
        syllabusCount++;
      }
    })
    syllabusListDiv.innerHTML = syllabusListHTML;
    setTimeout(()=>{
      if (headerCategorySubMenus.offsetHeight >= 500 && syllabusCount >= 30){
        listScrollArrow.style.display = 'flex';
      }else{
        listScrollArrow.style.display = 'none';
      }
    })
  }
  function addRemoveBackDrop(action,showDiv){
    const categoryMenuBackdrop = document.getElementById('categoryMenuBackdrop');
    const headerElement = document.querySelector('#nav .container-fluid');
    const headerCategoriesMenu = document.querySelector('.headerCategoriesMenu');
    if(action=='show'){
      showDiv.style.display = 'block';
      showDiv.style.opacity = '1';
      categoryMenuBackdrop.classList.remove('d-none');
      categoryMenuBackdrop.style.top = headerElement.offsetHeight + headerCategoriesMenu.offsetHeight +10+'px';
    }else if(action=='hide'){
      showDiv.style.display = 'none';
      showDiv.style.opacity = '0';
      categoryMenuBackdrop.classList.add('d-none');
    }
  }
  if (levelTags.length>0){
    updateHeaderCategories();
  }else {
    document.querySelector('.headerCategoriesMenu').classList.add('d-none');
  }
  if ($(window).width() < 767){
    let accordionHTMl = "";
    let accID = "";
    accordionHTMl +="<div class='accordion' id='accordion'>";
    levelTags.forEach((cat,index)=>{
      accID = index;
      accordionHTMl += "<div class='card mb-3'>" +
              "<div class='card-header p-2' id='heading-"+index+"'>";
      if(index==0){
        accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"'>";
      }else{
        accordionHTMl +="<button class='text-dark btn btn-link w-100 d-flex justify-content-between pl-0 collapsed' data-toggle='collapse' data-target='#collapse-"+index+"' aria-expanded='true' aria-controls='#collapse-"+index+"'>";
      }
      accordionHTMl +="<a href='/${session["entryController"]}/store?level="+cat.level.replaceAll(" ",'-')+"' target='_blank'><p>"+cat.level+"</p></a>"+
              "</button>" +
              "</div>";
      if (index==0){
        accordionHTMl +="<div id='collapse-"+accID+"' class='collapse show' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
      }else{
        accordionHTMl +="<div id='collapse-"+accID+"' class='collapse' aria-labelledby='heading-"+accID+"' data-parent='#accordion'>";
      }
      accordionHTMl +="<div class='card-body'>";
      syllabusTags.forEach((syllabus,index)=>{
        if (cat.level === syllabus.level){
          accordionHTMl +="<a href='/${session["entryController"]}/store?level="+cat.level+"&"+ "&syllabus="+ syllabus.syllabus.replaceAll(" ",'-')+"'  target='_blank'><p>"+syllabus.syllabus+"</p></a>";
        }
      })
      accordionHTMl +="</div>"+
              "</div>"+
              "</div>";
    })
    accordionHTMl+="</div>";
    document.querySelector('.arihant-big-menu-side-wrp').innerHTML = accordionHTMl;
  }
</script>
