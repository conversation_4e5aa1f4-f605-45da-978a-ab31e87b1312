<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
    var userLoggedIn=false;
</script>

<sec:ifLoggedIn>
    <script>
        userLoggedIn=true;
    </script>
</sec:ifLoggedIn>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

%{--col-md-10--}%
%{--variables and commons--}%
<script>
    function replaceAll(str, find, replace) {
        if (str == undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    var books1 = JSON.parse("${latestBooks}".replace(/&quot;/g, '"'));
    var banners = JSON.parse("${banners}".replace(/&quot;/g, '"'));
    var categories1 = JSON.parse("${categories}".replace(/&quot;/g, '"'));
    var topLevel = JSON.parse("${topLevel}".replace(/&quot;/g, '"'));
    var tokenId = "${params.tokenId}";
    var publisherBg = '${publisher.backgroundColor}';

    let books = [];
    books1.filter((book)=>{
        const i = books.findIndex((b)=> book.id === b.id);
        if(i<0){
            books.push(book);
        }
    });

    let categories = [];
    categories1.filter((element) => {
        const index = categories.findIndex(
            (category) =>
                category.level === element.level && category.syllabus === element.syllabus
        );
        if (index <= -1) {
            let grades = [];
            grades = grades.concat(element.grade);
            const allGrades = {
                level: element.level,
                syllabus: element.syllabus,
                grades,
            };
            categories.push(allGrades);
        } else if (!categories[index].grades.includes(element.grade)) {
            categories[index].grades.push(element.grade);
        }
    });

</script>

<section class="page-main-wrapper mdl-js publisher_page">
    <div class="container">
        <div class="col-md-12 py-4 px-0">

            %{--banner--}%
            <div class="row m-0 p-0 justify-content-center">
                <div class="h-100 w-100">
                    <div id="slider-desktop" class="carousel slide h-100 w-100 rounded shadow-sm d-none d-md-block" data-ride="carousel">
                        <ol class="carousel-indicators" id="banner-carousel-indicators"></ol>

                        <div class="carousel-inner w-100" id="banner-carousel"></div>
                    </div>
                    <div id="slider-mobile" class="carousel slide h-100 w-100 rounded shadow-sm d-md-none" data-ride="carousel">
                        <ol class="carousel-indicators" id="mobile-banner-carousel-indicators"></ol>
                        <div class="carousel-inner w-100" id="mobile-banner-carousel"></div>
                    </div>
                </div>
            </div>
            <script>
                var publisherName = '${publisher.name}';
                var publisherTagline = '${publisher.tagline}';
                var checkDescBanners = false;
                var checkMobBanners = false;
                var template = '';

                function displayBooksBanner(books) {
                    var colors = ['#2EBAC6', '#0D5FCE', '#6FCF97', '#F2C94C', '#C20232', '#FC7753', '#E40039', '#1abc9c', '#FD7272', '#55E6C1', '#17c0eb'];
                    var noOfEbooks = 0;
                    var ebookHtmlStr = "";
                    var imgSrc = "";

                    for (var i = 0; i < books.length; i++) {

                        imgSrc = books[i].coverImage;
                        if (books[i].coverImage != null && books[i].coverImage.startsWith("https")) {
                            imgSrc = books[i].coverImage;
                            imgSrc = imgSrc.replace("~", ":");
                        } else {
                            imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=webp";
                        }

                        var margin = "";
                        if(i==0 || i==2){
                            margin='1rem !important';
                        }else{
                            margin='-1rem !important';
                        }

                        ebookHtmlStr += "<div style='z-index: "+i+"; margin-top: "+margin+" ' class='col-6 col-md-4 col-lg-3 d-flex justify-content-center mb-md-4 mb-3 banner-books'>" +
                            "<div class='topSchoolBooks'>" +
                            "<div class='image-wrapper'>";
                        if (tokenId == "") {
                            ebookHtmlStr += "<a href='/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/ebook-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true' target='_blank'>";
                        } else {
                            ebookHtmlStr += "<a href='/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/ebook-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true&tokenId=" + tokenId + "'>";
                        }

                        ebookHtmlStr += "<div class='bookShadow'>";

                        if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                            ebookHtmlStr += "<div class='uncover' >" +
                                "<p >" + books[i].title + "</p>" +
                                "</div>";
                        } else {
                            ebookHtmlStr += "<img src='" + imgSrc + "' alt=''/>";
                        }

                        ebookHtmlStr += "</div>";
                        if (books[i].bookType == "print") {
                            ebookHtmlStr += "<h3>Print Book</h3>";
                        }else if(books[i].bookType == "ebook"){
                            ebookHtmlStr +="<h3>eBook</h3>";
                        }else if(books[i].bookType =="testseries"){
                            ebookHtmlStr +="<h3>Test Series</h3>";
                        }else if(books[i].bookType =="onlinecourse") {
                            ebookHtmlStr +="<h3>Online Course</h3>";
                        }else if(books[i].bookType =="liveclasses"){
                            ebookHtmlStr +="<h3>Live Classes</h3>";
                        }else if(books[i].bookType =="mcqsebook"){
                            ebookHtmlStr +="<h3>MCQs eBook</h3>";
                        }else if(books[i].bookType =="previouspapers"){
                            ebookHtmlStr +="<h3>Previous Papers</h3>";
                        }else{
                            ebookHtmlStr +="<h3>eBook</h3>";
                        }
                        ebookHtmlStr += "</a>" +
                            "</div>";

                        if (tokenId == "") {
                            ebookHtmlStr += "<a href='/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/ebook-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true' target='_blank'>";
                        } else {
                            ebookHtmlStr += "<a href='/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/ebook-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true&tokenId=" + tokenId + "'>";
                        }


                        ebookHtmlStr += "" +
                            "</a>" +
                            "</div>" +

                            "</div>";
                        noOfEbooks++;

                    }
                    if (noOfEbooks > 0) {
                        document.getElementById('content-data-books-ebooks-banner').innerHTML =  ebookHtmlStr;
                    }
                    var fronts = document.querySelectorAll(".uncover");
                    for (var i = 0; i < fronts.length; i++) {
                        fronts[i].style.background = colors[i % 11];
                    }

                }

                $('#banner-carousel,#mobile-banner-carousel').empty();
                if (banners.length > 0) {
                    $.each(banners, function (i, b) {
                        var item = b;
                        var bannerStr = "";
                        var indicatorStr = "";
                        var indicatorStrMobile = "";
                        var bannerStrMobile = "";
                        var imageDescUrl = "/wonderpublish/showImage?id=" + b.id + "&fromPub=true&fileName=" + b.imagePath + "&imgType=webp";
                        var imageMobUrl = "/wonderpublish/showImage?id="+b.id+"&fromPub=true&fileName="+b.imagePathMobile + "&imgType=webp";
                        var bookHyperLink = "";

                        var bookUrl = "/-";
                        books.forEach((book) => {
                            if (book.id == b.bookId) {
                                bookUrl = "/" + replaceAll(replaceAll(book.title, ' ', '-').toLowerCase(), '\'', '');
                                bookHyperLink = bookUrl + "/ebook-details?siteName=${session['entryController']}&bookId=" + b.bookId + "&preview=true";
                            }
                        });

                        indicatorStr += "<li data-target='#slider-desktop' class='border-light' data-slide-to='" + i + "'></li>";
                        indicatorStrMobile += "<li data-target='#slider-mobile' class='border-light' data-slide-to='" + i + "'></li>";
                        if(b.bookId) {
                            bannerStr += "<div class='carousel-item w-100'>" +
                                "<a href='" + bookHyperLink + "' target='_blank'>" +
                                "<img src='" + imageDescUrl + "' class='d-block w-100 rounded banner-img' alt='Banner Image'>" +
                                "</a>" +
                                "</div>";
                            bannerStrMobile += "<div class='carousel-item w-100'>" +
                                "<a href='" + bookHyperLink + "' target='_blank'>" +
                                "<img src='" + imageMobUrl + "' class='d-block w-100 rounded banner-img' alt='Banner Image'>" +
                                "</a>" +
                                "</div>";
                        } else {
                            bannerStr += "<div class='carousel-item w-100'>" +
                                "<img src='" + imageDescUrl + "' class='d-flex align-items-center w-100 rounded banner-img' alt='Banner Image'>" +
                                "</div>";
                            bannerStrMobile += "<div class='carousel-item w-100'>" +
                                "<img src='" + imageMobUrl + "' class='d-flex align-items-center w-100 rounded banner-img' alt='Banner Image'>" +
                                "</div>";
                        }

                        // If desktop banners are available
                        if(b.imagePath) {
                            checkDescBanners = true;
                            $('#banner-carousel').append(bannerStr).find('.carousel-item:first-child').addClass('active');
                            $('#banner-carousel-indicators').append(indicatorStr).find('li:first-child').addClass('active');
                        }

                        // If mobile banners are available
                        if(b.imagePathMobile) {
                            checkMobBanners = true;
                            $('#mobile-banner-carousel').append(bannerStrMobile).find('.carousel-item:first-child').addClass('active');
                            $('#mobile-banner-carousel-indicators').append(indicatorStrMobile).find('li:first-child').addClass('active');
                        }
                    });
                } else {
                    checkDescBanners = false; checkMobBanners = false;
                }

                // Showing empty banners based on condition
                if(!checkDescBanners && !checkMobBanners) {
                    template += emptyBannerUI(publisherName,publisherTagline);
                    $('#banner-carousel').append(template);
                    $("#slider-desktop").removeClass("d-none");
                    $("#slider-mobile").addClass("d-none");
                    if($(window).width()<767){
                        displayBooksBanner(books.slice(0, 2));
                    } else if($(window).width()<991){
                        displayBooksBanner(books.slice(0, 3));
                    } else {
                        displayBooksBanner(books.slice(0, 4));
                    }
                } else if(!checkMobBanners) {
                    template += emptyBannerUI(publisherName,publisherTagline);
                    $('#mobile-banner-carousel').append(template);
                    displayBooksBanner(books.slice(0, 2));
                }

                // If banner images are empty calling this function
                function emptyBannerUI(publisherName,publisherTagline) {
                    var emptyBannerTemplate = "<div class='carousel-item w-100 active rounded'> <div class='d-flex justify-content-center align-items-center w-100 rounded no-banners'>" +
                        "<div class='col-12 col-md-4 text-center'>" +
                        "<h1>"+publisherName+"</h1>"+
                        "<p class='text-secondary text-secondary-modifier'>"+publisherTagline+"</p>"+
                        "</div>"+
                        "<div class='col-12 col-md-8'>" +
                        "<div class='books-list mt-4 mt-md-0'><div class='d-flex justify-content-center align-items-center w-100' id='content-data-books-ebooks-banner'></div></div>" +
                        "</div>" +
                        "</div></div>";
                    return emptyBannerTemplate;
                }

                if (publisherBg) {
                    $('.carousel').css("background", publisherBg);
                }

            </script>

            %{--category--}%
            <div class="publisher_categories container">
                <div class="row m-0 p-0 justify-content-center">
                    <div class="col-12 col-md-10 m-0 p-0 rounded shadow bg-light">
                        <div class="row px-4 m-0 justify-content-center" id="category-inner">

                        </div>
                    </div>
                </div>
            </div>
            <script>

                function showMoreCategories() {
                    $(".hidden-category").toggleClass('d-none');
                    $(".show-remains,.hide-remains").toggleClass('d-none');
                }
                $(document).ready(function () {
                    if (window.matchMedia("(max-width: 700px)").matches) {
                        $('.resdropdown').addClass('dropdown');
                    } else {
                        $('.resdropdown').addClass('dropright');
                    }
                    // close sub menu on clicking main menu
                    $('.dropdown-toggle').on('click', (e) => {
                        $('.sbmnu').css('display', "none");
                    });
                    // close sub menu on clicking other sub menu
                    $('.dropdown-toggle-submenu').on("click", function (e) {
                        e.preventDefault();
                        if ($(this).parent('div').hasClass('dropright') || $(this).parent('div').hasClass('dropdown')) {
                            $('.dropdown-toggle-submenu').next('ul').css('display', "none");
                            $(this).next('ul').css('display', 'block');
                            e.stopPropagation();
                        }
                    });
                });
                var categoryStr = "";
                var lengthOfCategory;
                if($(window).width()<991) {
                    lengthOfCategory = 2;
                } else {
                    lengthOfCategory = 3;
                }
                // topLevel.length>1
                if (topLevel.length<1) {
                    topLevel.forEach((t, i) => {
                        categoryStr +=
                            "<div class='dropdown col-6 col-lg-4 m-0 px-2 text-center h-100 w-100'>" +
                            "<div class='dropdown-toggle btn h-100 w-100 pr-0 pl-0 pt-3 pb-3 d-flex align-items-center border-bottom rounded-0' data-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\"><span class='category-menu'>" + t.level + "</span></div>" +
                            "<ul class='dropdown-menu p-0 w-100'>";
                        categories.forEach((c, i) => {
                            if (c.level === t.level) {
                                categoryStr +=
                                    "<li class='dropdown-item m-0 p-0 h-100 w-100'>" +
                                    "<div class='resdropdown m-0 p-0 h-100 w-100'>" +
                                    "<div  class='dropdown-toggle-submenu dropdown-toggle h-100 w-100 p-3 btn text-left d-flex align-items-center' data-toggle=\"dropdown\" aria-haspopup=\"true\" aria-expanded=\"false\"><span class='category-menu'>" + c.syllabus + "</span><span class=\"caret\"></span></div>" +
                                    "<ul class='dropdown-menu dropdown-menu-left sbmnu p-0'>";
                                c.grades.forEach((g, i) => {
                                    var gradeName = c.level == 'School' ? "Class " + g : g;
                                    categoryStr += "<li class='dropdown-item dropdown-list-item btn p-2 px-3' onclick=\"loadBooks('" + c.level + "','" + c.syllabus + "','" + g + "')\">" + gradeName + "</li>";

                                })
                                categoryStr +=
                                    "</ul>" +
                                    "</div>" +
                                    "</li>";
                            }
                        })

                        categoryStr +=
                            "</ul>" +
                            "</div>";
                    });
                } else {
                    categories.forEach((t, i) => {
                        if(categories.length>lengthOfCategory){
                            $("#category-inner").removeClass("justify-content-center px-4").addClass("justify-content-left pl-3 pr-4 pr-lg-5");
                            categoryStr +="<div class='py-3 pl-lg-3 pl-2 pr-2 btn h-100 d-flex align-items-start show_more_categories' onclick='showMoreCategories()'><i class=\"material-icons-round show-remains\">more_horiz</i><i class=\"material-icons-round hide-remains d-none\">close</i></div>";
                        }
                        if (i < lengthOfCategory) {

                            categoryStr +=
                                "<div class='dropdown col-6 col-lg-4 m-0 px-2 text-left h-100 w-100'>" +
                                "<div class='dropdown-toggle btn h-100 w-100 py-3 pl-0 m-0 d-flex align-items-center border-bottom rounded-0' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'><span class='category-menu'>" + t.syllabus + "</span></div>" +
                                "<ul class='dropdown-menu dropdown-menu-left p-0'>";
                            t.grades.forEach((c, i) => {
                                categoryStr +=
                                    "<li class='dropdown-item dropdown-list-item btn p-2 px-3' onclick=\"loadBooks('" + t.level + "','" + t.syllabus + "','" + c + "')\">" + c + "</li>";
                            })

                            categoryStr +=
                                "</ul>" +
                                "</div>";
                        } else {
                            categoryStr +=
                                "<div class='dropdown col-6 col-lg-4 m-0 px-2 text-left h-100 w-100 d-none hidden-category'>" +
                                "<div class='dropdown-toggle btn h-100 w-100 py-3 pl-0 m-0 d-flex align-items-center border-bottom rounded-0'  data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'><span class='category-menu'>" + t.syllabus + "</span></div>" +
                                "<ul class='dropdown-menu dropdown-menu-left p-0'>";
                            t.grades.forEach((c, i) => {
                                var gradeName = t.level == 'School' ? "Class " + c : c;
                                categoryStr +=
                                    "<li class='dropdown-item dropdown-list-item btn p-2 px-3'  onclick=\"loadBooks('" + t.level + "','" + t.syllabus + "','" + c + "')\">" + gradeName + "</li>";

                            })

                            categoryStr +=
                                "</ul>" +
                                "</div>"
                        }
                    });
                }
                document.getElementById('category-inner').innerHTML = categoryStr;
            </script>

            %{--books--}%
            <div class="books-content-wrapper books-list pt-3 pt-md-4"><div class="row mt-4" id="content-data-books-ebooks"></div></div>
            <script>

                function loadBooks(level, syllabus, grade) {
                    $('.loading-icon').removeClass('hidden');
                    <g:remoteFunction controller="wonderpublish" action="getNewBooksList"  onSuccess='booksReceived(data);'
             params="'categories=true&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject=null&publisherId=${publisher.id}'" />
                }

                function booksReceived(data) {
                    if (data.status == "Nothing present") {
                        alert("No results found for your search. Please change the search criteria and search again.");
                        $('.loading-icon').addClass('hidden');
                    } else {
                        displayBooks(JSON.parse(data.books));
                        $('.loading-icon').addClass('hidden');
                    }
                }

                function displayBooks(books) {
                    var colors = ['#2EBAC6', '#0D5FCE', '#6FCF97', '#F2C94C', '#C20232', '#FC7753', '#E40039', '#1abc9c', '#FD7272', '#55E6C1', '#17c0eb'];
                    var noOfEbooks = 0;
                    var ebookHtmlStr = "";
                    var imgSrc = "";

                    var bookUrl = "";

                    var offerPrice = "";
                    var listPrice = "";
                    var addToCartBtn = "";

                    for (var i = 0; i < books.length; i++) {
                        if (books[i].offerPrice == 0 || books[i].offerPrice == 0.0 || books[i].offerPrice == null) {
                            offerPrice = "FREE";
                            addToCartBtn = "";
                        } else {
                            offerPrice = "&#x20b9 " + books[i].offerPrice;
                            if(userLoggedIn) {
                                addToCartBtn = "<a href='javascript:addToCart("+books[i].id+")' class='add_to_cart_btn'><img src='/assets/wonderslate/cart-main.svg'> <span id='addToCartBtn"+books[i].id+"'>Add to Cart</span></a>";
                            } else {
                                addToCartBtn = "<a href='javascript:loginOpen()' class='add_to_cart_btn'><img src='/assets/wonderslate/cart-main.svg'> Add to Cart</a>";
                            }

                        }

                        if (books[i].listPrice == 0 || books[i].listPrice == 0.0 || books[i].listPrice == "null" || books[i].listPrice == null || books[i].listPrice == books[i].offerPrice) {
                            listPrice = "";
                        } else {
                            listPrice = "&#x20b9 " + books[i].listPrice;

                        }

                        imgSrc = books[i].coverImage;
                        if (books[i].coverImage != null && books[i].coverImage.startsWith("https")) {
                            imgSrc = books[i].coverImage;
                            imgSrc = imgSrc.replace("~", ":");
                        } else {
                            imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=webp";
                        }


                        ebookHtmlStr += "<div class='col-6 col-md-4 col-lg-2 d-flex d-sm-block justify-content-center mb-md-4 mb-3'>" +
                            "<div class='topSchoolBooks'>" +
                            "<div class='image-wrapper'>";
                        if (tokenId == "") {
                            ebookHtmlStr += "<a href='/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/ebook-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true' target='_blank'>";
                        } else {
                            ebookHtmlStr += "<a href='/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/ebook-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true&tokenId=" + tokenId + "'>";
                        }

                        ebookHtmlStr += "<div class='bookShadow'>";

                        if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                            ebookHtmlStr += "<div class='uncover'>" +
                                "<p>" + books[i].title + "</p>" +
                                "</div>";
                        } else {
                            ebookHtmlStr += "<img src='" + imgSrc + "' alt=''/>";
                        }

                        ebookHtmlStr += "</div>";
                        if (books[i].bookType == "print") {
                            ebookHtmlStr += "<h3>Print Book</h3>";
                        }else if(books[i].bookType == "ebook"){
                            ebookHtmlStr +="<h3>eBook</h3>";
                        }else if(books[i].bookType =="testseries"){
                            ebookHtmlStr +="<h3>Test Series</h3>";
                        }else if(books[i].bookType =="onlinecourse") {
                            ebookHtmlStr +="<h3>Online Course</h3>";
                        }else if(books[i].bookType =="liveclasses"){
                            ebookHtmlStr +="<h3>Live Classes</h3>";
                        }else if(books[i].bookType =="mcqsebook"){
                            ebookHtmlStr +="<h3>MCQs eBook</h3>";
                        }else if(books[i].bookType =="previouspapers"){
                            ebookHtmlStr +="<h3>Previous Papers</h3>";
                        }else{
                            ebookHtmlStr +="<h3>eBook</h3>";
                        }
                        ebookHtmlStr += "</a>" +
                            "</div>";

                        if (tokenId == "") {
                            ebookHtmlStr += "<a href='/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/ebook-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true' target='_blank'>";
                        } else {
                            ebookHtmlStr += "<a href='/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/ebook-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true&tokenId=" + tokenId + "'>";
                        }

                        ebookHtmlStr += "<div class='content-wrapper'>" +
                            "<h3>" + books[i].title + "</h3>";
                        if (books[i].bookType == "print") {
                            ebookHtmlStr += "<p></p>";
                        } else {
                            ebookHtmlStr += "<p class='price'>" + offerPrice + "<span>" + listPrice + "</span>" + "</p>";
                            <%if("3".equals(""+session["siteId"])||"1".equals(""+session["siteId"])){%>
                            ebookHtmlStr += addToCartBtn;
                            <%}%>
                        }
                        ebookHtmlStr += "</div>" +
                            "</a>" +
                            "</div>" +

                            "</div>";
                        noOfEbooks++;

                    }
                    if (noOfEbooks > 0) {
                        document.getElementById("content-data-books-ebooks").innerHTML =  ebookHtmlStr;
                    } else {
                        document.getElementById("content-data-books-ebooks").innerHTML =  "<div class='col-12 my-3 text-center'><p><strong>No eBooks are available!</strong></p></div>";
                    }
                    var fronts = document.querySelectorAll(".uncover");
                    for (var i = 0; i < fronts.length; i++) {
                        fronts[i].style.background = colors[i % 11];
                    }

                }

                displayBooks(books);

            </script>

        </div>
    </div>
</section>

<g:render template="/books/footer_new"></g:render>
