<% if ("1".equals("" + session["siteId"])) { %>
<asset:stylesheet href="wonderslate/signup.css" async="true"/>
<% } else { %>
<asset:stylesheet href="${session['entryController']}/signup.css" async="true"/>
<% } %>

<% String siteName = session.getAttribute("siteName") != null ? session.getAttribute("siteName") : grailsApplication.config.grails.appServer.siteName; %>

<%
    boolean mobileOnlyLogin = "mobile".equals("" + session.getAttribute("loginType"))
%>
<% if (!(("android".equals(session["appType"])) || ("ios".equals(session["appType"])))) { %>

<style>
/* Removing Input Field Arrows */
#loginOpen input::-webkit-outer-spin-button,
#loginOpen input::-webkit-inner-spin-button,
#signup input::-webkit-outer-spin-button,
#signup input::-webkit-inner-spin-button,
#forgotPasswordmodal input::-webkit-outer-spin-button,
#forgotPasswordmodal input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
#loginOpen input[type=number],
#signup input[type=number],
#forgotPasswordmodal input[type=number] {
    -moz-appearance: textfield;
}
</style>

<!--------------    LOGIN MODAL  ------------->
<div class="modal fade" id="loginOpen" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="login_signup_loader mdl-progress mdl-js-progress mdl-progress__indeterminate"></div>

            <!-- Modal body -->
            <div class="modal-body d-lg-flex p-0">
                <button type="button" class="close" data-dismiss="modal">X</button>
                <div class="modal-text-content col-lg-6 p-4 p-lg-5 d-flex">
                    <lottie-player src="https://assets1.lottiefiles.com/packages/lf20_jcikwtux.json" background="transparent" speed="1" loop autoplay></lottie-player>
                    <h1 id="loginMessage">The Joy of <span>Learning</span></h1>
                </div>
                <div class="modal-form-content col-12 col-lg-6 p-4 p-md-5 d-flex align-items-center justify-content-center flex-column">
                    <h4 class="modal-title head-title w-100" id="signUpTitle">Login/Signup</h4>
                    <p id="signUpSubTitle"></p>
                    <g:form name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off" id="adduser" class="w-100">
                        <div class="inputBox">
                            <input type="text" class="form-control mt-2 d-none" placeholder="Name" id="name" name="name" required autocomplete="off">
                        </div>
                        <div class="inputBox d-flex align-items-center">
                            <input type="text" name="migrateMobile" id="migrateMobile" class="form-control mt-2" placeholder="Mobile Number/Email" autocomplete="migrate-mobile" required>
                            <a href="javascript:editNumber()" class="editPencil" style="display: none"><i class="fa-solid fa-pencil"></i></a>
                        </div>
                        <div class="inputBox">
                            <input type="password" class="form-control mt-2 d-none" placeholder="Password" id="signup-password" name="password" value="123test" autocomplete="current-password" required>
                        </div>
                        <div class="inputBox">
                            <input class="form-control mt-2" type="text" id="email" name="email" style="display: none;" placeholder="Email" autocomplete="off">
                        </div>
                        <div class="inputBox">
                            <input class="form-control mt-4" placeholder="Confirming OTP" name="signupOtp" id="signupOtp" required maxlength="10" minlength="10" style="display: none;" autocomplete="off">
                        </div>

                        <div class="inputBox mt-3">
                            <select name="state" id="stateSelectNew" class="form-control mt-4 d-none">
                                <option value="" selected="selected">Select State</option>
                            </select>
                        </div>
                        <div class="inputBox mt-3">
                            <select name="district" id="districtSelectNew"  class="form-control mt-4 d-none">
                                <option value="" selected="selected">Select District</option>
                            </select>
                        </div>

                        <div class="erorSection d-flex align-items-center">
                            <p class="mobileErr ml-2 mt-3" style="display: none;color: #E83500!important;">Please enter 10 digit mobile number</p>
                            <p class="otpErr ml-2 mt-3" style="display: none;color: #E83500!important;">Please enter valid OTP</p>
                            <p class="nameErr ml-2 mt-3" style="display: none;color: #E83500!important;">Please enter valid name.</p>
                            <span class="timer ml-auto mr-2" style="color:#B72319;margin-top: 16px !important;display: none"></span>
                            <p class="resendotp mt-3" style="display: none;">Didn’t  recieve the OTP? <a href="javascript:resendOTP();" style="color: #E83500;">Resend</a> </p>
                        </div>

                        <div class="inputBox">
                            <input type="button" onclick="javascript:verifyMobileOTP();" class="mt-2 btn login-btn" value="Verify OTP" id="verifybtn" style="display: none">
                        </div>
                        <div class="inputBox">
                            <input type="button" onclick="javascript:proceed();" class="mt-2 btn login-btn" value="Continue" id="continue" style="display: none">
                        </div>

                        <input type="hidden" name="username">
                        <input type="hidden" name="mobile">
                        <input type="hidden" name="otp_finished" value="true">


                        <div class="inputBox">
                            <input type="button"  onclick="javascript:submitMigrateOTP(false);" class="mt-4 btn login-btn btn-block" value="GET OTP" id="migrateOtp">
                        </div>

                        <div class="inputBox">
                            <button class="mt-4 btn login-btn" type="button" onclick="javascript:formSubmit();" style="display: none;" id="register">Register</button>
                        </div>
                    </g:form>

                </div>

            </div>

        </div>
    </div>
</div>


<!--------------    FORGOT-PASSWORD MODAL  ------------>
<div class="modal fade" id="forgotPasswordmodal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="login_signup_loader mdl-progress mdl-js-progress mdl-progress__indeterminate"></div>

            <!-- Modal body -->
            <div class="modal-body d-lg-flex p-0">
                <button type="button" class="close" data-dismiss="modal">X</button>
                <div class="modal-text-content col-12 col-lg-6 p-4 p-lg-5 d-flex">
                    <lottie-player src="https://assets1.lottiefiles.com/packages/lf20_jcikwtux.json" background="transparent" speed="1" loop autoplay></lottie-player>
                    <h1>The Joy of <span>Learning</span></h1>
                </div>
                <div class="modal-form-content col-12 col-lg-6 p-4 p-md-5">
                    <h4 class="modal-title head-title" id="forgotTitle">Forgot Password</h4>
                    <p id="forgotSubTitle">Don't worry. We'll fix this for you!</p>
                    <form class="mt-4 text-center" id="forgot-form">

                        <div class="otp-wrapper">

                            <input type="number" class="form-control mt-3" placeholder="Confirming OTP..." name="otpConfirm"
                                   id="otpConfirm" required maxlength="10" minlength="10">
                            <p id="ForgotPasswordOTPError" class="text-left error-text"></p>
                            <input type="button" onclick="javascript:verifyOTP();" class="mt-4 btn login-btn"
                                   value="Verify OTP">

                            <p class="mt-3">Verification code has been sent to <b><span id="sentMobileNumber"></span></b></p>
                            <div>
                                <span class="timer">
                                    <span class="time"></span>
                                </span>
                            </div><br>

                            <p class="resendotp" style="display: none;">Did not receive the OTP? <a
                                    href="javascript:resendForgotPasswordOTP();">Resend</a></p>

                        </div>

                        <p id="user-exist" class="error-msg"></p>

                        <span class="error-msg otperror-msg" style="display: none;">OTP failed. OTP is Incorrect.</span>

                        <p class="error-msg" id="limit-signin-verify"></p>

                    </form>
                    <g:form name="adduserFromForgot" url="[action: 'addUser', controller: 'creation']" method="post"
                            autocomplete="off" id="signUpFromForgot" class="mt-3 mb-4" style="display: none;">
                        <input type="text" class="form-control" placeholder="Name" id="nameFromForgot" name="name" required
                               value="" autocomplete="off">
                        <p id="nameErrorFromForgot" class="text-left error-text"></p>
                        <div class="position-relative mt-3">
                            <input type="password" class="form-control" placeholder="Password" id="passwordFromForgot"
                                   name="password" autocomplete="off" required>
                            <a href="javascript:void(0);" class="hide-password material-icons">visibility</a>
                            <p id="passwordFromForgotError" class="text-left error-text"></p>
                        </div>

                        <input type="hidden" name="username">
                        <input type="hidden" name="mobile">
                        <input type="hidden" name="email">
                        <input type="hidden" name="otp_finished" value="true">
                        <input type="button" onclick="javascript:formSubmitForgot();" class="mt-4 btn login-btn"
                               value="CONTINUE">
                    </g:form>
                </div>
            </div>

        </div>
    </div>
</div>

<% } %>
<% if(!"bookgpt".equals(params.action)){%>
<asset:javascript src="jquery-1.11.2.min.js"/>
<%}%>

<script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-player/1.4.3/lottie-player.min.js"></script>

<script>
    document.querySelector('.login_signup_loader').addEventListener('mdl-componentupgraded', function() {
        this.MaterialProgress.setProgress(44);
    });

    var functionNameToCall = "";
    var loginHappened = false;
    var siteIds = "<%=session.getAttribute("siteId")%>";
    var otpReg = "${otpReg}";
    var operation;
    var mobileNumber;
    var nextDestination = null;
    var userCheck = true;
    var validation;
    var forceRegisterMode=null;
    var registerBookId=null;
    var registerBookTitle=null;
    var returnFunctionCallForQuiz=null;
    var stateSel = document.getElementById("stateSelectNew"),
        districtSel = document.getElementById("districtSelectNew");

    //FORGOT PASSWORD SUBMISSION
    function formFPSubmit() {
        $("#emailidnf").hide();

        if (!$("#fPemail").val()) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        } else {

            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos < 1 || dotpos < atpos + 2 || dotpos + 2 >= email.length) {
                return false;
            } else {
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
            }
        }
    }

    //DISPLAYING FORGOT PASSWORD DETAILS
    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if ("OK" == data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-completed');
            $('#fp-user-email').html("“" + userEmail + "�?");
        } else if ("Google" == data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-google-paswd');
            $('#fp-user-email1').html("“" + userEmail + "�?");
        } else if ("Fail" == data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'account-exist');
        }
    }
</script>


<script>
    //P.R
    var loginName = document.getElementById("number");

    //OPENING SIGNUP MODAL
    function signupModal() {
        if ('${session['wileySite']}' !='true'){
            forceRegisterMode=null;
            $('#signup').modal('show');
            document.getElementById("signUpTitle").innerText="Sign Up";
            document.getElementById("signUpSubTitle").innerText="";
            document.getElementById("migrateMobile").type="text";
            document.getElementById("migrateMobile").placeholder="Mobile no / Email ";
            $('#loginOpen').modal('hide');
            $('p.error-msg').text('');
            $('#loginPasswordError').text('');
            $('#nameError').text('');
            $('#migrateMobileError').text('');
            $('.verification-code').hide();
            $('#migrateOtp').show();
            $("#signupOtp").hide();
            operation = "signup";
        }else{
            $('.loading-icon').removeClass('hidden');
            window.location.href='/privatelabel/wileySignup?signUpPage=true';
        }
    }

    //Sign up Submit
    function formSubmit() {
        if ($('#name,#mobile,#signup-password').val().length != 0) {
            // Disable register button and show loader
            disableButtonWithLoader('register');

            document.adduser.username.value = mobileNumber;
            if (mobileNumber.includes("@")) {
                document.adduser.email.value = mobileNumber;
            } else {
                document.adduser.mobile.value = mobileNumber;
            }
            var oData = new FormData(document.forms.namedItem("adduser"));
            oData.append("site", "Wonderslate");

            var url = "${createLink(controller:'creation',action:'addUser')}";

            var correctMobile = false;
            var correctEmail = false;
            if (/^\d{10}$/.test($("#mobile").val()) && $("#mobile").val() != "") {
                correctMobile = true;
                document.adduser.mobile.value = $("#mobile").val();
            }
            if(forceRegisterMode=="quiz"){
                oData.append("forceRegisterMode", "quiz");
            }

            ajaxFunction(url, oData);

        }
    }

    function formSubmitForgot() {
        var fullname = document.getElementById("nameFromForgot");
        var password = document.getElementById("passwordFromForgot");
        if (fullname.value == '') {
            $('#nameErrorFromForgot').text('Please enter your name.');
            $(fullname).focus().addClass('input-error');
            $('#passwordFromForgotError').text('');
            $(password).removeClass('input-error');
        } else if (password.value == '') {
            $('#passwordFromForgotError').text('Please enter password.');
            $(password).focus().addClass('input-error');
        } else {
            if ($('#passwordFromForgot,#passwordFromForgot').val().length != 0) {
                // Disable CONTINUE button for forgot password and show loader
                $('input[onclick="javascript:formSubmitForgot();"]').prop('disabled', true);
                $('.login_signup_loader').show();

                document.adduserFromForgot.username.value = mobileNumber;
                if (mobileNumber.includes("@")) {
                    document.adduserFromForgot.email.value = mobileNumber;
                } else {
                    document.adduserFromForgot.mobile.value = mobileNumber;
                }

                var oData = new FormData(document.forms.namedItem("adduserFromForgot"));
                oData.append("site", "Wonderslate");

                var url = "${createLink(controller:'creation',action:'addUser')}";

                ajaxFunctionForgot(url, oData);
            }
        }
    }

    function ajaxFunction(url, oData) {
        $.ajax({
            url: url,
            type: 'POST',
            data: oData,
            processData: false,  // tell jQuery not to process the data
            contentType: false,
            success: function (req) {
                loginDone(req);
            },
            error: function() {
                // Re-enable buttons on error
                enableButtonWithoutLoader('register');
                enableButtonWithoutLoader('continue');
                alert('Registration failed. Please try again.');
            }
        });
    }

    function ajaxFunctionForgot(url, oData) {
        $.ajax({
            url: url,
            type: 'POST',
            data: oData,
            processData: false,  // tell jQuery not to process the data
            contentType: false,
            success: function (req) {
                loginDone(req);
            },
            error: function() {
                // Re-enable CONTINUE button for forgot password on error
                $('input[onclick="javascript:formSubmitForgot();"]').prop('disabled', false);
                $('.login_signup_loader').hide();
                alert('Registration failed. Please try again.');
            }
        });
    }

    //LOGIN CODES
    //OPEN Login modal
    function loginOpen() {
        $('#loginOpen').modal('show');

        $('#signup').modal('hide');
        $('#login-form').trigger('reset');
        $('p.error-msg').text('');
        $('#loginPasswordError').text('');
        $('#nameError').text('');
        $('#migrateMobileError').text('');

        if ($('.mega_menu__wrapper').hasClass('menu-showing')) {
            $(".navbar-hamburger").toggleClass("menu-actives");
            $(".mega_menu__overlay_bg").toggleClass("active");
            $(".mega_menu__wrapper").toggleClass("menu-showing");
        }

    }


    //FORGOT PASSWORD CODES

    function checkUserExist() {
        document.signin.username.value = document.signin.username_temp.value;
        var username = document.signin.username.value;
        var siteId = "${session["siteId"]}";
        <g:remoteFunction controller="creation" action="checkUserExistForSite"  onSuccess='userExistSuccess(data);'
         params="'siteId='+siteId+'&username='+username" />
    }

    //Get OTP
    function userExistSuccess(data) {
        if (data.status == 'No user') {
            $('#username-empty').text('There is no user with this username..');
        } else {
            $('#loginOpen').modal('hide');
            getOTPForSignup();
            $('#forgotPasswordmodal').modal('show');
        }
    }


    function loginOpenWithFunction(functionName, message) {
        functionNameToCall = functionName;
        if (loginHappened) {
            window[functionNameToCall]();
        } else {
            document.getElementById("loginMessage").innerText = message;
            loginOpen();
        }
    }

    function signupWithFunction(functionName, message) {
        functionNameToCall = functionName;
        if (loginHappened) {
            window[functionNameToCall]();
        } else {
            $('#loginOpen').modal('show');
            $('p.error-msg').text('');
            $('#migrateMobileError').text('');
            $('#nameError').text('');
            $('#loginPasswordError').text('');

            document.getElementById("loginMessage").innerText = message;
            // document.getElementById("signupMessage").innerText = message;
            operation = "signup";
        }
    }

    $('#login-form input').keypress(function (e) {
        if (e.which == 13) {
            submitSignIn();
            return false;    //<---- Add this line
        }
    });

    $('#adduser input').keypress(function (e) {
        if (e.which == 13) {
            getOTPForSignup();
            return false;    //<---- Add this line
        }
    });
    $('#adduser input').keypress(function (e) {
        if (e.which == 13) {
            verifyMobileOTP();
            return false;    //<---- Add this line
        }
    });

    //Submit Login
    function submitSignIn() {
        document.signin.username.value = "${session["siteId"]}_" + document.signin.username_temp.value;
        var username = document.signin.username.value;
        var password = document.signin.password.value;
        if (loginName.value == '') {
            $('#usernameError').text('Please enter mobile number or email.');
            $(loginName).focus().addClass('input-error');
            $('#loginPasswordError').text('');
            $('#login-form #password').removeClass('input-error');
        } else if (password == '') {
            $('#loginPasswordError').text('Please enter password.');
            $('#login-form #password').addClass('input-error');
        } else {
            $('.login_signup_loader').show();
            <g:remoteFunction controller="log" action="login"  onSuccess="loginDone(data);" params="'source=web&username='+username+'&password='+password" />
        }
    }

    //Success Login/Signup
    var urlSeparate = location.search.split('?')[location.search.split('?').length - 1];
    var getGroup = urlSeparate.split('=')[0];
    if (getGroup == 'groupId') {
        $('.modal-dialog.modal-dialog-centered button.close').hide();
    }

    function loginDone(data) {
        // Re-enable all buttons and hide loader
        enableButtonWithoutLoader('register');
        enableButtonWithoutLoader('continue');
        $('input[onclick="javascript:formSubmitForgot();"]').prop('disabled', false);

        if ('failed' == data.status) {
            alert(data.message);
        }
        if ("ok" == data.status || "OK" == data.status) {
            $('.loading-icon').removeClass('hidden');
            if(forceRegisterMode=="free"||forceRegisterMode=="paid"){
                window.location.href = "/"+registerBookTitle+"/ebook?bookId="+registerBookId+"&siteName=${params.siteName}";
            }else  if(forceRegisterMode=="quiz"){

                userId=data.username;
                $('#loginOpen').modal('hide');
                $('#signup').modal('hide');
                $("#signupnav").hide();
                document.getElementById("myhomeref").href="/books/home";
                window[returnFunctionCallForQuiz]();
            } else {
                if (functionNameToCall != "") {
                    loginHappened = true;
                    $('#signup').modal('hide');
                    $('#loginOpen').modal('hide');
                    window[functionNameToCall]();
                }
                else if (siteIds != 1) {
                    window.location.href = "/wsLibrary/myLibrary";
                } else if (getGroup == 'groupId') {
                    $('.modal-dialog.modal-dialog-centered button.close').hide();
                    window.location.reload();
                } else {
                    loginHappened = true;
                    window.location.href = "/books/myHome";
                }
            }
        } else {
            $("#loginFailed").show();
        }
    }


    //FORGOT PASSWORD CODES
    //OPENING FORGOT PASSWORD MODAL
    function forgotPasswordOpen() {
        if (loginName.value == "") {
            $(loginName).focus().addClass('input-error');
            $('#usernameError').text('Please enter registered mobile number or email to retrieve the password.');
            $('#login-form #password').removeClass('input-error');
            $('#loginPasswordError').text('');
        } else {
            operation = "forgotpassword";
            $('#forgot-form').trigger('reset');
            getOTPForForgottenPassword(false);
        }
    }

    //CHECKING USER IS EXISTS OR NOT
    function userExistSuccess(data) {
        if (operation == "forgotpassword") {
            $('#forgotPasswordmodal').modal('show');
            $('#loginOpen').modal('hide');
            $('#signup').modal('hide');
        }
    }

    //CALLING GENERATE OTP API
    function getOTPForSignup(resend=false) {
        counter = 60;
        userCheck = false;
        mobileNumber = document.getElementById("migrateMobile").value;
        if ($('#name').val() == '') {
            $('#nameError').text('Please enter name.');
            $('#name').focus().addClass('input-error');
        } else if (mobileNumber == '') {
            if(forceRegisterMode=="free"||forceRegisterMode=="paid"||forceRegisterMode=="quiz") {
                $('#migrateMobileError').text('Please enter 10 digit mobile number.');
            }else{
                $('#migrateMobileError').text('Please enter 10 digit mobile number or email.');
            }
            $('#migrateMobile').focus().addClass('input-error');
        } else if ($('#signup-password').val() == '') {
            $('#signupPasswordError').text('Please enter password.');
            $('#signup-password').focus().addClass('input-error');
        } else {
            if ($('#name,#email,#mobile,#signup-password').val().length != 0) {
                if (/^\d{10}$/.test(mobileNumber) || mobileNumber.includes("@")) {
                    document.getElementById("sentMobileNumbers").innerHTML = mobileNumber;
                    $('.login_signup_loader').show();
                    if (mobileNumber.includes("@")) {
                        <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);'
                        params="'email='+mobileNumber+'&source=web'" />
                        validation = "mobile";
                    } else {
                        <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);'
                        params="'mobile='+mobileNumber+'&resend='+resend+'&userCheck='+userCheck+'&source=web'" />
                        validation = "email";
                    }
                } else {
                    $('#migrateMobileError').text('Please enter 10 digit mobile number or email.');
                    $('#migrateMobile').focus().addClass('input-error');
                    return false
                }
            }
        }

    }

    //Timer
    var interval;
    var counter = 60;

    function resendTimer() {
        clearInterval(interval);
        interval = setInterval(function () {
            counter--;
            // Display 'counter' wherever you want to display it.
            if (counter <= 0) {
                clearInterval(interval);
                $('.timer').html("");
                $('.resendotp').show();
                return;
            } else {
                $('.timer').html("<br>Resend OTP option available in <b>" + counter + "</b> seconds");
            }
        }, 1000);
    }

    //AFTER SUCCESSFULLY RECEIVING OTP
    function OTPReceived(data) {
        // Re-enable GET OTP button and hide loader
        enableButtonWithoutLoader('migrateOtp');
        // Re-enable resend link
        $('.resendotp a').css('pointer-events', 'auto').css('opacity', '1');

        if (data.status == 'OK'){
            $('.ldbar').hide();
            $('#signupOtp,.verification-code,#verifybtn').show();
            $('#migrateOtp').hide();
            $('#migrateMobile').attr('disabled','disabled');
            $('.editPencil').show();
            testr();
        }
    }

    //HANDLE OTP GENERATION FAILURE
    function OTPFailed() {
        enableButtonWithoutLoader('migrateOtp');
        // Re-enable resend link
        $('.resendotp a').css('pointer-events', 'auto').css('opacity', '1');
        alert('Failed to generate OTP. Please try again.');
    }

    //VERIFYING OTP
    function verifyOTP() {
        if (document.getElementById("otpConfirm").value == "") {
            $('#otpConfirm').focus().addClass('input-error');
            $('#ForgotPasswordOTPError').text('Please enter the OTP to proceed.');
        } else {
            // Disable verify OTP button for forgot password and show loader
            $('input[onclick="javascript:verifyOTP();"]').prop('disabled', true);
            $('.login_signup_loader').show();

            var mobileOTP = document.getElementById("otpConfirm").value;
            if (mobileNumber.includes("@"))
                <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);' onFailure='otpVerificationFailed();'
    params="'email='+mobileNumber+'&email_otp='+mobileOTP" />
            else
            <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);' onFailure='otpVerificationFailed();'
    params="'mobile='+mobileNumber+'&mobile_otp='+mobileOTP" />

        }
    }

    //Submit otp for Signup

    function verifyMobileOTP() {
        if (document.getElementById("signupOtp").value == "") {
            $('#signupOtp').focus().addClass('input-error');
            $('#signupOTPError').text('Please enter the OTP to proceed.');
        } else {
            // Disable button and show loader
            disableButtonWithLoader('verifybtn');

            var mobileOTP = document.getElementById("signupOtp").value;

            if (mobileNumber.includes("@"))
                <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);' onFailure='otpVerificationFailed();'
         params="'email='+mobileNumber+'&email_otp='+mobileOTP" />
            else
            <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);' onFailure='otpVerificationFailed();'
         params="'mobile='+mobileNumber+'&mobile_otp='+mobileOTP" />
        }

    }

    //OTP verified

    function otpVerified(data) {
        // Re-enable buttons and hide loader
        enableButtonWithoutLoader('verifybtn');
        $('input[onclick="javascript:verifyOTP();"]').prop('disabled', false);

        $('#signup .close').hide();
        if ("OK" == data.status) {
            if (data.userExists) {
                if (data.allowLogin) {
                    if(forceRegisterMode=="quiz"){
                        userId=data.username;
                        $('#loginOpen').modal('hide');
                        $('#signup').modal('hide');
                        $("#signupnav").hide();
                        document.getElementById("myhomeref").href="/books/home";
                        window[returnFunctionCallForQuiz]();
                    }else {
                        if (functionNameToCall != "") {
                            loginHappened = true;
                            $('#signup').modal('hide');
                            $('#signup').modal('hide');
                            $('#loginOpen').modal('hide');
                            window[functionNameToCall]();
                        }else {
                            window.location.href = "/security/loginmanager";
                            $('.loading-icon').removeClass('hidden');
                        }
                    }
                } else {
                    document.getElementById('limit-signin-verify').innerText = 'The user cannot login as he already logged in from multiple devices';
                }
            }else if("moveCartItems"==functionNameToCall){
                console.log("entering the correct thingy now");
                var oData = new FormData();
                oData.append("site", "Wonderslate");

                if(document.getElementById('shipName') && document.getElementById('shipName').value && document.getElementById('shipName').value.trim() !="" && document.getElementById('shipName').value !=null){
                    if (mobileNumber.includes("@")) {
                        oData.append("email", mobileNumber);
                    } else {
                        oData.append("mobile", mobileNumber);
                    }
                    oData.append("name", document.getElementById('shipName').value);
                    oData.append("state", document.getElementById('shipState').value);
                    oData.append("city", document.getElementById('shipCity').value);
                    oData.append("pincode", document.getElementById('shipPincode').value);
                    oData.append("registeredFrom", "web");
                    oData.append("username", mobileNumber);
                    oData.append("password", mobileNumber);
                    oData.append("siteId", "${session["siteId"]}");
                    var url = "${createLink(controller:'creation',action:'addUser')}";
                    ajaxFunction(url, oData);
                }else{
                    showSignupFields()
                }
            } else {
                showSignupFields()
            }
        } else {
            if (operation == "signup") {
                $('#signupOtp').focus().addClass('input-error');
                $('#signupOTPError').text('Incorrect OTP. Please try again.');
            } else if (operation == "forgotpassword") {
                $('#otpConfirm').focus().addClass('input-error');
                $('#ForgotPasswordOTPError').text('Incorrect OTP. Please try again.');
            }
        }
    }

    //HANDLE OTP VERIFICATION FAILURE
    function otpVerificationFailed() {
        enableButtonWithoutLoader('verifybtn');
        $('input[onclick="javascript:verifyOTP();"]').prop('disabled', false);
        alert('OTP verification failed. Please try again.');
    }


    function showSignupFields(){
        $('.ldbar').hide();
        $('#name').removeClass('d-none');
        $('#signupOtp').addClass('d-none');
        $('#verifybtn').hide();
        $('#continue').show();
        $('.resendotp').hide();
        $('#stateSelectNew').removeClass('d-none');
        $('#districtSelectNew').removeClass('d-none');
        $('#migrateMobile').hide();
        $('.editPencil').hide();
        $('.timer').html("");
        clearInterval(interval);
    }

    //ReSet Password
    function setPassword() {

        if (document.getElementById("setPassword1").value != document.getElementById("setPassword2").value) {
            $('#passwd-error').html('Please make sure password and confirm password are same.');
        } else if (document.getElementById("setPassword1").value == "") {
            $('#passwd-error').html('Please enter the password.')

        } else {
            $('.login_signup_loader').show();
            var oldPassword = "<%= session['userdetails']!=null?session['userdetails'].password:"" %>";
            var password = document.getElementById("setPassword1").value;
            <g:remoteFunction controller="creation" action="updateMigrateUserPassword"  onSuccess='passwordSetCompleted(data);'
    params="'oldPassword='+oldPassword+'&password='+password" />
        }
    }

    //Password set success
    function passwordSetCompleted(data) {
        $('.login_signup_loader').hide();
        window.location.href = "/security/loginmanager";
    }

    function checkLoginAndProceed(destination) {
        <sec:ifLoggedIn>
        $(".loading-icon").removeClass("hidden");
        window.location.href = destination;
        </sec:ifLoggedIn>
        <sec:ifNotLoggedIn>
        nextDestination = destination;
        </sec:ifNotLoggedIn>
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }

    function getOTPForForgottenPassword(resend) {
        counter = 60;
        mobileNumber = document.getElementById("number").value;

        if (/^\d{10}$/.test(mobileNumber) || mobileNumber.includes("@")) {
            document.getElementById("sentMobileNumber").innerHTML = mobileNumber;
            $('.login_signup_loader').show();
            if (mobileNumber.includes("@")) {
                <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='forgotPasswordOTPReceived(data);' onFailure='forgotPasswordOTPFailed();'
            params="'email='+mobileNumber+'&source=web'" />
                validation = "mobile";
            } else {
                <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='forgotPasswordOTPReceived(data);' onFailure='forgotPasswordOTPFailed();'
            params="'mobile='+mobileNumber+'&resend='+resend+'&userCheck=true&source=web'" />
                validation = "email";
            }

        } else {
            $(loginName).focus().addClass('input-error');
            $('#usernameError').text('Please enter 10 digit mobile number or email.');
            $('#login-form #password').removeClass('input-error');
            $('#loginPasswordError').text('');
            return false
        }

    }

    function forgotPasswordOTPReceived(data) {
        var userExist = data.userExist;
        userExistSuccess(userExist);
        $('#otpConfirm').focus();
        $('.resendotp').hide();
        $('.login_signup_loader').hide();
        // Re-enable resend link
        $('.resendotp a').css('pointer-events', 'auto').css('opacity', '1');
        resendTimer();
    }

    //HANDLE FORGOT PASSWORD OTP GENERATION FAILURE
    function forgotPasswordOTPFailed() {
        $('.login_signup_loader').hide();
        // Re-enable resend link
        $('.resendotp a').css('pointer-events', 'auto').css('opacity', '1');
        alert('Failed to generate OTP. Please try again.');
    }

    //setting the default focus
    $('#loginOpen').on('shown.bs.modal', function () {
        $('#number').focus();
    })

    $('#forgotPasswordmodal').on('shown.bs.modal', function () {
        document.getElementById("signInId").reset();
        $('#otpConfirm').focus();
    })

    $('#signup').on('shown.bs.modal', function () {
        $('#name').focus();
        $('#signupOTPError').text('');
    })

    $(loginName).on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#usernameError').text('');
        $('#loginFailed').hide();
    });
    $('#login-form #password').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#loginPasswordError').text('');
        $('#loginFailed').hide();
    });
    $('#name').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#nameError').text('');
    });
    $('#migrateMobile').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#migrateMobileError').text('');
    });
    $('#signup-password').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#signupPasswordError').text('');
    });
    $('#signupOtp').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#signupOTPError').text('');
    });
    $('#otpConfirm').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#ForgotPasswordOTPError').text('');
    });
    $('#nameFromForgot').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#nameErrorFromForgot').text('');
    });
    $('#passwordFromForgot').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#passwordFromForgotError').text('');
    });

    //hide or show password
    $('.hide-password').on('click', function(){
        var $this= $(this), $password_field = $this.prev('input');
        ( 'password' == $password_field.attr('type') ) ? $password_field.attr('type', 'text') : $password_field.attr('type', 'password');
        ( 'visibility' == $this.text() ) ? $this.text('visibility_off') : $this.text('visibility');
    });

    function openRegister(bookType,inputBookTitle,inputBookId){
        registerBookTitle = replaceAll(inputBookTitle,' ','-');
        registerBookId = inputBookId;

        if(bookType=="free"){
            loginOpen();
            forceRegisterMode = "free";
            document.getElementById("signUpTitle").innerText="Get this book for free!";
            document.getElementById("migrateMobile").type="number";
            document.getElementById("migrateMobile").placeholder="Mobile no";
            document.getElementById("signUpSubTitle").innerText="Enter the following information to get this bok.";

        }
        else if(bookType=="paid"){
            loginOpen();
            forceRegisterMode = "paid";
            document.getElementById("migrateMobile").type="number";
            document.getElementById("migrateMobile").placeholder="Mobile no";
            document.getElementById("signUpTitle").innerText="Get a chapter for free!";
            document.getElementById("signUpSubTitle").innerText="Enter the following information to get a free chapter.";

        }
    }

    function openRegisterForQuiz(returnFunctionCall){
        returnFunctionCallForQuiz = returnFunctionCall;
        $('.loading-icon').addClass('hidden');
        signupModal();
        forceRegisterMode = "quiz";
        document.getElementById("signUpTitle").innerText="Well done. You are almost there!";
        document.getElementById("migrateMobile").type="number";
        document.getElementById("migrateMobile").placeholder="Mobile no";
        document.getElementById("signUpSubTitle").innerText="Enter the following information and check your quiz results.";



    }

    function resetError(){
        $("#loginFailed").hide();
    }

    $('#loginOpen').on('hidden.bs.modal', function () {
        $('#number, #login-form #password').removeClass('input-error');
        $('#usernameError, #loginPasswordError').text('');
        $('#loginFailed').hide();
    });

    $('#forgotPasswordmodal').on('hidden.bs.modal', function () {
        $('#otpConfirm, #nameFromForgot, #passwordFromForgot').removeClass('input-error');
        $('#ForgotPasswordOTPError, #nameErrorFromForgot, #passwordFromForgotError').text('');
        $("#forgotTitle").text("Forgot Password");
        $("#forgotSubTitle, #forgot-form").show();
        $("#signUpFromForgot").hide();
    });

    $('#signup').on('hidden.bs.modal', function () {
        $('#name, #migrateMobile, #signup-password, #signupOtp').removeClass('input-error');
        $('#nameError, #migrateMobileError, #signupPasswordError, #signupOTPError').text('');
    });

    // Utility functions for button state management
    function disableButtonWithLoader(buttonId) {
        $('#' + buttonId).prop('disabled', true);
        $('.login_signup_loader').show();
    }

    function enableButtonWithoutLoader(buttonId) {
        $('#' + buttonId).prop('disabled', false);
        $('.login_signup_loader').hide();
    }

    // Wrapper function for resend OTP
    function resendOTP() {
        // Disable resend link temporarily
        $('.resendotp a').css('pointer-events', 'none').css('opacity', '0.5');
        submitMigrateOTP(true);
    }

    // Wrapper function for resend forgot password OTP
    function resendForgotPasswordOTP() {
        // Disable resend link temporarily
        $('.resendotp a').css('pointer-events', 'none').css('opacity', '0.5');
        getOTPForForgottenPassword(true);
    }

    //GENERATING OTP
    function submitMigrateOTP(resendVal){
        mobileNumber = document.getElementById("migrateMobile").value;
        if (!mobMailValidate(mobileNumber)){
            mobileNumber.includes("@") ? $('.mobileErr').text("Please enter valid email"):"";
            $('.mobileErr').show();
            $('#migrateMobile').focus();
            setTimeout(function () {
                $('.mobileErr').hide();
            },2000)
        }else{
            // Disable button and show loader
            disableButtonWithLoader('migrateOtp');

            resend = resendVal;
            userCheck = false;
            $('.ldbar').show();
            if (mobileNumber.includes("@")) {
                <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);' onFailure='OTPFailed();'
                        params="'email='+mobileNumber+'&source=web'" />
                validation = "mobile";
            } else {
                <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);' onFailure='OTPFailed();'
                        params="'mobile='+mobileNumber+'&resend='+resend+'&userCheck='+userCheck+'&source=web'" />
                validation = "email";
            }
        }
    }

    function mobMailValidate(input) {
        var emailRegex = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        var mobileRegex = /^\d{10}$/;

        if (emailRegex.test(input)) {
            return true;
        } else if (mobileRegex.test(input)) {
           return true;
        } else {
            return false;
        }
    }
    //Timer
    function testr() {
        var counter = 60;
        clearInterval(interval);
        interval = setInterval(function () {
            counter--;
            // Display 'counter' wherever you want to display it.
            if (counter <= 0) {
                clearInterval(interval);
                $('.timer').html("");
                $('.resendotp').show();
                return;
            } else {
                $('.timer').show();
                $('.timer').text(counter+"s");
            }
        }, 1000);
    }
    function openAccessCodePage(){
        window.location.href ="/wsLibrary/accessCode";
    }

    //STATES & DIST OBJ
    var stateObjectNew = {
        "Andhra Pradesh": [
            "Anantapur",
            "Chittoor",
            "East Godavari",
            "Guntur",
            "Krishna",
            "Kurnool",
            "Nellore",
            "Prakasam",
            "Srikakulam",
            "Visakhapatnam",
            "Vizianagaram",
            "West Godavari",
            "YSR Kadapa"
        ],
        "Andaman and Nicobar Island(UT)":[
            "South Andaman",
            "North and Middle Andaman",
            "Nicobar",
        ],
        "Arunachal Pradesh": [
            "Tawang",
            "West Kameng",
            "East Kameng",
            "Papum Pare",
            "Kurung Kumey",
            "Kra Daadi",
            "Lower Subansiri",
            "Upper Subansiri",
            "West Siang",
            "East Siang",
            "Siang",
            "Upper Siang",
            "Lower Siang",
            "Lower Dibang Valley",
            "Dibang Valley",
            "Anjaw",
            "Lohit",
            "Namsai",
            "Changlang",
            "Tirap",
            "Longding"
        ],
        "Assam": [
            "Baksa",
            "Barpeta",
            "Biswanath",
            "Bongaigaon",
            "Cachar",
            "Charaideo",
            "Chirang",
            "Darrang",
            "Dhemaji",
            "Dhubri",
            "Dibrugarh",
            "Goalpara",
            "Golaghat",
            "Hailakandi",
            "Hojai",
            "Jorhat",
            "Kamrup Metropolitan",
            "Kamrup",
            "Karbi Anglong",
            "Karimganj",
            "Kokrajhar",
            "Lakhimpur",
            "Majuli",
            "Morigaon",
            "Nagaon",
            "Nalbari",
            "Dima Hasao",
            "Sivasagar",
            "Sonitpur",
            "South Salmara-Mankachar",
            "Tinsukia",
            "Udalguri",
            "West Karbi Anglong"
        ],
        "Bihar": [
            "Araria",
            "Arwal",
            "Aurangabad",
            "Banka",
            "Begusarai",
            "Bhagalpur",
            "Bhojpur",
            "Buxar",
            "Darbhanga",
            "East Champaran (Motihari)",
            "Gaya",
            "Gopalganj",
            "Jamui",
            "Jehanabad",
            "Kaimur (Bhabua)",
            "Katihar",
            "Khagaria",
            "Kishanganj",
            "Lakhisarai",
            "Madhepura",
            "Madhubani",
            "Munger (Monghyr)",
            "Muzaffarpur",
            "Nalanda",
            "Nawada",
            "Patna",
            "Purnia (Purnea)",
            "Rohtas",
            "Saharsa",
            "Samastipur",
            "Saran",
            "Sheikhpura",
            "Sheohar",
            "Sitamarhi",
            "Siwan",
            "Supaul",
            "Vaishali",
            "West Champaran"
        ],
        "Chandigarh (UT)": [
            "Chandigarh"
        ],
        "Chhattisgarh": [
            "Balod",
            "Baloda Bazar",
            "Balrampur",
            "Bastar",
            "Bemetara",
            "Bijapur",
            "Bilaspur",
            "Dantewada (South Bastar)",
            "Dhamtari",
            "Durg",
            "Gariyaband",
            "Janjgir-Champa",
            "Jashpur",
            "Kabirdham (Kawardha)",
            "Kanker (North Bastar)",
            "Kondagaon",
            "Korba",
            "Korea (Koriya)",
            "Mahasamund",
            "Mungeli",
            "Narayanpur",
            "Raigarh",
            "Raipur",
            "Rajnandgaon",
            "Sukma",
            "Surajpur  ",
            "Surguja"
        ],
        "Dadra and Nagar Haveli (UT)": [
            "Dadra & Nagar Haveli"
        ],
        "Daman and Diu (UT)": [
            "Daman",
            "Diu"
        ],
        "Delhi (NCT)": [
            "Central Delhi",
            "East Delhi",
            "New Delhi",
            "North Delhi",
            "North East  Delhi",
            "North West  Delhi",
            "Shahdara",
            "South Delhi",
            "South East Delhi",
            "South West  Delhi",
            "West Delhi"
        ],
        "Goa": [
            "North Goa",
            "South Goa"
        ],
        "Gujarat": [
            "Ahmedabad",
            "Amreli",
            "Anand",
            "Aravalli",
            "Banaskantha (Palanpur)",
            "Bharuch",
            "Bhavnagar",
            "Botad",
            "Chhota Udepur",
            "Dahod",
            "Dangs (Ahwa)",
            "Devbhoomi Dwarka",
            "Gandhinagar",
            "Gir Somnath",
            "Jamnagar",
            "Junagadh",
            "Kachchh",
            "Kheda (Nadiad)",
            "Mahisagar",
            "Mehsana",
            "Morbi",
            "Narmada (Rajpipla)",
            "Navsari",
            "Panchmahal (Godhra)",
            "Patan",
            "Porbandar",
            "Rajkot",
            "Sabarkantha (Himmatnagar)",
            "Surat",
            "Surendranagar",
            "Tapi (Vyara)",
            "Vadodara",
            "Valsad"
        ],
        "Haryana": [
            "Ambala",
            "Bhiwani",
            "Charkhi Dadri",
            "Faridabad",
            "Fatehabad",
            "Gurgaon",
            "Hisar",
            "Jhajjar",
            "Jind",
            "Kaithal",
            "Karnal",
            "Kurukshetra",
            "Mahendragarh",
            "Mewat",
            "Palwal",
            "Panchkula",
            "Panipat",
            "Rewari",
            "Rohtak",
            "Sirsa",
            "Sonipat",
            "Yamunanagar"
        ],
        "Himachal Pradesh": [
            "Bilaspur",
            "Chamba",
            "Hamirpur",
            "Kangra",
            "Kinnaur",
            "Kullu",
            "Lahaul &amp; Spiti",
            "Mandi",
            "Shimla",
            "Sirmaur (Sirmour)",
            "Solan",
            "Una"
        ],
        "Jammu and Kashmir": [
            "Anantnag",
            "Bandipore",
            "Baramulla",
            "Budgam",
            "Doda",
            "Ganderbal",
            "Jammu",
            "Kathua",
            "Kishtwar",
            "Kulgam",
            "Kupwara",
            "Poonch",
            "Pulwama",
            "Rajouri",
            "Ramban",
            "Reasi",
            "Samba",
            "Shopian",
            "Srinagar",
            "Udhampur"
        ],
        "Jharkhand": [
            "Bokaro",
            "Chatra",
            "Deoghar",
            "Dhanbad",
            "Dumka",
            "East Singhbhum",
            "Garhwa",
            "Giridih",
            "Godda",
            "Gumla",
            "Hazaribag",
            "Jamtara",
            "Khunti",
            "Koderma",
            "Latehar",
            "Lohardaga",
            "Pakur",
            "Palamu",
            "Ramgarh",
            "Ranchi",
            "Sahibganj",
            "Seraikela-Kharsawan",
            "Simdega",
            "West Singhbhum"
        ],
        "Karnataka": [
            "Bagalkot",
            "Ballari (Bellary)",
            "Belagavi (Belgaum)",
            "Bengaluru (Bangalore) Rural",
            "Bengaluru (Bangalore) Urban",
            "Bidar",
            "Chamarajanagar",
            "Chikballapur",
            "Chikkamagaluru (Chikmagalur)",
            "Chitradurga",
            "Dakshina Kannada",
            "Davangere",
            "Dharwad",
            "Gadag",
            "Hassan",
            "Haveri",
            "Kalaburagi (Gulbarga)",
            "Kodagu",
            "Kolar",
            "Koppal",
            "Mandya",
            "Mysuru (Mysore)",
            "Raichur",
            "Ramanagara",
            "Shivamogga (Shimoga)",
            "Tumakuru (Tumkur)",
            "Udupi",
            "Uttara Kannada (Karwar)",
            "Vijayapura (Bijapur)",
            "Yadgir"
        ],
        "Kerala": [
            "Alappuzha",
            "Ernakulam",
            "Idukki",
            "Kannur",
            "Kasaragod",
            "Kollam",
            "Kottayam",
            "Kozhikode",
            "Malappuram",
            "Palakkad",
            "Pathanamthitta",
            "Thiruvananthapuram",
            "Thrissur",
            "Wayanad"
        ],
        "Ladakh (UT)":[
            "Leh",
            "Kargil"
        ],
        "Lakshadweep (UT)": [
            "Agatti",
            "Amini",
            "Androth",
            "Bithra",
            "Chethlath",
            "Kavaratti",
            "Kadmath",
            "Kalpeni",
            "Kilthan",
            "Minicoy"
        ],
        "Madhya Pradesh": [
            "Agar Malwa",
            "Alirajpur",
            "Anuppur",
            "Ashoknagar",
            "Balaghat",
            "Barwani",
            "Betul",
            "Bhind",
            "Bhopal",
            "Burhanpur",
            "Chhatarpur",
            "Chhindwara",
            "Damoh",
            "Datia",
            "Dewas",
            "Dhar",
            "Dindori",
            "Guna",
            "Gwalior",
            "Harda",
            "Hoshangabad",
            "Indore",
            "Jabalpur",
            "Jhabua",
            "Katni",
            "Khandwa",
            "Khargone",
            "Mandla",
            "Mandsaur",
            "Morena",
            "Narsinghpur",
            "Neemuch",
            "Panna",
            "Raisen",
            "Rajgarh",
            "Ratlam",
            "Rewa",
            "Sagar",
            "Satna",
            "Sehore",
            "Seoni",
            "Shahdol",
            "Shajapur",
            "Sheopur",
            "Shivpuri",
            "Sidhi",
            "Singrauli",
            "Tikamgarh",
            "Ujjain",
            "Umaria",
            "Vidisha"
        ],
        "Maharashtra": [
            "Ahmednagar",
            "Akola",
            "Amravati",
            "Aurangabad",
            "Beed",
            "Bhandara",
            "Buldhana",
            "Chandrapur",
            "Dhule",
            "Gadchiroli",
            "Gondia",
            "Hingoli",
            "Jalgaon",
            "Jalna",
            "Kolhapur",
            "Latur",
            "Mumbai City",
            "Mumbai Suburban",
            "Nagpur",
            "Nanded",
            "Nandurbar",
            "Nashik",
            "Osmanabad",
            "Palghar",
            "Parbhani",
            "Pune",
            "Raigad",
            "Ratnagiri",
            "Sangli",
            "Satara",
            "Sindhudurg",
            "Solapur",
            "Thane",
            "Wardha",
            "Washim",
            "Yavatmal"
        ],
        "Manipur": [
            "Bishnupur",
            "Chandel",
            "Churachandpur",
            "Imphal East",
            "Imphal West",
            "Jiribam",
            "Kakching",
            "Kamjong",
            "Kangpokpi",
            "Noney",
            "Pherzawl",
            "Senapati",
            "Tamenglong",
            "Tengnoupal",
            "Thoubal",
            "Ukhrul"
        ],
        "Meghalaya": [
            "East Garo Hills",
            "East Jaintia Hills",
            "East Khasi Hills",
            "North Garo Hills",
            "Ri Bhoi",
            "South Garo Hills",
            "South West Garo Hills ",
            "South West Khasi Hills",
            "West Garo Hills",
            "West Jaintia Hills",
            "West Khasi Hills"
        ],
        "Mizoram": [
            "Aizawl",
            "Champhai",
            "Kolasib",
            "Lawngtlai",
            "Lunglei",
            "Mamit",
            "Saiha",
            "Serchhip"
        ],
        "Nagaland": [
            "Dimapur",
            "Kiphire",
            "Kohima",
            "Longleng",
            "Mokokchung",
            "Mon",
            "Peren",
            "Phek",
            "Tuensang",
            "Wokha",
            "Zunheboto"
        ],
        "Odisha": [
            "Angul",
            "Balangir",
            "Balasore",
            "Bargarh",
            "Bhadrak",
            "Boudh",
            "Cuttack",
            "Deogarh",
            "Dhenkanal",
            "Gajapati",
            "Ganjam",
            "Jagatsinghapur",
            "Jajpur",
            "Jharsuguda",
            "Kalahandi",
            "Kandhamal",
            "Kendrapara",
            "Kendujhar (Keonjhar)",
            "Khordha",
            "Koraput",
            "Malkangiri",
            "Mayurbhanj",
            "Nabarangpur",
            "Nayagarh",
            "Nuapada",
            "Puri",
            "Rayagada",
            "Sambalpur",
            "Sonepur",
            "Sundargarh"
        ],
        "Puducherry (UT)": [
            "Karaikal",
            "Mahe",
            "Pondicherry",
            "Yanam"
        ],
        "Punjab": [
            "Amritsar",
            "Barnala",
            "Bathinda",
            "Faridkot",
            "Fatehgarh Sahib",
            "Fazilka",
            "Ferozepur",
            "Gurdaspur",
            "Hoshiarpur",
            "Jalandhar",
            "Kapurthala",
            "Ludhiana",
            "Mansa",
            "Moga",
            "Muktsar",
            "Nawanshahr (Shahid Bhagat Singh Nagar)",
            "Pathankot",
            "Patiala",
            "Rupnagar",
            "Sahibzada Ajit Singh Nagar (Mohali)",
            "Sangrur",
            "Tarn Taran"
        ],
        "Rajasthan": [
            "Ajmer",
            "Alwar",
            "Banswara",
            "Baran",
            "Barmer",
            "Bharatpur",
            "Bhilwara",
            "Bikaner",
            "Bundi",
            "Chittorgarh",
            "Churu",
            "Dausa",
            "Dholpur",
            "Dungarpur",
            "Hanumangarh",
            "Jaipur",
            "Jaisalmer",
            "Jalore",
            "Jhalawar",
            "Jhunjhunu",
            "Jodhpur",
            "Karauli",
            "Kota",
            "Nagaur",
            "Pali",
            "Pratapgarh",
            "Rajsamand",
            "Sawai Madhopur",
            "Sikar",
            "Sirohi",
            "Sri Ganganagar",
            "Tonk",
            "Udaipur"
        ],
        "Sikkim": [
            "East Sikkim",
            "North Sikkim",
            "South Sikkim",
            "West Sikkim"
        ],
        "Tamil Nadu": [
            "Ariyalur",
            "Chennai",
            "Coimbatore",
            "Cuddalore",
            "Dharmapuri",
            "Dindigul",
            "Erode",
            "Kanchipuram",
            "Kanyakumari",
            "Karur",
            "Krishnagiri",
            "Madurai",
            "Nagapattinam",
            "Namakkal",
            "Nilgiris",
            "Perambalur",
            "Pudukkottai",
            "Ramanathapuram",
            "Salem",
            "Sivaganga",
            "Thanjavur",
            "Theni",
            "Thoothukudi (Tuticorin)",
            "Tiruchirappalli",
            "Tirunelveli",
            "Tiruppur",
            "Tiruvallur",
            "Tiruvannamalai",
            "Tiruvarur",
            "Vellore",
            "Viluppuram",
            "Virudhunagar"
        ],
        "Telangana": [
            "Adilabad",
            "Bhadradri Kothagudem",
            "Hyderabad",
            "Jagtial",
            "Jangaon",
            "Jayashankar Bhoopalpally",
            "Jogulamba Gadwal",
            "Kamareddy",
            "Karimnagar",
            "Khammam",
            "Komaram Bheem Asifabad",
            "Mahabubabad",
            "Mahabubnagar",
            "Mancherial",
            "Medak",
            "Medchal",
            "Nagarkurnool",
            "Nalgonda",
            "Nirmal",
            "Nizamabad",
            "Peddapalli",
            "Rajanna Sircilla",
            "Rangareddy",
            "Sangareddy",
            "Siddipet",
            "Suryapet",
            "Vikarabad",
            "Wanaparthy",
            "Warangal (Rural)",
            "Warangal (Urban)",
            "Yadadri Bhuvanagiri"
        ],
        "Tripura": [
            "Dhalai",
            "Gomati",
            "Khowai",
            "North Tripura",
            "Sepahijala",
            "South Tripura",
            "Unakoti",
            "West Tripura"
        ],
        "Uttarakhand": [
            "Almora",
            "Bageshwar",
            "Chamoli",
            "Champawat",
            "Dehradun",
            "Haridwar",
            "Nainital",
            "Pauri Garhwal",
            "Pithoragarh",
            "Rudraprayag",
            "Tehri Garhwal",
            "Udham Singh Nagar",
            "Uttarkashi"
        ],
        "Uttar Pradesh": [
            "Agra",
            "Aligarh",
            "Allahabad",
            "Ambedkar Nagar",
            "Amethi (Chatrapati Sahuji Mahraj Nagar)",
            "Amroha (J.P. Nagar)",
            "Auraiya",
            "Azamgarh",
            "Baghpat",
            "Bahraich",
            "Ballia",
            "Balrampur",
            "Banda",
            "Barabanki",
            "Bareilly",
            "Basti",
            "Bhadohi",
            "Bijnor",
            "Budaun",
            "Bulandshahr",
            "Chandauli",
            "Chitrakoot",
            "Deoria",
            "Etah",
            "Etawah",
            "Faizabad",
            "Farrukhabad",
            "Fatehpur",
            "Firozabad",
            "Gautam Buddha Nagar",
            "Ghaziabad",
            "Ghazipur",
            "Gonda",
            "Gorakhpur",
            "Hamirpur",
            "Hapur (Panchsheel Nagar)",
            "Hardoi",
            "Hathras",
            "Jalaun",
            "Jaunpur",
            "Jhansi",
            "Kannauj",
            "Kanpur Dehat",
            "Kanpur Nagar",
            "Kanshiram Nagar (Kasganj)",
            "Kaushambi",
            "Kushinagar (Padrauna)",
            "Lakhimpur - Kheri",
            "Lalitpur",
            "Lucknow",
            "Maharajganj",
            "Mahoba",
            "Mainpuri",
            "Mathura",
            "Mau",
            "Meerut",
            "Mirzapur",
            "Moradabad",
            "Muzaffarnagar",
            "Pilibhit",
            "Pratapgarh",
            "RaeBareli",
            "Rampur",
            "Saharanpur",
            "Sambhal (Bhim Nagar)",
            "Sant Kabir Nagar",
            "Shahjahanpur",
            "Shamali (Prabuddh Nagar)",
            "Shravasti",
            "Siddharth Nagar",
            "Sitapur",
            "Sonbhadra",
            "Sultanpur",
            "Unnao",
            "Varanasi"
        ],
        "West Bengal": [
            "Alipurduar",
            "Bankura",
            "Birbhum",
            "Burdwan (Bardhaman)",
            "Cooch Behar",
            "Dakshin Dinajpur (South Dinajpur)",
            "Darjeeling",
            "Hooghly",
            "Howrah",
            "Jalpaiguri",
            "Kalimpong",
            "Kolkata",
            "Malda",
            "Murshidabad",
            "Nadia",
            "North 24 Parganas",
            "Paschim Medinipur (West Medinipur)",
            "Purba Medinipur (East Medinipur)",
            "Purulia",
            "South 24 Parganas",
            "Uttar Dinajpur (North Dinajpur)"
        ]

    }
    // Change State and District Function
    for (var state in stateObjectNew) {
        stateSel.options[stateSel.options.length] = new Option(state, state);
    }
    stateSel.onchange = function () {
        districtSel.length = 1; // remove all options bar first
        if (this.selectedIndex < 1) return; // done
        var district = stateObjectNew[stateSel.value];
        for (var i = 0; i < district.length; i++) {
            districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
        }
    }
    // stateSel.onchange(); // reset in case page is reloaded
    stateSel.onload = function () {
        districtSel.length = 1; // remove all options bar first
        if (this.selectedIndex < 1) return; // done
        var district = stateObjectNew[stateSel.value];
        for (var i = 0; i < district.length; i++) {
            districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
        }
    }
    stateSel.onload();

    function proceed(){
        userName = $('#name').val();
        if (userName.length>0){
            // Disable continue button and show loader
            disableButtonWithLoader('continue');
            formSubmit(); //SIGN-UP [USER DOESN'T EXIST]
        }else{
            $('.nameErr').show();
            setTimeout(function (){
                $('.nameErr').hide();
            },2000)
        }
    }
    //ALLOWING USER TO EDIT MOBILE NUMBER
    function editNumber(){
        $('#migrateMobile').removeAttr('disabled');
        $('#verifybtn').hide();
        $('#migrateOtp').show();
        $('#signupOtp').hide();
        $('.timer').html("");
        $('.resendotp').hide();
        $('#signupOtp').val('');
        $('.otpErr').hide();
        clearInterval(interval);
    }
    const migrateMobile = document.getElementById("migrateMobile");

    migrateMobile.addEventListener("keydown", function(event) {
        // Check if the 'Enter' key (key code 13) was pressed
        if (event.key === "Enter" && document.activeElement === migrateMobile) {
            event.preventDefault();
            submitMigrateOTP(false);
        }
    });
    if("${params.mode}"=="loginform"){
        document.addEventListener('DOMContentLoaded',loginOpen)
    }
</script>

