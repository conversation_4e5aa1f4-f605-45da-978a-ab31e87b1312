<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/privatelabel/navheader_new"></g:render>
<asset:stylesheet href="landingpage/libwonderStyles.css"/>
<asset:stylesheet href="whitelabel/additionalStyles.css"/>

<div class="page-main-wrapper mdl-js information-admin p-5 cuetAcademics">
    <div class="container-fluid">
        <div class="curve-bg">
            <img src="${assetPath(src: 'libwonder/curve-bg.svg')}">
        </div>
        <section class="banner_wrap p-4 p-md-5 my-5">
            <div class="row mt-5">
                <div class="banner_info col-12 col-md-6">
                    <h1 class="titleRowThree">
                        <strong style="margin-right: 5px;margin-left: 5px;"> Knimbus</strong>  - <br> A versatile single solution platform to access your library from anywhere, anytime.
                    </h1>
                    <h3 class="titleRowTwo">Transform your Library for the Digital Future!</h3>
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start mt-3 pt-3">
                        <sec:ifNotLoggedIn>
                            <button class="btn login-btn mr-3 cuetLogin" onclick="javascript:loginOpen()">Login</button>
                            <button class="btn signup-btn" onclick="javascript:signupModal()">Sign Up</button>
                        </sec:ifNotLoggedIn>

                        <sec:ifLoggedIn>
                            <a class="btn btn-lg signup-btn" href="/wsLibrary/myLibrary?mode=mybooks">Go to My Books</a>
                        </sec:ifLoggedIn>
                    </div>
                    <sec:ifNotLoggedIn>
                        <div class="d-flex align-items-center justify-content-center d-md-none mt-4">
                            <% if(showLibrary){%>
                            <a class="btn btn-lg signup-btn" href="/wsLibrary/myLibrary?mode=mybooks">Go to My Books</a>
                            <%}%>
                        </div>
                    </sec:ifNotLoggedIn>
                </div>
                <div class="banner_img col-12 col-md-6">
                    <img src="${assetPath(src: 'libwonder/lib-banner.png')}" style="width: 75%;">
                </div>
            </div>
        </section>
    </div>
</div>
<g:render template="/privatelabel/footer_new"></g:render>

