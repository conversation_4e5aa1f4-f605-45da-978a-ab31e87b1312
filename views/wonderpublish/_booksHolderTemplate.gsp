
<section class="store">
  <div class="loading-icon">
    <div class="loader-wrapper">
      <div class="loader">Loading</div>
    </div>
  </div>
  <div class="container">
    <div class="row">

      <div class="col-12 col-md-4 col-lg-3 mt-2 mt-sm-4 mt-md-0">
       <div class="quick-sortmenu">
        <form>
          <input type="text" name="search" placeholder="Search" class="search typeahead" id="search-book" autocomplete="off" value="${params.searchString}">
          <i class="searchIcon material-icons">search</i>
        </form>
        <div class="tab-menu mt-4">


            <% if("books".equals(grailsApplication.config.grails.appServer.default)){%>
          <ul class="nav nav-pills justify-content-between" role="tablist" id="materialtabs">
            <%if(categories!=null&&categories.indexOf("School")!=-1){%>
            <li class="nav-item">
              <a class="nav-link <%= (categories!=null&&categories.startsWith("School"))?"active":"" %> " data-toggle="pill" href="#School" id="tabschool">
                <i class="material-icons">school</i>
                <span>School</span>
              </a>
            </li>
            <%}%>
            <%if(categories!=null&&categories.indexOf("Competitive Exams")!=-1){%>
            <li class="nav-item ">
              <a class="nav-link <%= (categories!=null&&categories.startsWith("College"))?"active":"" %>" data-toggle="pill" href="#College" id="tabcollege">
                <i class="material-icons">account_balance</i>
                <span>College</span>
              </a>
              </a>
            </li>
            <%}%>
            <%if(categories!=null&&categories.indexOf("Healthcare")!=-1){%>
            <li class="nav-item ">
              <a class="nav-link <%= (categories!=null&&categories.startsWith("Heathcare"))?"active":"" %>" data-toggle="pill" href="#Heathcare" id="tabhealthcare">
                <i class="material-icons">local_hospital</i>
                <span>Healthcare11</span>
              </a>
            </li>
            <%}%>
            <%if(categories!=null&&categories.indexOf("Competitive Exams")!=-1){%>
            <li class="nav-item ">
              <a class="nav-link <%= (categories!=null&&categories.startsWith("Competitive Exams"))?"active":"" %> " data-toggle="pill" href="#CompetitiveExams" id="tabcompetitiveexams">
                <i class="material-icons">assignment</i>
                <span>Tests</span>
              </a>
            </li>
            <%}%>
          </ul>
          <%} else if("eutkarsh".equals(grailsApplication.config.grails.appServer.default)){ %>
          <ul class="nav nav-pills justify-content-between" role="tablist" id="materialtabs">
          <%if(categories!=null&&categories.indexOf("School")!=-1){%>
          <li class="nav-item">
            <a class="nav-link <%= (categories!=null&&categories.startsWith("School"))?"active":"" %> " data-toggle="pill" href="#School" id="tabschool">
              <i class="material-icons">school</i>
              <span>School</span>
            </a>
          </li>
          <%}%>
          <%if(categories!=null&&categories.indexOf("Competitive Exams")!=-1){%>
          <li class="nav-item ">
            <a class="nav-link <%= (categories!=null&&categories.startsWith("Competitive Exams"))?"active":"" %> " data-toggle="pill" href="#CompetitiveExams" id="tabcompetitiveexams">
              <i class="material-icons">assignment</i>
              <span style="width: auto">Competitive Exams</span>
            </a>
          </li>
          <%}%>
        </ul>
          <%}%>




          <div class="filter " id="filterDiv">
            <form class="d-none d-sm-block">

              <!--Class-->
              <label>Class</label>
              <div>
                <button type="button" class="btn btn-default dropdown-toggle class-selection-btn class-selection-btns disabled" id="class-selection-btn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select</button>

                <ul class="dropdown-menu class-selection-dropdown" id="gradedropdown">

                </ul>
              </div>

              <!--Subject-->
              <label>Subject</label>
              <div>
                <button type="button" class="btn btn-default dropdown-toggle class-selection-btn subject-selection-btn disabled" id="subject-selection-btn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select</button>

                <ul class="dropdown-menu class-selection-dropdown" id="subjectdropdown">

                </ul>
              </div>

            </form>
          </div>

          <div class="feature-books mt-4 mb-4 d-none ">
            <div class="card" id="featuredBooks">
              <div class="content-header d-flex align-items-center justify-content-between">
                <h4>Featured eBooks</h4>
                <a href="#">View All</a>
              </div>
              <div class="topSchoolBooks owl-carousel owl-theme">
                <div class="item">
                  <div class="image-wrapper">
                    <img src="${assetPath(src: 'landingpageImages/book1.png')}">
                    <h3>WS eBooks +</h3>
                  </div>
                  <div class="content-wrapper">
                    <h3>Practice-KCET Biology
                    Previous Year Question Papers</h3>
                    <p class="sub-name"><span>by</span> iWS Publications</p>
                    <p class="complete">COMPLETE BOOK</p>
                    <p class="price">&#x20b9 190<span>200</span></p>
                  </div>
                </div>
                <div class="item">
                  <div class="image-wrapper">
                    <img src="${assetPath(src: 'landingpageImages/book2.png')}">
                    <h3>WS eBooks +</h3>
                  </div>
                  <div class="content-wrapper">
                    <h3>Practice-KCET Biology
                    Previous Year Question Papers</h3>
                    <p class="sub-name"><span>by</span> iWS Publications</p>
                    <p class="complete">COMPLETE BOOK</p>
                    <p class="price">&#x20b9 150<span>200</span></p>
                  </div>
                </div>
                <div class="item">
                  <div class="image-wrapper">
                    <img src="${assetPath(src: 'landingpageImages/NEET.png')}">
                    <h3>WS eBooks +</h3>
                  </div>
                  <div class="content-wrapper">
                    <h3>Practice-KCET Biology
                    Previous Year Question Papers</h3>
                    <p><span>by</span> iWS Publications</p>
                    <p class="complete">COMPLETE BOOK</p>
                    <p class="price">&#x20b9 130<span>200</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mobile-filter mt-4 d-flex d-sm-none justify-content-between">
            <div class="mob-sort">
                <button class="d-flex" data-toggle="modal" data-target="#filter-mode">
                   <i class="material-icons">filter_list</i>
                    <span class="ml-2">Filter</span>
                </button>

            </div>
            <div class="mob-filter">
                <div class="d-flex" data-toggle="modal" data-target="#sort-mode">
                    %{--<span class="mr-2">Sort</span>--}%
                    %{--<i class="material-icons">sort</i>--}%
                </div>
              <div class="modal fade" id="sort-modes">
                <div class="modal-dialog modal-sm modal-dialog-centered">
                  <div class="modal-content">

                    <!-- Modal Header -->
                    <div class="modal-header justify-content-center">
                      <h4 class="modal-title">Sort by</h4>

                    </div>

                    <!-- Modal body -->
                    <div class="modal-body">

                    </div>

                    <!-- Modal footer -->
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>

                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
        </div>
      </div>

      <div class="col-md-8 col-lg-8 ml-lg-4 bl-left mt-4 mt-sm-0">
        <div class="tab-content">
          <div class="quick-sort mt-2 pb-2 mb-2">
            <ul class="quick-nav nav" id="subtabs">
              <button class="show-more">Show more</button>
              <button class="show-less d-none">Show Less</button>
            </ul>
          </div>
          <%if(categories!=null&&categories.indexOf("School")!=-1){%>
          <div class="container tab-pane <%= (categories!=null&&categories.startsWith("School"))?"active":"" %>" id="School">
            <div class="d-flex justify-content-between align-items-center">
               <h3 id="tab-head">School eBooks</h3>
                <div class="custom-select-ws">
                  %{--<select name="sort">--}%
                    %{--<option selected>Relevance</option>--}%
                    %{--<option value="c1">Class1</option>--}%
                    %{--<option value="c2">Class2</option>--}%
                    %{--<option value="c3">class3</option>--}%
                  %{--</select>--}%
                </div>
              </div>
            <div class="row mt-4" id="content-data-books-School"></div>
          </div>
          <%}%>
          <%if(categories!=null&&categories.indexOf("Healthcare")!=-1){%>
          <div class="container tab-pane <%= (categories!=null&&categories.startsWith("Healthcare"))?"active":"" %>" id="Healthcare">
            <div class="d-flex justify-content-between align-items-center">
              <h3 id="tab-head">Healthcare eBooks</h3>
              <div class="custom-select-ws">
                %{--<select name="sort">--}%
                %{--<option selected>Relevance</option>--}%
                %{--<option value="c1">Class1</option>--}%
                %{--<option value="c2">Class2</option>--}%
                %{--<option value="c3">class3</option>--}%
                %{--</select>--}%
              </div>
            </div>
            <div class="row mt-4" id="content-data-books-Healthcare"></div>
          </div>
          <%}%>
          <%if(categories!=null&&categories.indexOf("College")!=-1){%>
          <div class="container tab-pane fade  <%= (categories!=null&&categories.startsWith("College"))?"active":"" %>" id="College">
            <div class="d-flex justify-content-between align-items-center">
            <h3 id="tab-head">College eBooks</h3>
            <div class="custom-select-ws">
              %{--<select name="sort">--}%
                %{--<option selected>Relevance</option>--}%
                %{--<option value="c1">Class1</option>--}%
                %{--<option value="c2">Class2</option>--}%
                %{--<option value="c3">class3</option>--}%
              %{--</select>--}%
            </div>
          </div>

            <div class="row mt-4" id="content-data-books-College">

            </div>
          </div>
          <%}%>
          <%if(categories!=null&&categories.indexOf("Competitive Exams")!=-1){%>
              <div  class="container tab-pane <%= (categories!=null&&categories.startsWith("Competitive Exams"))?"active":"" %>" id="CompetitiveExams">
                  <ul class="quick-nav nav" id="subtabs"></ul>
                  <div class="d-flex justify-content-between align-items-center">
                    <% if("eutkarsh".equals(grailsApplication.config.grails.appServer.default)){%>

                      <h3 id="tab-head">Competitive Exams</h3>
                    <%}else{%>
                    <h3 id="tab-head">Tests</h3>
                    <%}%>
                      <div class="custom-select-ws">
                      %{--<select name="sort">--}%
                          %{--<option selected>Relevance</option>--}%
                          %{--<option value="c1">Class1</option>--}%
                          %{--<option value="c2">Class2</option>--}%
                          %{--<option value="c3">class3</option>--}%
                      %{--</select>--}%
                      </div>
                  </div>
          <div class="row mt-4" id="content-data-books-CompetitiveExams"></div>
             </div>
          <%}%>
        </div>
      </div>
    </div>
  </div>
  </div>
</section>

<script>

</script>