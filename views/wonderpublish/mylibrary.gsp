<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<section class="main-content">
  <div class="container-fluid main-content-container">
      <div class="col-md-12 col-sm-12">
        <div id="search-error" class='no-books'>
          <div class='no-books-available'>
            <div class='no-book-wrapper'>
              <img class='book-image' src='${assetPath(src: 'desert_plants.png')}' />
            </div>
            <p>Please <a href='/funlearn/signIn' class="plz-login">Login!</a> to add &amp; read eBooks here.
            </p>
          </div>
        </div>
        <ul class="books" id="content-data-books"></ul>
      </div>
    </div>
  </div>
</section>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
</body>
<script>
  $('.main-content').css({
    'height' : '53%'
  });
  $('.main-content-container').css({
    'position' : 'relative',
    'top' : '50%',
    'transform' : 'translateY(-50%)'
  });
</script>
</html>