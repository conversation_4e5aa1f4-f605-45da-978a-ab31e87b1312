<script>
    function populateFlashcardSection(cData) {
        var cStr = "";
        for (var i = 0; i < cData.length; ++i) {
            var showShare = false;
            var data = cData[i];
            getFlashCards(data.id);
        }
    }

    function getFlashCards(id){

        <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='displayFlashCards(data);' params="'resId='+id"/>
    }

    function displayFlashCards(data){
        var keyValues=data.keyValues;
        var cStr="";
        var position = 1;

        $("#carousel-btn-left, #carousel-btn-left-modal").off("click");
        $("#carousel-btn-right, #carousel-btn-right-modal").off("click");
        $('#content-data-flashCards, #falsh-card-modal-body').off("click");

        $('#carousel-btn-right, #carousel-btn-right-modal').show();
        $('#carousel-btn-left, #carousel-btn-left-modal').show();

        $('#card-number, #card-number-modal').html(1+" of "+keyValues.length);
        $('#carousel-btn-right, #carousel-btn-right-modal').removeClass('carousel-btn-disabled');
        $('#carousel-btn-left, #carousel-btn-left-modal').addClass('carousel-btn-disabled');

        for(i=0;i<keyValues.length;i++) {
            cStr += "<div class='item carousel-item'>" +
                "<div class='flip-container' id='flash-card-"+keyValues[i].id+"'>" +
                "<div class='flipper'>" +
                "<div class='front'>" +
                "<span class='name'>" + keyValues[i].term + "</span>" +
                "</div>" +
                "<div class='back'>" +
                "<span class='back-title'>"+keyValues[i].definition+"</span>" +
                "</div>" +
                "<a href='#' id='flip-card-btn-"+keyValues[i].id+"' class='flip-card-btn'><i class='material-icons'>repeat</i> Turn</a>" +
                "</div>" +
                "</div>" +
                "</div>" ;
        }

        $('#carousel-btn-left, #carousel-btn-left-modal').click(function(e) {
            e.preventDefault();
            setTimeout(function() {
                if($('.flip-container').hasClass('flip')) {
                    $('.flip-container').removeClass('flip');
                    $('.flip-card-btn').removeClass('btn-flip');
                }
            }, 1000);

            if(position <= keyValues.length) {
                $('#card-number, #card-number-modal').html((position-1)+" of " +keyValues.length);
                $('#carousel-btn-right, #carousel-btn-right-modal').removeClass('carousel-btn-disabled');
                position = position-1;

                if(position == 1) {
                    $('#carousel-btn-left, #carousel-btn-left-modal').addClass('carousel-btn-disabled');
                }
            }
        });

        $('#carousel-btn-right, #carousel-btn-right-modal').click(function(e) {
            e.preventDefault();
            setTimeout(function() {
                if($('.flip-container').hasClass('flip')) {
                    $('.flip-container').removeClass('flip');
                    $('.flip-card-btn').removeClass('btn-flip');
                }
            }, 1000);

            if(position < keyValues.length) {
                $('#card-number, #card-number-modal').html((position+1)+" of " +keyValues.length);
                $('#carousel-btn-left, #carousel-btn-left-modal').removeClass('carousel-btn-disabled');
                position = position + 1;
                if(position >= keyValues.length) {
                    $('#carousel-btn-right, #carousel-btn-right-modal').addClass('carousel-btn-disabled');
                }
            }
        });

        $("#content-flashCards").css("display", "block");
        var contentData;

        if(siteId == "9") {
            contentData = document.getElementById('content-data-flashCards');
            document.getElementById("flashCardTitle").innerHTML=revisionName;
        } else {
            contentData = document.getElementById('falsh-card-modal-body');
            document.getElementById("flashCardTitle").innerHTML=revisionName;
        }

        contentData.innerHTML = cStr;

        $('#content-data-flashCards .item, #falsh-card-modal-body .item').first().addClass('active');
        if(keyValues.length>0){
            $('#content-data-flashCards, #falsh-card-modal-body').on('click', '.flip-card-btn', function(e) {
                e.preventDefault();
                $(this).parents('.flip-container').toggleClass('flip');
                $(this).toggleClass('btn-flip');
            });
        }
<%
    if("sage".equals(session["entryController"]) && "true".equals(session.getAttribute("sageUserCreated"))){
        session.setAttribute("sageUserCreated",null);
%>
        $('#studentInformation').modal('show');
<%  } %>
    }

    //removing the copy thingy

    document.getElementById('study-set-from-notes').ondragstart = function () {
        return false; };
    document.getElementById('falsh-card-modal-body').ondragstart = function () {
        return false; };

    $(document).ready(function() {
        $('#study-set-from-notes').bind('copy paste', function(e) {
            e.preventDefault();
        });
    });

    $(document).ready(function() {
        $('#falsh-card-modal-body').bind('copy paste', function(e) {
            e.preventDefault();
        });
    });
</script>
