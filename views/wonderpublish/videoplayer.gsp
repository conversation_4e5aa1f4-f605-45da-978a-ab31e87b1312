<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://cdn.plyr.io/3.6.1/plyr.css" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <title>Document</title>
    <style>
        html,body{
            padding: 0;
            margin: 0;
        }
    :root {
        --plyr-color-main: #1ac266;
    }
    .player {
        --plyr-color-main: #1ac266;
    }
    .ytp-pause-overlay
    {
        bottom:-200px !important;
    }
        .testss{
            width:400px;
        }
        .hide-logo{
            background:#000;
            width: 110px;
            height: 40px;
            position: absolute;
            bottom: 5px;
            right: 0px;
        }
        .logo-wrapper{
            display: flex;
            align-items: center;
            justify-content: space-around;
            height: 40px;
            color:#fff;
            font-size: 16px;
            padding: 5px;
        }
        .logo-wrapper img{

        }

    </style>
</head>

<body>
<div class="testss">
    <div class="plyr__video-embed" id="player">

        <iframe src="https://www.youtube.com/embed/0ZmJ6qmuB-o?origin=https://plyr.io&amp;iv_load_policy=3&amp;modestbranding=1&amp;playsinline=1&amp;showinfo=0&amp;rel=0&amp;enablejsapi=1"
                allow="autoplay"
        ></iframe>
        <div class="hide-logo" id="hideLogo">
            <div class="logo-wrapper">
            <img src="${assetPath(src: 'landingpageImages/ws-tabicon.svg')}" width="110px" height="30px">
                <p>Wonderslate</p>
            </div>
        </div>
    </div>
</div>
<select id="selectBtn">
</select>
<script src="https://cdn.plyr.io/3.6.1/plyr.js"></script>

<script src="https://cdn.plyr.io/3.6.1/plyr.polyfilled.js"></script>
<script>

    var raj=[144,240,480];
    var arrayLetters = ['A', 'B'];
    var player = new Plyr('#player',{
        controls:['play-large', 'play', 'progress','rewind','fast-forward', 'mute', 'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen','quality'],
        quality:{default: 576, options: [4320, 2880, 2160, 1440, 1080, 720, 576, 480, 360, 240] }
    });
    player.on('play',function (event) {
        console.log(event.detail.plyr.options.quality.push(raj));
        console.log(event.detail.plyr.options);
        setTimeout( function(){$('#hideLogo').hide();} , 4000);

    });

    player.on('pause',function (event) {
       $('#hideLogo').show();
    });

</script>
</body>
</html>
