<g:render template="/${session['entryController']}/navheader_new"></g:render>
<script>var defaultSiteName="${session['entryController']}";
</script>
<style>

@import url('https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900&display=swap');
@media (min-width: 1300px) {
    .container.chat-container {
        max-width: 960px;
    }
}
@media (min-width: 1400px) {
    .chat-container .chat-pagination {
        max-width: 205px;
    }
}
@media (min-width: 1500px) {
    .chat-container .chat-pagination {
        max-width: 175px;
    }
    .container.chat-container {
        max-width: 1140px;
    }
}
@media (min-width: 1600px) {
    .chat-container .chat-pagination {
        max-width: 225px;
    }
}
@media (min-width: 1700px) {
    .chat-container .chat-pagination {
        max-width: 275px;
    }
}
@media (min-width: 1800px) {
    .chat-container .chat-pagination {
        max-width: 315px;
    }
}
@media (min-width: 1900px) {
    .chat-container .chat-pagination {
        max-width: 365px;
    }
}
@media (min-width: 2000px) {
    .chat-container .chat-pagination {
        max-width: 415px;
    }
}
.chat-export-btn:focus:active {
    color: #007bff !important;
    border-color: #007bff !important;
}
.chat-export-btn:focus:hover {
    color: #007bff !important;
}
.tab-content,.chat-tabs,.chat-header {
    position: relative;
}
.tab-content>.tab-pane {
    background: #ffffff;
}
.chat-pagination {
    position: absolute;
    left: 15px;
    max-width: 175px;
}
#liveChatPageBtn a {
    font-weight: normal;
}
#liveChatPageBtn a:focus:active {
    color: #ffffff;
    outline: 0;
    box-shadow: none;
    background-color: #007bff !important;
}
#pageNumbers li.active .page-link {
    background: #007bff !important;
}
#pageNumbers li .page-link:focus:active {
    box-shadow: none;
    color: #ffffff;
    background-color: #007bff !important;
}
#imgPopupModal .close {
    position: absolute;
    right: -10px;
    top: -10px;
    width: 25px;
    height: 25px;
    background: #000;
    border: 2px solid #fff;
    color: #FFF;
    border-radius: 50px;
    font-size: 14px;
    font-weight: normal;
    opacity: 1;
    z-index: 999;
}
#imgPopupModal .close:focus {
    outline: 0;
}
#imgPopupModal .modal-body {
    padding: 7px;
}
#imgPopupModal #originalImage {
    width: 100%;
}
#imgPopupModal #imgCaption {
    text-align: center;
    padding: 7px;
    font-size: 14px;
}
.options-img {
    border-radius: 2px;
    cursor: pointer;
}
.options-img:hover {
    opacity: 0.7;
}
.tab-body {
    height: calc(100vh - 260px);
    overflow: hidden;
    overflow-y: auto;
    border: 1px solid #ddd;
    position: relative;
}
#displayPollQuestion .modal-body {
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}
#displayPollQuestion #pollQuestion h5 {
    /*white-space: pre-wrap;*/
    /*display: flex;*/
    /*text-align: left;*/
    /*justify-content: center;*/
    /*align-items: center;*/
}
#displayPollQuestion #pollQuestion h5 p {
    margin-bottom: 0;
}
#displayPollQuestion .poll-answers-list button {
    /*white-space: pre-wrap;*/
    cursor: default;
    text-align: left;
    /*display: flex;*/
    align-items: center;
}
#displayPollQuestion .poll-answers-list button p {
    margin-bottom: 0;
}
#displayPollQuestion .poll-answers-list button span {
    /*color: #777;*/
    font-weight: normal;
}
#pollsList tr td a.btn-outline-primary:focus,
#pollsList tr td a.btn-outline-primary:focus:active{
    color: #007bff;
}
.chat-tabs li a.nav-link {
    border-color: #eee;
}
#replyModal {
    z-index: 999991;
}
#replyModal textarea.form-control {
    min-height: 120px;
}
#replyModal textarea.form-control:focus {
    box-shadow: none !important;
}
.user-action-modal {
    top: 30px;
    bottom: auto;
    background: none !important;
}
.user-action-modal .modal-dialog {
    margin: 20px auto;
}
.user-action-modal .modal-content {
    border: none;
}
.user-action-modal .modal-body p {
    margin-bottom: 0;
    color: #f00;
    font-weight: 500;
    text-align: center;
    font-size: 16px;
}
#stopChatWindow .btn-outline-primary:focus:active {
    color: #007bff;
    background-color: transparent;
}
#stopChatWindow .btn-outline-primary:hover {
    background-color: transparent;
    color: #007bff;
}

    .chat-header{
        /*height: 50px;*/
        background:#ddd;
    }
    .chat-header h4{
        font-size: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0.5rem;
        color: #fff000;

        font-family: 'Roboto', sans-serif;
        font-weight: normal;
    }
    .chat-footer{
        height: 100px;
        background: #eee;
    }
    .chat-body{
        height: calc(100vh - 320px);
        overflow: hidden;
        overflow-y: auto;
        border: 1px solid #ddd;
        position: relative;
    }
.chat-body h5.message_info {
    font-size: 17px;
    width: 93%;
    margin-bottom: 2px;
    font-weight: normal;
    color: #030303;
    font-family: 'Roboto', sans-serif;
}

.chat-body p.sender_name small {
    color: #808080;
    font-size: 11px;
}
.chat-body p.sender_name {
    width: 93%;
    margin-bottom: 0;
    font-size: 13px;
    color: #333333;
    font-family: 'Roboto', sans-serif;
}
.chat-body p.sender_name span {
    color: #4200FF;
}
    .hidden{
        display: none;
    }
    .customChatWrapper{
        display: none !important;
    }
.btn-trans{
    background: transparent;
    border: none;
}
    .chat-body .row:first-child{
        margin-top: 1rem;
    }
    .action-btn{
        position: absolute;
        right: 10px;
        top:10px;
    }
    .action-btn button{
        border: none;
        outline: none;
        color: #aaa;
    }
    .action-btn button:hover, .action-btn button:focus {
        color: #000000;
        cursor: pointer;
    }
    .action-btn .dropdown-item {
        font-family: 'Roboto', sans-serif;
    }
    .action-btn .dropdown-item:active {
        background-color: #000000;
        color: #fff !important;
    }
    .form-control{
        outline: none;
    }
    .form-control:focus{
        outline: none;
    }
    .arrow{
        background: #007bff;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        position: sticky;
        bottom: 10px;
        right: 3px;
        float: right;
    }
    textarea {
        resize: none;
        width: 90%;
        font-family: 'Roboto', sans-serif;
    }
.arrow:focus{
    outline: none;
}
    .arrow:active{
        outline: none;
    }
    #inputFocus:focus{
        outline: none !important;
        border:none;
        box-shadow: none;
    }
    .btn-send{
        background: #000;
        border-radius: 50px;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        margin-right: 1rem;
    }
    .btn-send i{
        color:#fff200;
        font-size: 30px;
    }

    .loading-icon {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        background: rgba(68, 68, 68, 0.9);
        z-index: 9999;
        overflow: hidden;
    }
    .load-wrapper {
        position: absolute;
        top: 35%;
        width: 100%;
    }
    .loader-wrapper {
        width: 139px;
        height: 65px;
        background-color: #FFFFFF;
        position: relative;
        top: 50% !important;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
        margin: 0 auto;
        border-radius: 4px;
    }
    .loader,
    .loader:before,
    .loader:after {
        border-radius: 50%;
        width: 2.5em;
        height: 2.5em;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
        -webkit-animation: load7 1.8s infinite ease-in-out;
        animation: load7 1.8s infinite ease-in-out;
    }
    .loader {
        color: #F05A2A;
        font-size: 9px;
        margin: 0 auto;
        position: relative;
        text-indent: -9999em;
        -webkit-transform: translateZ(0);
        -ms-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-animation-delay: -0.16s;
        animation-delay: -0.16s;
    }
    .loader:before,
    .loader:after {
        content: '';
        position: absolute;
        top: 0;
    }
    .loader:before {
        left: -3.5em;
        -webkit-animation-delay: -0.32s;
        animation-delay: -0.32s;
    }
    .loader:after {
        left: 3.5em;
    }
    @-webkit-keyframes load7 {
        0%,
        80%,
        100% {
            box-shadow: 0 2.5em 0 -1.3em;
        }
        40% {
            box-shadow: 0 2.5em 0 0;
        }
    }
    @keyframes load7 {
        0%,
        80%,
        100% {
            box-shadow: 0 2.5em 0 -1.3em;
        }
        40% {
            box-shadow: 0 2.5em 0 0;
        }
    }


    /* Auto scroll */
    .auto_scroll label, .auto_scroll input {
        font-family: 'Roboto', sans-serif;
    }

    .deleted_comment {
        color: #606060;
        font-size: 14px;
        font-family: 'Roboto', sans-serif;
    }
    textarea#commentInput {
        color: #030303;
    }
</style>

<% if("eutkarsh".equals(session["entryController"])){%>
<style>
.logo_icon {
    background-image: url("/assets/eutkarsh/utkarsh-logo.svg");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 32px;
    height: 32px;
}
</style>
<%}%>
<% if(session["siteId"] == 21){%>
<style>
.logo_icon {
    background-image: url("/assets/clients/winners_logo.jpg");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 32px;
    height: 32px;
}
</style>
<%}%>
<% if(session["siteId"] == 34){%>
<style>
.logo_icon {
    background-image: url("/assets/clients/jbclasses_logo.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 32px;
    height: 32px;
}
</style>
<%}%>
<% if("books".equals(session["entryController"])){%>
<style>
.logo_icon {
    background-image: url("/assets/landingpageImages/ws-tabicon.svg");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 32px;
    height: 32px;
}
</style>
<%}%>
<link href="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/css/bootstrap4-toggle.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/js/bootstrap4-toggle.min.js"></script>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section style="height: 100vh;">
<div class="container chat-container text-right my-3 auto_scroll">
<label for="auto_scroll_chk" class="mb-0">
        <strong class="mr-1">Live users</strong>
        <span id="watching">0</span>&nbsp;&nbsp;
    </label>
    <label for="auto_scroll_chk" class="mb-0">
        <strong class="mr-1">Auto Scroll</strong>
        <input checked type="checkbox" id="auto_scroll_chk" name="auto_scroll_chk" value="1" onchange="autoScrollChkChanged(this)" data-toggle="toggle" data-size="sm" data-onstyle="success">
    </label>
</div>
<div class="container chat-container">
    <div class="chat-pagination">
        <div id="liveChatPageBtn" class="mb-2 text-center"></div>
        <ul id="pageNumbers" class="pagination pagination-sm mb-0 flex-wrap"></ul>
    </div>
    <div class="chat-header bg-dark text-white shadow">
    <h4 class="text-center mb-0 py-3"><span>${bookName}</span><span> | </span><span>${chapterName}</span><span> | </span><span>${videoName}</span></h4>
    </div>
<ul class="nav nav-tabs nav-pills nav-justified chat-tabs" role="tablist">

    <li role="presentation" class="nav-item" >
        <a  class="nav-link active" href="#chatWindow" role="tab" data-toggle="tab"  id="tabChat">Chat</a>
    </li>
   <!-- <li role="presentation" class="nav-item" >
        <a  class="nav-link" href="#pollWindow" role="tab" data-toggle="tab"  id="tabPoll">Polls</a>
    </li>
    <li role="presentation" class="nav-item" >
        <a class="nav-link" href="#blockedUsersWindow" role="tab" data-toggle="tab" id="tabBlockedUsers">Blocked users</a>
    </li>-->
    <li role="presentation" class="nav-item" >
        <a class="nav-link" href="#stopChatWindow" role="tab" data-toggle="tab" id="tabStopChat">Stop Chat</a>
    </li>
    <li role="presentation" class="nav-item" >
        <a class="nav-link" href="#exportWindow" role="tab" data-toggle="tab" id="tabExport">Export</a>
    </li>
</ul>
    <div class="tab-content">
        <div role="tabpanel" class="tab-pane fade active show" id="chatWindow">
            <div class="chat-body" id="chat-body-id">
                <div class="media position-relative ml-2 mt-2">
                </div>
                <button onclick="autoScrollClicked()" class="arrow shadow btn" style="display: none;" id="downArrow" type="button">
                    <i class="material-icons">arrow_downward</i>
                </button>
            </div>
            <div class="chat-footer align-items-center border">
                <div class="d-flex">
                    <textarea id="commentInput" onkeydown="commentInputKeyDown(event)" class="form-control mt-3 ml-2 shadow"></textarea>
                    <button class="btn btn-trans btn-send mt-4 shadow" id="commentSubmitBtn" type="button">
                        <i class="material-icons">near_me</i>
                    </button>

                </div>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="pollWindow">
            <div class="tab-body p-4">
                <table class="table table-bordered">
                    <thead class="thead-light">
                    <th width="60%">Poll</th>
                    <th width="20%">Action</th>
                    <th width="20%">Result</th>
                    </thead>
                    <tbody id="pollsList" class="bg-white">

                    </tbody>
                </table>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="blockedUsersWindow">
            <div class="tab-body p-4">
                <table class="table table-bordered">
                    <thead class="thead-light">
                    <th width="70%">Name</th>
                    <th width="30%">Action</th>
                    </thead>
                    <tbody id="blockedUsersList" class="bg-white">

                    </tbody>
                </table>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="stopChatWindow">
            <div class="row mt-5">
                <div class="col-md-6 text-center border-right">
                    <h5 class="mb-4">Manual</h5>
                    <div id="stopChat"> <a href="javascript:stopChat();" class="btn btn-outline-primary">Stop Chat</a>
                    </div>
                    <div id="startChat" style="display: none"> <a href="javascript:startChat();" class="btn btn-outline-primary">Start Chat</a>
                    </div>
                </div>
                <div class="col-md-6 text-center border-left">
                    <h5 class="mb-4">Automatic</h5>
                    <div class="form-group row mx-auto col-md-10 align-items-center">
                        <label class="col-md-4 mb-0 text-right">Hour</label>
                        <div class="col-md-8">
                            <select class="form-control" id="setHours">
                                <option value="0">Select</option>
                                <option value="01">01</option>
                                <option value="02">02</option>
                                <option value="03">03</option>
                                <option value="04">04</option>
                                <option value="05">05</option>
                                <option value="06">06</option>
                                <option value="07">07</option>
                                <option value="08">08</option>
                                <option value="09">09</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row mx-auto col-md-10 align-items-center">
                        <label class="col-md-4 mb-0 text-right">Minutes</label>
                        <div class="col-md-8">
                            <select class="form-control" id="setMinutes">
                                <option value="0">Select</option>
                                <option value="00">00</option>
                                <option value="01">01</option>
                                <option value="02">02</option>
                                <option value="03">03</option>
                                <option value="04">04</option>
                                <option value="05">05</option>
                                <option value="06">06</option>
                                <option value="07">07</option>
                                <option value="08">08</option>
                                <option value="09">09</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="13">13</option>
                                <option value="14">14</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="19">19</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                                <option value="24">24</option>
                                <option value="25">25</option>
                                <option value="26">26</option>
                                <option value="27">27</option>
                                <option value="28">28</option>
                                <option value="29">29</option>
                                <option value="30">30</option>
                                <option value="31">31</option>
                                <option value="32">32</option>
                                <option value="33">33</option>
                                <option value="34">34</option>
                                <option value="35">35</option>
                                <option value="36">36</option>
                                <option value="37">37</option>
                                <option value="38">38</option>
                                <option value="39">39</option>
                                <option value="40">40</option>
                                <option value="41">41</option>
                                <option value="42">42</option>
                                <option value="43">43</option>
                                <option value="44">44</option>
                                <option value="45">45</option>
                                <option value="46">46</option>
                                <option value="47">47</option>
                                <option value="48">48</option>
                                <option value="49">49</option>
                                <option value="50">50</option>
                                <option value="51">51</option>
                                <option value="52">52</option>
                                <option value="53">53</option>
                                <option value="54">54</option>
                                <option value="55">55</option>
                                <option value="56">56</option>
                                <option value="57">57</option>
                                <option value="58">58</option>
                                <option value="59">59</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row mx-auto col-md-10 align-items-center">
                        <label class="col-md-4 mb-0 text-right">Seconds</label>
                        <div class="col-md-8">
                            <select class="form-control" id="setSeconds">
                                <option value="0">Select</option>
                                <option value="00">00</option>
                                <option value="01">01</option>
                                <option value="02">02</option>
                                <option value="03">03</option>
                                <option value="04">04</option>
                                <option value="05">05</option>
                                <option value="06">06</option>
                                <option value="07">07</option>
                                <option value="08">08</option>
                                <option value="09">09</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="13">13</option>
                                <option value="14">14</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="19">19</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                                <option value="24">24</option>
                                <option value="25">25</option>
                                <option value="26">26</option>
                                <option value="27">27</option>
                                <option value="28">28</option>
                                <option value="29">29</option>
                                <option value="30">30</option>
                                <option value="31">31</option>
                                <option value="32">32</option>
                                <option value="33">33</option>
                                <option value="34">34</option>
                                <option value="35">35</option>
                                <option value="36">36</option>
                                <option value="37">37</option>
                                <option value="38">38</option>
                                <option value="39">39</option>
                                <option value="40">40</option>
                                <option value="41">41</option>
                                <option value="42">42</option>
                                <option value="43">43</option>
                                <option value="44">44</option>
                                <option value="45">45</option>
                                <option value="46">46</option>
                                <option value="47">47</option>
                                <option value="48">48</option>
                                <option value="49">49</option>
                                <option value="50">50</option>
                                <option value="51">51</option>
                                <option value="52">52</option>
                                <option value="53">53</option>
                                <option value="54">54</option>
                                <option value="55">55</option>
                                <option value="56">56</option>
                                <option value="57">57</option>
                                <option value="58">58</option>
                                <option value="59">59</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row mx-auto col-md-10 justify-content-center mt-4" id="countdownTimerButton">
                        <label class="col-md-4"></label>
                        <div class="col-md-8">
                            <button type="button" class="btn btn-outline-primary col-md-6" onclick="getAutoTime();">Submit</button>
                        </div>
                    </div>
                    <div class="form-group row mx-auto col-md-10 justify-content-center mt-4" >
                        <label class="col-md-4"></label>
                        <div class="col-md-8" id="countdownTimer" style="display: none">

                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div role="tabpanel" class="tab-pane fade" id="exportWindow">
            <a class="btn btn-outline-primary chat-export-btn mt-4" href="javascript:exportChat()">Download chat in excel</a>
        </div>
    </div>


</div>

</div>
    <div class="modal fade otp-screens" id="replyModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" data-verify="otp-request">
        <div class="modal-content">
            <!-- Modal Header -->
            %{--<div class="modal-header">
                <h4 class="modal-title">OTP Verification</h4>
            </div>--}%

            <!-- Modal body -->
            <div class="modal-body text-center mx-auto">
                <h4>Reply</h4>

                <div class="mobile-wrapper">
                    <g:form>
                        <span class="input-login">
                            <textarea class="form-control input-field border border-dark mt-3" name="replyMsg" id="replyMsg" autocomplete="off"></textarea>

                        </span>
                    </g:form>
                </div>

            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-default col-md-5" data-dismiss="modal" aria-label="Close">CANCEL</button>
                <button type="button" class="btn btn-lg btn-primary col-md-5" id="get-otp" onclick="javascript:sendReply();">SEND</button>

            </div>

        </div>
    </div>
</div>

<div class="modal fade user-action-modal" id="userActionModal" data-keyboard="false" data-backdrop="false" >
    <div class="modal-dialog">
        <div class="modal-content shadow rounded">
            <!-- Modal body -->
            <div class="modal-body shadow">
                <div id="userActionModalMessage"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="startPollModal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body text-center mx-auto">
                <h5>Poll Duration<br><small><em>(in seconds)</em></small></h5>
                <g:form class="my-4">
                    <input type="number"  name="pollDuration" id="pollDuration" value="20" class="border border-dark px-2 form-control">
                </g:form>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center border-top-0">
                <button type="button" class="btn btn-default col-md-5" data-dismiss="modal" aria-label="Close">CANCEL</button>
                <button type="button" class="btn btn-lg btn-primary col-md-5"  onclick="javascript:startPoll();">START</button>

            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="displayPollQuestion" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered" data-verify="otp-request">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body text-center mx-auto w-100 p-4">

                <div id="pollQuestion">



                </div>
                <div id="countDownTimerDisplay"></div>
            <div id="pollStopBtn" style="display: none"><a class='btn btn-sm btn-primary' href="javascript:pollCompleted()">Stop Poll</a></div>

            </div>

            <!-- Modal footer -->


        </div>
    </div>
</div>

<div class="modal fade image-popup-modal" id="imgPopupModal" data-keyboard="false" data-backdrop="true" >
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content shadow rounded">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">X</span>
            </button>
            <!-- Modal body -->
            <div class="modal-body">
                <img id="originalImage" src="">
                <div id="imgCaption"></div>
            </div>
        </div>
    </div>
</div>

</section>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.1.4/sockjs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
<script src="https://kit.fontawesome.com/d08b0443c8.js" crossorigin="anonymous"></script>
<script src='https://cdnjs.cloudflare.com/ajax/libs/twbs-pagination/1.4.2/jquery.twbsPagination.min.js'></script>

<script>
    var allCommentsHeight = 0;
    var resId = "${params.resId}";
var latestCommentId = 0, olldestIdForPagination = 0;
var firstCall = '0';

var showArrowIcon=0;
var autoScrollChecked = 1;
var pollDuration=0;
var totalMessagesDisplayed=0;
var perPageMessagesLimit=300;
var firstDisplayedElementId=-1;
var latestPageIndex=0;

var currentPageIndex =-1;
var currentlyDisplayedPageIndex=-1;
var pageChangeClicked = false;

   function getPageContents(index){
       pageChangeClicked = true;
       if(currentPageIndex!=index) {
           if (index == -1) {
               currentPageIndex = -1;
               latestCommentId = 0;
               totalMessagesDisplayed=0;
           } else currentPageIndex = index;
           document.getElementById("chat-body-id").innerHTML = "<div class=\"media position-relative ml-2 mt-2\">\n" +
               "                </div>\n" +
               "                <button onclick=\"autoScrollClicked()\" class=\"arrow shadow btn\" style=\"display: none;\" id=\"downArrow\" type=\"button\">\n" +
               "                    <i class=\"material-icons\">arrow_downward</i>\n" +
               "                </button>";
           getNewCommentsById();
       }
   }

    function getAllUserComments() {
        $('.loading-icon').addClass('hidden');
        if(resId == undefined || resId == '0' || resId == '' || resId == 0 || resId == null || resId.toLowerCase() == 'null'){
            alert("Please enter valid RES-ID");
            var url = "/comments/chatLogin";
            window.location.href=url
        }
        firstCall = '1';
        getNewCommentsById();

    }


    function displayComments(data){
        var commentsArr = data['comments'];
        var currentChatNo = data.currentChatNo;
        if((Math.floor((currentChatNo-1)/perPageMessagesLimit)+1)==latestPageIndex) {
            //do nothing everything is set
        }else{
            var liveChatStr = "<h6>Page Numbers</h6><a class='btn btn-sm text-white bg-primary' href='javascript:getPageContents(-1);'>Live chat</a>";
            var htmlStr = "";
            for(var i=0;i<(Math.floor((currentChatNo-1)/perPageMessagesLimit))+1;i++){
                htmlStr += "<li class='page-item mr-2 mb-2'><a class='page-link btn btn-outline-primary' href='javascript:getPageContents("+i+");'>"+(i+1)+"</a></li>";
            }
            latestPageIndex = (Math.floor((currentChatNo-1)/perPageMessagesLimit))+1;
            document.getElementById("pageNumbers").innerHTML = htmlStr;
            document.getElementById("liveChatPageBtn").innerHTML = liveChatStr;

        }
        currentlyDisplayedPageIndex = currentPageIndex;
        if(commentsArr != undefined){
            if(commentsArr.length >0) {
               if(currentPageIndex==-1) {
                   latestCommentId = commentsArr[0]['messageId'];
               }else{

                   commentsArr.reverse();
               }
                if(olldestIdForPagination == 0)olldestIdForPagination = commentsArr[commentsArr.length-1]['messageId'];
            }

            for(var i= commentsArr.length-1 ; i >= 0 ; i-- ){
                if(commentsArr[i]['status']=='deleted') continue;
                if(firstDisplayedElementId==-1) firstDisplayedElementId = commentsArr[i]['chatNo']
                if(document.getElementById("inputFocus") == null || document.getElementById("inputFocus") == undefined){
                    var str = '<input id="inputFocus" style="width: 0px;height: 0px;border: none">';
                    $(str).insertAfter($('.media').last());
                }
                try{
                    if(commentsArr[i]['adminUser'] == "true"){
                        var str = '<div id="'+commentsArr[i]['chatNo']+'" class="media position-relative ml-2 mt-2">' +
                                    '   <div class="mr-3 mt-1 rounded-circle logo_icon"></div>\n' +
                                         "<div class=\"media-body\"> \n" +
                                            "  <p class='sender_name'> <small><i>" +
                            ""+moment.utc(commentsArr[i]['dateCreated']).local().format("DD-MM-YYYY h:mm a")+"" ;
                           if(commentsArr[i].status=="reply") str +="&nbsp;&nbsp;<b><i class='fas fa-reply'></i></b>";
                           str += "</i></small></p>\n" +
                                                "<h5 class='message_info'>"+decodeURI(commentsArr[i]['message'].replace(/"/g,"").replace(/~~/g,",").replace(/~/g,":"))+"</h5>\n" +
                                        "</div>\n" +
                                        "<div class=\"dropdown action-btn dropleft\">\n" +
                                        "    <button type=\"button\" class=\"btn-trans\" data-toggle=\"dropdown\">\n" +
                                        "        <i class=\"material-icons\">\n" +
                                        "            more_vert\n" +
                                        "        </i>\n" +
                                         "    </button>"+
                                        "  <div class=\"dropdown-menu p-0 mr-0\">\n" +
                                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['sender']+"','"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete comment</a>\n" +
                                        "  </div>\n" +
                                        "</div>"+
                            '  </div>';
                        $(str).insertAfter($('.media').last());
                        totalMessagesDisplayed++;
                       if(currentPageIndex==-1&&totalMessagesDisplayed>perPageMessagesLimit) {

                            //remove the first element
                            totalMessagesDisplayed--;
                            removeFirstElement();
                        }

                    }else{
                        var str = '<div id="'+commentsArr[i]['chatNo']+'" class="media position-relative ml-2 mt-2">' +
                            '   <img src="" alt="" class="mr-3 mt-1 rounded-circle" style="width:32px;">\n' +
                            "<div class=\"media-body\"> \n" +
                            "<p class='sender_name'><span>"+commentsArr[i]['sender']+"</span>, "+commentsArr[i]['username']+" <small><i>"+
                            " "+moment.utc(commentsArr[i]['dateCreated']).local().format("DD-MM-YYYY h:mm a")+"&nbsp;&nbsp;<b>"+commentsArr[i]['course']+"</b>"+
                            "</i></small></p>\n" +
                            "<h5 class='message_info'>"+decodeURI(commentsArr[i]['message'].replace(/"/g,"").replace(/~~/g,",").replace(/~/g,":"))+"</h5>\n" +
                            "</div>\n" +
                            "<div class=\"dropdown action-btn dropleft\">\n" +
                            "    <button type=\"button\" class=\"btn-trans\" data-toggle=\"dropdown\">\n" +
                            "        <i class=\"material-icons\">\n" +
                            "            more_vert\n" +
                            "        </i>\n" +
                            "    </button>"+
                            "  <div class=\"dropdown-menu p-0 mr-0\">\n" +
                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['sender']+"','"+commentsArr[i]['username']+"','reply','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Reply</a>\n" +
                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['sender']+"','"+commentsArr[i]['username']+"','blockTemporary','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Block user - temporary</a>\n" +
                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['sender']+"','"+commentsArr[i]['username']+"','block','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Block user - permanent</a>\n" +
                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['sender']+"','"+commentsArr[i]['username']+"','unblock','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Unblock user</a>\n" +
                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['sender']+"','"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete comment</a>\n" +
                            "  </div>\n" +
                            "</div>"+
                            '  </div>';
                        $(str).insertAfter($('.media').last());
                        totalMessagesDisplayed++;

                        if(currentPageIndex==-1&&totalMessagesDisplayed>perPageMessagesLimit) {

                            //remove the first element
                            totalMessagesDisplayed--;

                            removeFirstElement();
                        }
                    }
                }catch (e) {
                    continue;
                }
                allCommentsHeight = allCommentsHeight + $("#"+commentsArr[i]['messageId']).innerHeight();
                // console.log(allCommentsHeight);
            }
           if(firstCall == '1') {
               $( "#inputFocus" ).focus();
               firstCall = '0';
           }
        }
        if(showArrowIcon == 1) {
            $("#downArrow").show();
        }else{
            // $( "#inputFocus" ).focus();
            // $("#downArrow").hide();
            var objDiv = document.getElementsByClassName("chat-body")[0];
            objDiv.scrollTop = (objDiv.scrollHeight );
        }

        $("#liveChatPageBtn a").click(function () {
            $(this).addClass("text-white bg-primary").removeClass("btn-outline-primary");
            $("#pageNumbers li.page-item").removeClass("active");
            //$("#pageNumbers li.page-item").last().addClass("active");
            $("#tabChat").trigger("click");
        });
        $("#pageNumbers li.page-item a").click(function () {
            $(this).parent().addClass("active").siblings().removeClass("active");
            $("#liveChatPageBtn a").removeClass("text-white bg-primary").addClass("btn-outline-primary");
            $("#tabChat").trigger("click");
        });
        // $("#pageNumbers li.page-item:last-child a").click(function () {
        //     $("#liveChatPageBtn a").addClass("text-white bg-primary").removeClass("btn-outline-primary");
        // });

    }

    // $(window).on('load',function () {
    //     $("#liveChatPageBtn a").trigger('click');
    //     $("#pageNumbers li.page-item").last().addClass("active");
    // });

    function removeFirstElement(){
         var element  = document.getElementById(firstDisplayedElementId);
         firstDisplayedElementId++;
        if (typeof(element) != 'undefined' && element != null) element.remove();
    }




    getAllUserComments();

    var replyToUsername;
    var userFullName;

    function userAction(fullName,userName,action,messageId,admin) {
        var url = "";
        userFullName = fullName;
        if(action == 'block') {
            url = "/comments/blockUserComments";
            document.getElementById("userActionModalMessage").innerHTML = "<p>"+userFullName+" has been blocked permanent.</p>";
            $("#userActionModal").modal("show");
            setTimeout(function(){
                $('#userActionModal').modal('hide');
            }, 5000);
        }
        else if(action == 'blockTemporary') {
            url = "/comments/blockUserCommentsTemp";
            document.getElementById("userActionModalMessage").innerHTML = "<p>"+userFullName+" has been blocked temporarily.</p>";
            $("#userActionModal").modal("show");
            setTimeout(function(){
                $('#userActionModal').modal('hide');
            }, 5000);
        }
        else if(action == 'unblock') {
            url = "/comments/unblockUserComments";
            document.getElementById("userActionModalMessage").innerHTML = "<p>"+userFullName+" has been unblocked.</p>";
            $("#userActionModal").modal("show");

            setTimeout(function(){
                $('#userActionModal').modal('hide');
            }, 5000);

        }
        else if(action == 'delete') {
            url = "/comments/removeQuestion";
        }
        else if(action=='reply')
        {   replyToUsername = userName;
            $('#replyMsg').val('');
            $('#replyModal').modal('show');
            return;
        }
        var xhttp = new XMLHttpRequest();
        xhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                // document.getElementById("demo").innerHTML = this.responseText;
                if(action == 'delete'){
                    if(admin == "true"){
                        var str = '' +
                            '    <div class="deleted_comment">\n' +
                                '<em>\n' +
                                '\n' + decodeURI("This comment is deleted") +
                                '</em>\n' +
                            // ' <p style="float: right"> '+moment.utc(commentsArr[i]['dateCreated']).local().format("DD-MM-YYYY h:mm a")+'</p> ' +
                            '    </div>\n' +
                            '  ';
                        $('#'+messageId).html(str);
                    }else{
                        var str = '' +
                            '    <div class="deleted_comment">\n' +
                                '<em>\n' +
                                '\n' + decodeURI("This comment is deleted") +
                                '</em>\n' +
                            '    </div>\n' +

                            '';
                        $('#'+messageId).html(str);
                    }
                }
            }
        };
        xhttp.open("POST",url , true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("username="+userName+"&messageId="+messageId+"&resId="+resId);
    }

    function autoScrollClicked() {
        // $( "#inputFocus" ).focus();
        // $("#downArrow").hide();
        var objDiv = document.getElementsByClassName("chat-body")[0];
        objDiv.scrollTop = (objDiv.scrollHeight );
        if(autoScrollChecked == 0)  {
            showArrowIcon = 1;
        }else if(autoScrollChecked == 1){
            showArrowIcon = 0;
        }
    }
    function sendReply(){
        var msg = $('#replyMsg').val();

        if(msg != '' && msg != undefined){
            <g:remoteFunction controller="comments" action="addQuestion" onFailure='getNewCommentsById();' onSuccess="checkForAddQuestionStatus(data)"
        params="'siteId=1&resId='+resId+'&toUser='+replyToUsername+'&status=reply&question='+msg"/>
        }
        $('#replyModal').modal('hide');
    }
$('#commentSubmitBtn').on('click',function(){
    var msg = $('#commentInput').val();
    msg = msg.replace(/%/g," ");
    $('#commentInput').val('');
    if(msg != '' && msg != undefined){

        <g:remoteFunction controller="comments" action="addQuestion" onFailure='getNewCommentsById();' onSuccess="checkForAddQuestionStatus(data)"
        params="'siteId=1&resId='+resId+'&question='+msg"/>
    }
})

    function getNewCommentsById() {
        // httpPollingRequestStatus = "started"

        <g:remoteFunction controller="comments" action="getLatestCommentsForChatAdmin" onSuccess='checkToDisplayCommentsOrNot(data);'
        params="'siteId=1&resId='+resId+'&questionId='+latestCommentId+'&currentPageIndex='+currentPageIndex+'&currentlyDisplayedPageIndex='+currentlyDisplayedPageIndex"/>
    }

    function setInterval() {
      // intervalValueId = window.setInterval(getNewCommentsById(), 3000);
    }

    function checkToDisplayCommentsOrNot(data){

        var comments = data['comments'];
        document.getElementById("watching").innerText=data['liveVideoUsersCount'];
        if("false"==data.chatStopped){
            $("#stopChat").show();
            $("#startChat").hide();
            $("#countdownTimer").hide();
            $("#countdownTimerButton").show();
        }else{
            $("#stopChat").hide();
            $("#startChat").show();
            $("#countdownTimer").show();
            $("#countdownTimerButton").hide();
        }
        if(comments.length > 0 ) {//&& latestCommentId < comments[0]['messageId']
            displayComments(data);
        }

        //this logic is added to make sure additional setTimeouts are not set. Without this check everytime a new page is clicked a new timeout gets activated
        if(!pageChangeClicked) {
            setTimeout(function () {
                getNewCommentsById();
            }, 3000);
        }else pageChangeClicked = false;

    }

    function checkToDisplayCommentsOrNotFirst(data){
        var comments = data['comments'];
        if("false"==data.chatStopped){
            $("#stopChat").show();
            $("#startChat").hide();
            $("#countdownTimer").hide();
            $("#countdownTimerButton").show();
        }else{
            $("#stopChat").hide();
            $("#startChat").show();
            $("#countdownTimer").show();
            $("#countdownTimerButton").hide();
        }
        if(comments.length > 0 ) {//&& latestCommentId < comments[0]['messageId']
            displayCommentsFirst(data);
        }
    }


    function checkForAddQuestionStatus(data){
        if(data['id'] != null && data['id'] != 0 && data['id'] != undefined && data['id'] != '0') {
            document.getElementById("userActionModalMessage").innerHTML = "<p>Message sent</p>";
            $("#userActionModal").modal("show");
            setTimeout(function(){
                $('#userActionModal').modal('hide');
            }, 2000);
        }
        else {
            //window.location.reload();
            document.getElementById("userActionModalMessage").innerHTML = "<p>Chat has been stopped. Please start the chat and try again.</p>";
            $("#userActionModal").modal("show");
            setTimeout(function(){
                $('#userActionModal').modal('hide');
            }, 5000);
        }
    }

    function commentInputKeyDown(event){
        if (event.defaultPrevented) {
            return;
        }
        var handled = false;
        if (event.key !== undefined) {
            if (event.altKey && event.key === 'Enter') {
                $( "#commentSubmitBtn" ).trigger( "click" );
            }
        } else if (event.keyIdentifier !== undefined) {
            if (event.altKey && event.keyIdentifier === "Enter") {
                $( "#commentSubmitBtn" ).trigger( "click" );
            }

        } else if (event.keyCode !== undefined) {
            if (event.altKey && event.keyCode === 13) {
                $( "#commentSubmitBtn" ).trigger( "click" );
            }
        }
        if (handled) {
            event.preventDefault();
        };
    };

    // $('.chat-body').scroll(function() {
    //     if($(this).scrollTop() + $('.chat-body').innerHeight() >= $('.chat-body').height()) {
    //         alert("bottom!");
    //         // getData();
    //     }
    // });
    var offset=0;
    var i=0;

    function stopChat(){
        <g:remoteFunction controller="comments" action="stopChat" onSuccess='displayChatStatus(data)' params="'resId='+resId"/>
        $("#stopChat").hide();
        $("#startChat").show();
        $("#countdownTimer").hide();
        $("#countdownTimerButton").hide();

    }

    function startChat(){
        <g:remoteFunction controller="comments" action="startChat" onSuccess='displayChatStatus(data)' params="'resId='+resId"/>
        $("#stopChat").show();
        $("#startChat").hide();
        $("#countdownTimer").hide();
        $("#countdownTimerButton").show();

    }

    function displayChatStatus(data){
        //alert("Chat "+data.status);
        document.getElementById("userActionModalMessage").innerHTML = "<p>Chat "+data.status+".</p>";
        $("#userActionModal").modal("show");
        setTimeout(function(){
            $('#userActionModal').modal('hide');
        }, 5000);
    }
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {

        var target = $(e.target).attr("id") // activated tab
      if(target=="tabBlockedUsers"){

          getBlockedUsers();
      }else  if(target=="tabPoll"){

          getPolls();
      }

    });

    $(document).ready(function(){
       setTimeout(function(){
            $("#tabChat").trigger("click");
        },500)
    });

    function getPolls(){
        <g:remoteFunction controller="polls" action="getPollingByResId" onSuccess='displayPolls(data)' params="'id='+resId"/>
    }

    function displayPolls(data){
       var polls = data.polls;
       if(polls!=null&&polls.length>0){
            var htmlStr = "";
            var earlierPollId=-1;
            var samePoll = false;
           if(getCookie("pollStarted")!=null&&getCookie("pollStarted")>-1) earlierPollId = getCookie("pollStarted");
           else if(getCookie("pollCompleted")!=null&&getCookie("pollCompleted  ")>-1)  earlierPollId = getCookie("pollCompleted");
           for(var i=0;i<polls.length;i++){
               if(earlierPollId == polls[i].id) samePoll = true;
               if("started"==polls[i].status){
                   htmlStr +="<tr><td>"+polls[i].name+"</td><td id='poll_"+polls[i].id+"'>Poll Started</td><td id='pollResults_"+polls[i].id+"'></td></tr>";

               }else if("completed"==polls[i].status||"resultsComputed"==polls[i].status||"published"==polls[i].status){
                   htmlStr +="<tr><td>"+polls[i].name+"</td><td id='poll_"+polls[i].id+"'>Poll Completed</td><td id='pollResults_"+polls[i].id+"'><a class='btn btn-sm btn-outline-primary' href='/polls/pollResults?pollId="+polls[i].id+"'>Show Results</a></td></tr>";
               }
               else {
                   htmlStr +="<tr><td>"+polls[i].name+"</td><td id='poll_"+polls[i].id+"'><a class='btn btn-sm btn-primary' href=\"javascript:showStartPollModal('"+polls[i].id+"')\">Start</a></td><td id='pollResults_"+polls[i].id+"'></td></tr>";
               }
           }
           htmlStr +="<tr><td colspan='3'><a class='btn btn-sm btn-outline-primary' href='/polls/pollResults?resId="+resId+"'>Consolidated Results</a></td></tr>";
           document.getElementById("pollsList").innerHTML = htmlStr;
           //if poll was started or had started stop process and then  refreshed.
           if(samePoll) {
               if (getCookie("pollStarted") != null && getCookie("pollStarted") > -1) {
                   currentPollId = getCookie("pollStarted");
                   console.log(" start currentPollId=" + currentPollId);
                   showStartPollModal(currentPollId);
               } else if (getCookie("pollCompleted") != null && getCookie("pollCompleted  ") > -1) {
                   currentPollId = getCookie("pollCompleted");
                   console.log(" stop currentPollId=" + currentPollId);
                   pollCompleted();
               }
           }
        }else{

           document.getElementById("pollsList").innerHTML = "<tr class='text-center'><td colspan='3'>No polls added yet!</td></tr>";
       }
    }
    var currentPollId;
    var pollStarting = false;
    var pollStopping = false;
    function showStartPollModal(pollId){
        if(pollStopping){
            alert("Please start the new poll after the poll completion of previous poll");
        }else {
            currentPollId = pollId;
            startPoll();
        }
    }
    function startPoll(){

        setCookieWithExpiry("pollCompleted",-1,60);
        setCookieWithExpiry("pollStarted",currentPollId,60);
        pollDuration = 20;
        pollStarting = true;
         $("#displayPollQuestion").modal("show");
        $("#pollStopBtn").hide();
        document.getElementById("poll_"+currentPollId).innerText="Poll Started";
        <g:remoteFunction controller="polls" action="getPollingDetailsByPollId"  onSuccess="showPollQuestion(data)"
        params="'id='+currentPollId+'&resId='+resId"/>
    }

    function showPollQuestion(data){
       var question = data.polls;
       var options = data.details;
        var htmlStr = "";
        var pollImgSrc="/polls/getPollsImages?resId=" + question.resId + "&fileName=" + question.imgName + "&pollsId=" + question.id + "&imgType=polls";
        if(question.imgName != undefined && question.imgName != "") {
            htmlStr += "<h5>"+question.question+"</h5><img src='"+ pollImgSrc+"' onclick='openImagePopup(this);' width='70' height='70' class='mt-2 options-img'><div class='poll-answers-list d-flex flex-wrap justify-content-center col-12 my-4 px-0'>";
        } else {
            htmlStr += "<h5>"+question.question+"</h5><div class='poll-answers-list d-flex flex-wrap justify-content-center col-12 my-4 px-0'>";
        }
       for(var i=0;i<options.length;i++){
           var optionImgSrc = "/polls/getPollsImages?resId="+question.resId+"&fileName="+options[i].imgName+"&pollsId="+options[i].pollsId+"&imgType=options";
           if(options[i].imgName != undefined && options[i].imgName != "") {
               htmlStr +="<button class='btn btn-sm col-5 mb-3 mx-2 py-2'><div class='d-flex' style='white-space: pre-wrap;'> <span class='mr-2'>"+(i+1)+".</span><span>"+options[i].optionText+"</span></div><img src='"+ optionImgSrc+"' onclick='openImagePopup(this);' width='40' height='40' class='mt-2 options-img'></button><br>";
           } else {
               htmlStr +="<button class='btn btn-sm col-5 mb-3 mx-2 py-2'><span class='d-flex' style='white-space: pre-wrap;'><span class='mr-2'>"+(i+1)+".</span><span>"+options[i].optionText+"</span></span></button><br>";
           }
       }
        htmlStr += "</div>";
       document.getElementById("pollQuestion").innerHTML=htmlStr;
        MathJax.Hub.Queue(["Typeset", MathJax.Hub, "pollQuestion"]);
        updateCountDownTimer();
    }

    function updateCountDownTimer(){
       if(pollStarting) document.getElementById("countDownTimerDisplay").innerText = "Time remaining for stop option - "+pollDuration+" seconds";

        if(pollDuration>0) pollTimer();
        else {
            if(pollStarting){
                //inform server to start the poll
                <g:remoteFunction controller="comments" action="startPoll" params="'pollId='+currentPollId+'&resId='+resId"/>
                $("#pollStopBtn").show();
                document.getElementById("countDownTimerDisplay").innerText="";

            }else{
                setCookieWithExpiry("pollCompleted",-1,60);
                document.getElementById("poll_"+currentPollId).innerText="Poll Completed";
                document.getElementById("pollResults_"+currentPollId).innerHTML="<a class='btn btn-sm btn-outline-primary' href='/polls/pollResults?pollId="+currentPollId+"'>Show Results</a>";
                <g:remoteFunction controller="comments" action="pollCompleted" onSuccess='pollCompletedConfirm(data)' params="'resId='+resId"/>
                pollStopping = false;
            }
        }
    }
    function pollTimer(){
        setTimeout(function(){
            pollDuration--;
            updateCountDownTimer();
        }, 1000);
    }



    function pollCompleted(){
        setCookieWithExpiry("pollStarted",-1,60);
        setCookieWithExpiry("pollCompleted",currentPollId,60);
        $("#displayPollQuestion").modal("hide");
        document.getElementById("poll_"+currentPollId).innerText="Poll completion in process";
        pollDuration = 30;
        pollStarting = false;
        pollStopping = true;
        updateCountDownTimer();

    }
    function pollCompletedConfirm(data){

    }

   function exportChat(){
       window.open("/comments/downloadWebChat?resId="+resId, "_blank");
   }
    function getBlockedUsers(){
        <g:remoteFunction controller="comments" action="getBlockedUsers" onSuccess='displayBlockedUsers(data)' params="'resId='+resId"/>
    }
    function displayBlockedUsers(data){
       var htmlStr="";
        var blockedUsers=  JSON.parse(data.blockedUsers);
       if(data.blockedUsers!=null&&blockedUsers.length>0){
           //htmlStr="";


           for(var i=0;i<blockedUsers.length;i++){
               htmlStr +="<tr><td>"+blockedUsers[i].name+"</td><td><a class='btn btn-sm btn-primary unblock-user' href=\"javascript:userAction('"+blockedUsers[i].name+"','"+blockedUsers[i].username+"','unblock','','')\"> Unblock </a></td></tr>";
           }
           document.getElementById("blockedUsersList").innerHTML = htmlStr;
       } else {
           document.getElementById("blockedUsersList").innerHTML = "<tr><td colspan='2' class='text-center'>No blocked users</td></tr>";
       }
    }

    $("#blockedUsersList").on('click','.unblock-user',function(){
        $(this).closest('tr').remove();
    });

    function getAutoTime() {
        var hour = document.getElementById("setHours");
        var minutes = document.getElementById("setMinutes");
        var seconds = document.getElementById("setSeconds");
        var selectedHourValue = parseInt(hour.options[hour.selectedIndex].value);
        var selectedMinutesValue = parseInt(minutes.options[minutes.selectedIndex].value);
        var selectedSecondsValue = parseInt(seconds.options[seconds.selectedIndex].value);

        var autoStopDuration = (selectedHourValue*60*60)+(selectedMinutesValue*60)+selectedSecondsValue;
        if (autoStopDuration == 0) alert("Please select timeframe for stopping the chat");

        else {

            <g:remoteFunction controller="comments" action="stopChat" onSuccess='displayChatStatus(data)' params="'resId='+resId+'&autoStopDuration='+autoStopDuration"/>
            $("#stopChat").hide();
            $("#startChat").show();
            $("#countdownTimer").show();
            $("#countdownTimerButton").hide();
            displayChatStartTime(autoStopDuration);
        }
    }


    function displayChatStartTime(autoStopDuration){

       var dt = new Date();
        dt.setSeconds( dt.getSeconds() + autoStopDuration );
        document.getElementById("countdownTimer").innerHTML = "The chat is stopped till "+dt;

    }

    function openImagePopup(img) {
        var originalImg = document.getElementById("originalImage");
        var imgCaption = document.getElementById("imgCaption");
        originalImg.src = img.src;
        imgCaption.innerHTML = img.alt;
        $("#imgPopupModal").modal("show");
    }
    function setCookieWithExpiry(cname,cvalue,exMinutes) {
        var d = new Date();
        d.setTime(d.getTime() + (exMinutes*60*1000));
        var expires = "expires="+ d.toUTCString();
        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    }




</script>
