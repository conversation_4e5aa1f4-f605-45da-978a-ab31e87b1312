    <g:render template="/${session['entryController']}/navheader_new"></g:render>
<style>

@import url('https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900&display=swap');

    .chat-header{
        /*height: 50px;*/
        background:#ddd;
    }
    .chat-header h4{
        font-size: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0.5rem;
        color: #fff000;

        font-family: 'Roboto', sans-serif;
        font-weight: normal;
    }
    .chat-footer{
        height: 100px;
        background: #eee;
    }
    .chat-body{
        height: calc(100vh - 233px);
        overflow: hidden;
        overflow-y: auto;
        border: 1px solid #ddd;
        position: relative;
    }

    .chat-body h5.message_info {
        font-size: 17px;
        width: 93%;
        margin-bottom: 2px;
        font-weight: normal;
        color: #030303;
        font-family: 'Roboto', sans-serif;
    }

    .chat-body p.sender_name small {
        color: #808080;
        font-size: 11px;
    }
    .chat-body p.sender_name {
        width: 93%;
        margin-bottom: 0;
        font-size: 13px;
        color: #333333;
        font-family: 'Roboto', sans-serif;
    }
    .chat-body p.sender_name span {
        color: #4200FF;
    }
    .customChatWrapper{
        display: none !important;
    }
    @-webkit-keyframes Dots {
        0% {
            content: "";
        }
        33% {
            content: ".";
        }
        66% {
            content: "..";
        }
        100% {
            content: "...";
        }
    }
    @keyframes Dots {
        0% {
            content: "";
        }
        33% {
            content: ".";
        }
        66% {
            content: "..";
        }
        100% {
            content: "...";
        }
    }
    .loading-icon {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        background: rgba(68, 68, 68, 0.9);
        z-index: 9999;
        overflow: hidden;
    }
    .load-wrapper {
        position: absolute;
        top: 35%;
        width: 100%;
    }
.btn-trans{
    background: transparent;
    border: none;
}
    .chat-body .row:first-child{
        margin-top: 1rem;
    }
    .action-btn{
        position: absolute;
        right: 10px;
        top:10px;
    }
    .action-btn button{
        border: none;
        outline: none;
        color: #aaa;
    }
    .action-btn button:hover, .action-btn button:focus {
        color: #000000;
        cursor: pointer;
    }
    .action-btn .dropdown-item {
        font-family: 'Roboto', sans-serif;
    }
    .action-btn .dropdown-item:active {
        background-color: #000000;
        color: #fff !important;
    }
    .form-control{
        outline: none;
    }
    .form-control:focus{
        outline: none;
    }
    .arrow{
        background: #007bff;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        position: sticky;
        bottom: 10px;
        right: 3px;
        float: right;
    }
    textarea {
        resize: none;
        width: 90%;
        font-family: 'Roboto', sans-serif;
    }
.arrow:focus{
    outline: none;
}
    .arrow:active{
        outline: none;
    }
    #inputFocus:focus{
        outline: none !important;
        border:none;
        box-shadow: none;
    }
    .btn-send{
        background: #000;
        border-radius: 50px;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        margin-right: 1rem;
    }
    .btn-send i{
        color:#fff200;
        font-size: 30px;
    }
    .loading-icon {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        background: rgba(68, 68, 68, 0.9);
        z-index: 9999;
        overflow: hidden;
    }
    .load-wrapper {
        position: absolute;
        top: 35%;
        width: 100%;
    }
    .loader-wrapper {
        width: 139px;
        height: 65px;
        background-color: #FFFFFF;
        position: relative;
        top: 50% !important;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
        margin: 0 auto;
        border-radius: 4px;
    }
    .loader,
    .loader:before,
    .loader:after {
        border-radius: 50%;
        width: 2.5em;
        height: 2.5em;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
        -webkit-animation: load7 1.8s infinite ease-in-out;
        animation: load7 1.8s infinite ease-in-out;
    }
    .loader {
        color: #F05A2A;
        font-size: 9px;
        margin: 0 auto;
        position: relative;
        text-indent: -9999em;
        -webkit-transform: translateZ(0);
        -ms-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-animation-delay: -0.16s;
        animation-delay: -0.16s;
    }
    .loader:before,
    .loader:after {
        content: '';
        position: absolute;
        top: 0;
    }
    .loader:before {
        left: -3.5em;
        -webkit-animation-delay: -0.32s;
        animation-delay: -0.32s;
    }
    .loader:after {
        left: 3.5em;
    }
    @-webkit-keyframes load7 {
        0%,
        80%,
        100% {
            box-shadow: 0 2.5em 0 -1.3em;
        }
        40% {
            box-shadow: 0 2.5em 0 0;
        }
    }
    @keyframes load7 {
        0%,
        80%,
        100% {
            box-shadow: 0 2.5em 0 -1.3em;
        }
        40% {
            box-shadow: 0 2.5em 0 0;
        }
    }


    /* Auto scroll */
    .auto_scroll label, .auto_scroll input {
        font-family: 'Roboto', sans-serif;
    }

    .deleted_comment {
        color: #606060;
        font-size: 14px;
        font-family: 'Roboto', sans-serif;
    }
    textarea#commentInput {
        color: #030303;
    }

</style>
    <% if("eutkarsh".equals(session["entryController"])){%>
    <style>
    .logo_icon {
        background-image: url("/assets/eutkarsh/utkarsh-logo.svg");
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        width: 32px;
        height: 32px;
    }
    </style>
    <%}%>
    <% if(session["siteId"] == 21){%>
    <style>
    .logo_icon {
        background-image: url("/assets/clients/winners_logo.jpg");
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        width: 32px;
        height: 32px;
    }
    </style>
    <%}%>
    <% if(session["siteId"] == 34){%>
    <style>
    .logo_icon {
        background-image: url("/assets/clients/jbclasses_logo.png");
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        width: 32px;
        height: 32px;
    }
    </style>
    <%}%>
    <% if("books".equals(session["entryController"])){%>
    <style>
    .logo_icon {
        background-image: url("/assets/landingpageImages/ws-tabicon.svg");
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        width: 32px;
        height: 32px;
    }
    </style>
    <%}%>
<link href="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/css/bootstrap4-toggle.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/js/bootstrap4-toggle.min.js"></script>

<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section style="height: 100vh;">

<div class="container text-right my-3 auto_scroll">
    <label for="auto_scroll_chk" class="mb-0">
        <strong class="mr-1">Auto Scroll</strong>
        <input checked type="checkbox" id="auto_scroll_chk" name="auto_scroll_chk" value="1" onchange="autoScrollChkChanged(this)" data-toggle="toggle" data-size="sm" data-onstyle="success">
    </label>
</div>

<div class="container">
    <div class="chat-header bg-dark text-white shadow">
    <h4 class="text-center mb-0 py-3"><span>${bookName}</span><span>|</span><span>${chapterName}</span><span>|</span><span>${videoName}</span></h4>
    </div>
    <div class="chat-body">
<div class="media position-relative ml-2 mt-2">
</div>
<button onclick="autoScrollClicked()" class="arrow shadow" style="display: none;" id="downArrow" type="button">
    <i class="material-icons">arrow_downward</i>
</button>
     </div>
    <div class="chat-footer align-items-center border">
        <div class="d-flex">
                <textarea id="commentInput" onkeydown="commentInputKeyDown(event)" class="form-control mt-3 ml-2 shadow"></textarea>
                <button class="btn btn-trans btn-send mt-4 shadow" id="commentSubmitBtn" type="button">
                    <i class="material-icons">near_me</i>
                </button>
        </div>
    </div>
</div>

</div>
</section>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.1.4/sockjs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>

<script>
    var allCommentsHeight = 0;
    var resId = "${params.resId}";
    var latestCommentId = 0;
    //var firstCall = '0';
    var intervalValueId;
    var showArrowIcon=0;
    var autoScrollChecked = 1;
    /*
    httpPollingRequestStatus values
    "started" after request is sent to server for new comments
    "finished" after new comments are fetched from response
    "displayed" after new comments are displayed
    "waiting" during the time interval between the successive httpPolling requests
    */

    var httpPollingRequestStatus = "";
    var commentsBuffer = {};
    var commentsBufferIdsArr = [];
    function getAllUserComments() {
        $('.loading-icon').addClass('hidden');
        if(resId == undefined || resId == '0' || resId == '' || resId == 0 || resId == null || resId.toLowerCase() == 'null'){
            alert("Please enter valid RES-ID");
            var url = "/comments/chatLogin";
            window.location.href=url
        }
        //firstCall = '1';
        <g:remoteFunction controller="comments" action="getAllUserComments" onSuccess='displayComments(data,httpPollingRequired)'
        params="'siteId=1&resId='+resId"/>
%{--        <g:remoteFunction controller="comments" action="getResourcedetailsByResId" onSuccess='displayResName(data)'--}%
%{--        params="'siteId=1&resId='+resId"/>--}%
    }

    function establishWebSocketConnection() {
        var topic = resId;
        var url = location.protocol +"//"+ location.hostname ;
        // var url = "https://publish.wonderslate.com"
        // url = url +"/wonderlive/ws";
        // this.webSocketEndPoint = url;
        var socket = new SockJS("${createLink(uri: '/stomp')}");
        stompClient = Stomp.over(socket);

        stompClient.connect({}, onConnected, onError);
    }

    function onConnected() {
        // Subscribe to the Public Topic
        getAllUserComments();
        stompClient.subscribe('/topic/'+resId, onMessageReceived);
        httpPollingRequired = false;
    }


    function onMessageReceived(payload) {
        var commentStr = JSON.stringify(payload.body);
        var commentObj = JSON.parse(JSON.parse(commentStr));
        console.log(httpPollingRequestStatus);
       if(httpPollingRequestStatus == "started"){
           var key = commentObj.messageId;
           commentsBuffer["'"+key+"'"] = commentObj;
           commentsBufferIdsArr.push(key);
       }else {
            httpPollingRequired = false;
            displayCommentsOnMessage(commentObj);
        }
    }

    function onError(error) {
        httpPollingRequired = true;
        console.log("onError called*********")
        getAllUserComments();
        console.log(error);
    }

    function displayComments(data,httpPolling){
        var divOriginalHeight=$('.chat-body').innerHeight();
        if(divOriginalHeight < allCommentsHeight) {
            $("#downArrow").show();
        }
        var commentsArr = data['comments'];
        if(commentsArr != undefined){
            if(latestCommentId == 0) latestCommentId = commentsArr[0]['messageId'];
            for(var i= commentsArr.length-1 ; i >= 0 ; i-- ){
                if(document.getElementById("inputFocus") == null || document.getElementById("inputFocus") == undefined){
                    var str = '<input id="inputFocus" style="width: 0px;height: 0px;border: none">';
                    $(str).insertAfter($('.media').last());
                }
                try{
                    if(commentsArr[i]['adminUser'] == "true"){
                        var str = '<div id="'+commentsArr[i]['messageId']+'" class="media position-relative ml-2 mt-2">' +
                                    '   <div class="mr-3 mt-1 rounded-circle logo_icon"></div>\n' +
                                         "<div class=\"media-body\"> \n" +
                                            "  <p class='sender_name'> <small><i>"+moment.utc(commentsArr[i]['dateCreated']).local().format("DD-MM-YYYY h:mm a")+"</i></small></p>\n" +
                                                "<h5 class='message_info'>"+decodeURI(commentsArr[i]['message'].replace(/"/g,"").replace(/~~/g,",").replace(/~/g,":"))+"</h5>\n" +
                                        "</div>\n" +
                                        "<div class=\"dropdown action-btn dropleft\">\n" +
                                        "    <button type=\"button\" class=\"btn-trans\" data-toggle=\"dropdown\">\n" +
                                        "        <i class=\"material-icons\">\n" +
                                        "            more_vert\n" +
                                        "        </i>\n" +
                                         "    </button>"+
                                        "  <div class=\"dropdown-menu p-0 mr-0\">\n" +
                                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete comment</a>\n" +
                                        "  </div>\n" +
                                        "</div>"+
                            '  </div>';
                        $(str).insertAfter($('.media').last());
                    }else{
                        var str = '<div id="'+commentsArr[i]['messageId']+'" class="media position-relative ml-2 mt-2">' +
                            '   <img src="" alt="" class="mr-3 mt-1 rounded-circle" style="width:32px;">\n' +
                            "<div class=\"media-body\"> \n" +
                            "  <p class='sender_name'><span>"+commentsArr[i]['sender']+"</span>, "+commentsArr[i]['username']+" <small><i>"+" "+moment.utc(commentsArr[i]['dateCreated']).local().format("DD-MM-YYYY h:mm a")+"</i></small></p>\n" +
                            "<h5 class='message_info'>"+decodeURI(commentsArr[i]['message'].replace(/"/g,"").replace(/~~/g,",").replace(/~/g,":"))+"</h5>\n" +
                            "</div>\n" +
                            "<div class=\"dropdown action-btn dropleft\">\n" +
                            "    <button type=\"button\" class=\"btn-trans\" data-toggle=\"dropdown\">\n" +
                            "        <i class=\"material-icons\">\n" +
                            "            more_vert\n" +
                            "        </i>\n" +
                            "    </button>"+
                            "  <div class=\"dropdown-menu p-0 mr-0\">\n" +
                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','block','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Block user</a>\n" +
                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','unblock','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Unblock user</a>\n" +
                            "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete comment</a>\n" +
                            "  </div>\n" +
                            "</div>"+
                            '  </div>';
                        $(str).insertAfter($('.media').last());
                    }
                }catch (e) {
                    continue;
                }
                allCommentsHeight = allCommentsHeight + $("#"+commentsArr[i]['messageId']).innerHeight();
                // console.log(allCommentsHeight);
            }
            // if(firstCall == '1') {
            //     $( "#inputFocus" ).focus();
            //     firstCall = '0';
            // }
        }
        if(showArrowIcon == 1) {
            $("#downArrow").show();
        }else{
            // $( "#inputFocus" ).focus();
            // $("#downArrow").hide();
            var objDiv = document.getElementsByClassName("chat-body")[0];
            objDiv.scrollTop = (objDiv.scrollHeight );
        }

        if(httpPolling == true || httpPolling == "true") {
            getNewCommentsById();
        }
    }

    function displayCommentsOnMessage(data){
        allCommentsHeight = allCommentsHeight + $("#"+data['messageId']).innerHeight();
        var divOriginalHeight=$('.chat-body').innerHeight();
        // if(divOriginalHeight < allCommentsHeight) {
        //     $("#downArrow").show();
        // }
        try{
           if(latestCommentId < data['messageId']) latestCommentId = data['messageId'];
            if(data['admin'] == "true" || data['admin'] == true){
                var str = '<div id="'+data['messageId']+'" class="media position-relative ml-2 mt-2">' +
                    '   <div class="mr-3 mt-1 rounded-circle logo_icon"></div>\n' +
                    "<div class=\"media-body\"> \n" +
                    "  <p class='sender_name'> <small><i>"+moment.utc(data['dateCreated']).local().format("DD-MM-YYYY h:mm a")+"</i></small></p>\n" +
                    "<h5 class='message_info'>"+decodeURI(data['message'].replace(/"/g,"").replace(/~~/g,",").replace(/~/g,":"))+"</h5>\n" +
                    "</div>\n" +
                    "<div class=\"dropdown action-btn dropleft\">\n" +
                    "    <button type=\"button\" class=\"btn-trans\" data-toggle=\"dropdown\">\n" +
                    "        <i class=\"material-icons\">\n" +
                    "            more_vert\n" +
                    "        </i>\n" +
                    "    </button>"+
                    "  <div class=\"dropdown-menu p-0 mr-0\">\n" +
                    "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+data['username']+"','delete','"+data['messageId']+"','"+data['adminUser']+"')\" >Delete comment</a>\n" +
                    "  </div>\n" +
                    "</div>"+
                    '  </div>';
                $(str).insertAfter($('.media').last());
            }else{
                var str = '<div id="'+data['messageId']+'" class="media position-relative ml-2 mt-2">' +
                    '   <img src="" alt="" class="mr-3 mt-1 rounded-circle" style="width:32px;">\n' +
                    "<div class=\"media-body\"> \n" +
                    "<p class='sender_name'><span>"+data['sender']+"</span>, "+data['username']+" <small><i>"+" "+moment.utc(data['dateCreated']).local().format("DD-MM-YYYY h:mm a")+"</i></small></p>\n" +
                    "<h5 class='message_info'>"+decodeURI(data['message'].replace(/"/g,"").replace(/~~/g,",").replace(/~/g,":"))+"</h5>\n" +
                    "</div>\n" +
                    "<div class=\"dropdown action-btn dropleft\">\n" +
                    "    <button type=\"button\" class=\"btn-trans\" data-toggle=\"dropdown\">\n" +
                    "        <i class=\"material-icons\">\n" +
                    "            more_vert\n" +
                    "        </i>\n" +
                    "    </button>"+
                    "  <div class=\"dropdown-menu p-0 mr-0\">\n" +
                    "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+data['username']+"','block','"+data['messageId']+"','"+data['adminUser']+"')\" >Block user</a>\n" +
                    "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+data['username']+"','unblock','"+data['messageId']+"','"+data['adminUser']+"')\" >Unblock user</a>\n" +
                    "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+data['username']+"','delete','"+data['messageId']+"','"+data['adminUser']+"')\" >Delete comment</a>\n" +
                    "  </div>\n" +
                    "</div>"+
                    '  </div>';
                $(str).insertAfter($('.media').last());
            }
        }catch (e) {
            console.log(e);
        }
        if(showArrowIcon == 1) {
            $("#downArrow").show();
        }else{
            // $( "#inputFocus" ).focus();
            // $("#downArrow").hide();
            var objDiv = document.getElementsByClassName("chat-body")[0];
            objDiv.scrollTop = (objDiv.scrollHeight );
        }
        if(httpPollingRequired == true) getNewCommentsById();
    }

    establishWebSocketConnection();
    function userAction(userName,action,messageId,admin) {
        var url = "";
        if(action == 'block') url = "/comments/blockUserComments";
        else if(action == 'unblock') url = "/comments/unblockUserComments";
        else if(action == 'delete') url = "/comments/removeQuestion"
        var xhttp = new XMLHttpRequest();
        xhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                // document.getElementById("demo").innerHTML = this.responseText;
                if(action == 'delete'){
                    if(admin == "true"){
                        var str = '' +
                            // '    <img src="/assets/eutkarsh/utkarsh-logo.svg" class="mr-3" alt="...">\n' +
                            // "<div class=\"dropdown\">\n" +
                            // "  <button type=\"button\" class=\"btn-blocks\" data-toggle=\"dropdown\">\n" +
                            // "<i class=\"material-icons\">\n" +
                            // "more_vert\n" +
                            // "</i>"+
                            // "  </button>\n" +
                            // "  <div class=\"dropdown-menu\">\n" +
                            // // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','block','"+commentsArr[i]['messageId']+"')\" >Block user</a>\n" +
                            // // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','unblock','"+commentsArr[i]['messageId']+"')\" >Unblock user</a>\n" +
                            // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete Message</a>\n" +
                            // "  </div>\n" +
                            // "</div>"+
                            '    <div class="deleted_comment">\n' +
                                '<em>\n' +
                                '\n' + decodeURI("This comment is deleted") +
                                '</em>\n' +
                            // ' <p style="float: right"> '+moment.utc(commentsArr[i]['dateCreated']).local().format("DD-MM-YYYY h:mm a")+'</p> ' +
                            '    </div>\n' +
                            '  ';
                        $('#'+messageId).html(str);
                    }else{
                        var str = '' +
                            '    <div class="deleted_comment">\n' +
                                '<em>\n' +
                                '\n' + decodeURI("This comment is deleted") +
                                '</em>\n' +
                            // ' <p style="float: right"> '+moment.utc(commentsArr[i]['dateCreated']).local().format("DD-MM-YYYY h:mm a")+'</p> ' +
                            '    </div>\n' +
                            // "<div class=\"dropdown\">\n" +
                            // "  <button type=\"button\" class=\"btn-blocks\" data-toggle=\"dropdown\">\n" +
                            // "<i class=\"material-icons\">\n" +
                            // "more_vert\n" +
                            // "</i>"+
                            // "  </button>\n" +
                            // "  <div class=\"dropdown-menu\">\n" +
                            // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','block','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Block user</a>\n" +
                            // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','unblock','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Unblock user</a>\n" +
                            // "    <a class=\"dropdown-item\" style=\"cursor: pointer\" onclick=\"userAction('"+commentsArr[i]['username']+"','delete','"+commentsArr[i]['messageId']+"','"+commentsArr[i]['adminUser']+"')\" >Delete Message</a>\n" +
                            // "  </div>\n" +
                            // "</div>"+
                            // '    <p class="username">' +
                            // '\n' + commentsArr[i]['sender'] +
                            // '</p>\n' +
                            '';
                        $('#'+messageId).html(str);
                    }
                }
            }
        };
        xhttp.open("POST",url , true);
        xhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xhttp.send("username="+userName+"&messageId="+messageId);
    }

    function autoScrollClicked() {
        // $( "#inputFocus" ).focus();
        // $("#downArrow").hide();
        // showArrowIcon = 0;
        var objDiv = document.getElementsByClassName("chat-body")[0];
        objDiv.scrollTop = (objDiv.scrollHeight );
        if(autoScrollChecked == 0)  {
            showArrowIcon = 1;
        }else if(autoScrollChecked == 1){
            showArrowIcon = 0;
        }
    }

$('#commentSubmitBtn').on('click',function(){
    var msg = $('#commentInput').val();
    console.log(msg);
    msg = msg.replace(/%/g," ");
    $('#commentInput').val('');
    if(msg != '' && msg != undefined){
        <g:remoteFunction controller="comments" action="addQuestion" onFailure='getNewCommentsById();' onSuccess="checkForAddQuestionStatus(data)"
        params="'siteId=1&resId='+resId+'&question='+msg"/>

    }

})

    function getNewCommentsById() {
        httpPollingRequestStatus = "started"
        <g:remoteFunction controller="comments" action="getNewCommentsById" onSuccess='checkToDisplayCommentsOrNot(data,httpPollingRequired);'
        params="'siteId=1&resId='+resId+'&questionId='+latestCommentId"/>
    }

    function checkToDisplayCommentsOrNot(data,httpPollingRequired){
        var comments = data['comments'];
        if(latestCommentId > 0 && comments.length > 0 && latestCommentId < comments[0]['messageId']) {
            latestCommentId = comments[0]["messageId"];
            if(httpPollingRequired == false){
                console.log("websocket working...");
                var commentsIdsArr = []
                for(var i=0; i<comments.length; i++){
                    commentsIdsArr.push(comments[i]['messageId']);
                }
                for(var j=0; j<commentsBufferIdsArr.length; j++){
                    if(commentsIdsArr.indexOf(commentsBufferIdsArr[i]) == -1) comments.push(commentsBuffer["'"+commentsBufferIdsArr[i]+"'"]);
                }
                data['comments'] = [];
                data['comments'] = comments;
                displayComments(data,httpPollingRequired);
            }
            else{
                displayComments(data,httpPollingRequired)
            }
        }else { //if(latestCommentId > 0)
            setTimeout(function(){
                if(httpPollingRequired == true) getNewCommentsById();
            }, 15000);

        }

    }

    function checkForAddQuestionStatus(data){
        if(data['id'] != null && data['id'] != 0 && data['id'] != undefined && data['id'] != '0') {
            if(latestCommentId < data['id']) {
                // latestCommentId = data['id'];
                if(data['result'] == "error" || data['result'] == "noaccess") {
                    httpPollingRequired = true;
                    displayCommentsOnMessage(JSON.parse(data['data']))
                }
            }
            else httpPollingRequired = false;
        }
        else {
            window.location.reload();
        }
    }

    function commentInputKeyDown(event){
        if (event.defaultPrevented) {
            return;
        }
        var handled = false;
        if (event.key !== undefined) {
            if (event.altKey && event.key === 'Enter') {
                $( "#commentSubmitBtn" ).trigger( "click" );
            }
        } else if (event.keyIdentifier !== undefined) {
            if (event.altKey && event.keyIdentifier === "Enter") {
                $( "#commentSubmitBtn" ).trigger( "click" );
            }

        } else if (event.keyCode !== undefined) {
        } else if (event.keyCode !== undefined) {
            if (event.altKey && event.keyCode === 13) {
                $( "#commentSubmitBtn" ).trigger( "click" );
            }
        }
        if (handled) {
            event.preventDefault();
        };
    };
    // jQuery(function($) {
    //     $('.chat-body').on('scroll', function() {
    //         if($(this).scrollTop() + $(this).innerHeight() < $(this)[0].scrollHeight) {
    //             showArrowIcon = 1;
    //             $('.arrow').show();
    //         }else if($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight) {
    //             showArrowIcon = 0;
    //             $('.arrow').hide();
    //         }
    //     })
    // });

    $('.chat-body').on('scroll', function() {
        // alert();
        if($(this).scrollTop() + $(this).innerHeight() < $(this)[0].scrollHeight) {
            if(autoScrollChecked == 0)  {
                showArrowIcon = 1;
                $('.arrow').show();
            }else if(autoScrollChecked == 1){
                showArrowIcon = 0;
                $('.arrow').hide();
            }
        }else if($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight) {
            showArrowIcon = 0;
            $('.arrow').hide();
        }
    })

    function autoScrollChkChanged(cb){
        if(cb.checked == true){
            autoScrollChecked = 1;
            showArrowIcon = 0;
        }else{
            autoScrollChecked = 0;
            showArrowIcon = 1;
        }
    }
</script>
