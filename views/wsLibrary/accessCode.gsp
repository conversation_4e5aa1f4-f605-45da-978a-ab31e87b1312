<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js pb-5 pt-0 institute-access-code">
    <div class="container pt-4 pt-md-5 mt-3 mt-md-0">

        <div class="d-flex justify-content-start align-items-center page_title col-12 col-md-10 mx-auto pb-2 px-0">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn d-flex" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
            <% if(params.bookTitle!=null) { %>
            <h3><strong>Scratch Code for  ${params.bookTitle}</strong></h3>
            <%} else {%>
            <h3><strong>Scratch Code</strong></h3>
            <%} %>
        </div>

        <div class='row col-12 col-md-10 mx-auto px-0'>
            <div class="access_code my-4 w-75">
                <form id="bookAccessForm" novalidate>
                    <div class="form-row mt-2">
                        <div class="col-8 col-lg-6">
                            <div class="form-group form-group-modifier">
                                <input type="text" id="accessCode" value="" class="form-control form-control-modifier border-bottom text-uppercase" placeholder="Enter scratch code here" aria-describedby="codeSubmit" required autocomplete="off">
                            </div>
                        </div>
                        <button id="codeSubmit" class="btn btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-3 col-lg-2" type="button" onclick="checkAccessCode();">
                            SUBMIT
                        </button>
                    </div>
                    <div id="invalidAccessCode" class="form-text form-text-modifier text-danger hidden"></div>
                </form>
            </div>
        </div>

    </div>
</section>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>

<script>

    function checkAccessCode(){
        var bookLevel="${params.bookLevel}"
        var bookId="${params.bookId}"
        var accessCode  = document.getElementById("accessCode").value;
        $('.loading-icon').removeClass('hidden');
        if((siteId==66) || (siteId==25)){
            <g:remoteFunction controller="wsLibrary" action="checkInstituteAccessCode"  onSuccess='accessModeChecked(data);' params="'accessCode='+accessCode"/>
        }else {
            <g:remoteFunction controller="wsshop" action="bookCdValidate"  onSuccess='bookAccessModeChecked(data);' params="'bookCode='+accessCode+'&bookLevel='+bookLevel+'&bookId='+bookId"/>
        }
    }

    function bookAccessModeChecked(data) {
        var status = data.status;
        if(status.indexOf("campaignCode_")!=-1){
         // redirect to /privatelabel/accessCodeBooks page
            var campaignCode = status.split("_")[1];
            window.location.href = "/privatelabel/accessCodeBooks?campaignCode="+campaignCode+"&accessCode="+document.getElementById("accessCode").value;
        }else {
            $('.loading-icon').addClass('hidden');
            var btnText, btnColor, redirectUrl;

            btnText = "Go to My books";
            btnColor = "#367AD8";
            if (siteId == 1) {
                redirectUrl = "/wsLibrary/myLibrary?mode=mybooks";
            } else {
                redirectUrl = "/wsLibrary/myLibrary";
            }

            if (status == "allocated") {
                swal({
                    title: "Book Access Granted!",
                    text: "Book successfully added to your library.",
                    type: "success",
                    allowOutsideClick: false,
                    showConfirmButton: true,
                    showCancelButton: false,
                    confirmButtonColor: btnColor,
                    confirmButtonText: btnText,
                    cancelButtonText: "Cancel",
                    closeOnConfirm: true,
                    closeOnCancel: false,
                    allowEscapeKey: false,
                    customClass: '',
                }, function () {
                    $('.loading-icon').removeClass('hidden');
                    window.location.href = redirectUrl;
                });
            } else {
                document.getElementById("invalidAccessCode").innerHTML = "Invalid code";
                $("#invalidAccessCode").removeClass('hidden');
                $("#accessCode").focus();
            }
        }
    }

    function accessModeChecked(data) {
        var status = data.status;
        var instituteId = data.instituteId;
        $('.loading-icon').addClass('hidden');
        var btnText, btnColor, redirectUrl;
        if(siteId==1) {
            btnText = "Okay";
            btnColor = "#27AE60";
            redirectUrl = "/books/home";
        } else {
            btnText = "Go to My Books";
            btnColor = "#367AD8";
            redirectUrl = "/wsLibrary/myLibrary";
        }
        if(status=="OK"){
            localStorage.setItem('instituteAccess',instituteId);
            swal({
                title: "Access Granted!",
                text: "Institute successfully added to your library.",
                type: "success",
                allowOutsideClick: false,
                showConfirmButton: true,
                showCancelButton: false,
                confirmButtonColor: btnColor,
                confirmButtonText: btnText,
                cancelButtonText: "Cancel",
                closeOnConfirm: true,
                closeOnCancel: false,
                allowEscapeKey: false,
                customClass: '',
            }, function() {
                $('.loading-icon').removeClass('hidden');
                window.location.href = redirectUrl;
            });
        }else {
            document.getElementById("invalidAccessCode").innerHTML = status;
            $("#invalidAccessCode").removeClass('hidden');
            $("#accessCode").focus();
        }
    }

    $(document).ready(function(){
        $("#accessCode").focus().keyup(function () {
            $("#invalidAccessCode").addClass('hidden');
            this.value = this.value.replace(/[^a-zA-Z0-9]/g, '');
        });
    });

</script>


</body>
</html>
