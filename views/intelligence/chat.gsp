<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" referrerpolicy="no-referrer" />
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="/assets/bookgpt/externalchat.css">
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Y0QVWCD9K" defer></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-6CJSRG577K');
    </script>
</head>
<body>
<div class="chat-container">
    <div class="chat-box-wrap">
        <h2 class="ch-title">${chapterTitle}</h2>
        <div class="chat-box" id="chatBox"></div>
        <div class="is-typing" style="display:none;margin-left: 20px;">
            <div class="jump1"></div>
            <div class="jump2"></div>
            <div class="jump3"></div>
        </div>
    </div>


    <div class="chat">
        <div class="chatInputWrapper">
            <textarea class="input-field chatInputField" id="inputField" placeholder='Ask any question about the chapter...'></textarea>
        </div>
        <div class="chatInputOptions">
            <button class='formulaBtn'>
                <i class='fa-solid fa-square-root-variable'></i>
            </button>
            <button class='send-btn sendIcon' id='sendBtn'>
                <i class='fa-solid fa-paper-plane'></i>
            </button>
        </div>
    </div>
</div>

<!-- Modal Structure -->
<div id="modal" class="modal">
    <div class="modal-content">
        <span id="closeModal" class="close-icon">&times;</span>
        <div id="modalBody">
            <!-- Replaceable Content -->
            <h5>Modal Title</h5>
            <p>This is a reusable modal. You can customize the content here.</p>
        </div>
    </div>
</div>
<script>
    const inputField = document.getElementById('inputField');
    const chatBoxwrap = document.querySelector('.chat-box-wrap');
    const formulaBtn = document.querySelector('.formulaBtn')
    const modal = document.getElementById('modal');
    const closeModal = document.getElementById('closeModal');
    const isTyping = document.querySelector('.is-typing')
    var typingWorker;
    let curId = null
    const chat_history = [];
    let isResponseComplete = true
    let namespace = "${params.chapterId}"+"_"+"${params.resId}";
    let isPDFReady = false
    let doubtEntered = false

    document.getElementById('sendBtn').addEventListener('click', ()=>{
        doubtEntered = true
        chatInitiate()
    })
    inputField.addEventListener('keydown',(e)=>{
        if(e.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            doubtEntered = true
            chatInitiate()
        }
    })

    function chatInitiate(){
        if(inputField.value.trim()!=='' && isResponseComplete && isPDFReady){
            sendMessage()
        }else if(!isPDFReady){
            alert("Please wait while reading materials is being processed.")
        }else{
            pauseShowingAnswer()
        }
    }
    async function sendMessage() {
        curId++;
        const message = inputField.value.trim();
        isTyping.style.display='flex';
        isResponseComplete = false
        doubtEntered = false
        if (message !== "") {
            addMessage(message, 'student');
            let reqObj = {
                namespace:namespace,
                query:message,
                resType:"userInput",
                chatHistory:chat_history,
                resId: ${params.resId},
                chapterId:${params.chapterId},
            }
            inputField.value = "";
            inputField.focus();
            const response = await fetch('/prompt/retrieveData',{
                method:"POST",
                body:JSON.stringify(reqObj),
                headers:{
                    "Content-Type":"application/json"
                }
            })
            isTyping.style.display='none'
            const result = await response.json()
            chat_history.push({
                user: result.query ? result.query : "",
                ai: result.answer ? result.answer : ""
            });
            addMessage(result.answer, 'bot');
        }
    }

    function addMessage(message, type) {
        const chatBox = document.getElementById('chatBox');
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message');

        const icon = document.createElement('div');
        icon.classList.add('icon');

        const bubble = document.createElement('div');
        bubble.classList.add('bubble');

        if(type==="student"){
            bubble.id = "student_"+curId
            bubble.innerText = message;
            messageDiv.classList.add('student-message');
            icon.style.backgroundColor = '#efecfe' // Different colors for each
            icon.innerHTML="<img src='/assets/resource/student-icon.svg'/>"
        }else{
            bubble.id = "bot_"+curId
            icon.style.backgroundColor = '#d8d8ffe6'; // Different colors for each
            icon.innerHTML="<img src='/assets/resource/tutor-icon.svg'/>"
            messageDiv.classList.add('bot-message');

        }

        messageDiv.appendChild(icon);
        messageDiv.appendChild(bubble);
        chatBox.appendChild(messageDiv);
        chatBoxwrap.scrollTop = chatBoxwrap.scrollHeight;
        if(type==="student"){
            renderMathInElement(bubble);
        }else {
            document.getElementById('sendBtn').innerHTML = '<i class="fa-solid fa-pause"></i>'
            typeWriter(message, 0);
            chatBoxwrap.scrollTop = chatBoxwrap.scrollHeight + 50;
        }
    }
    function typeWriter(text, i, callback) {
        var answerDiv = document.getElementById("bot_"+curId);
        typingWorker = new Worker('/assets/bookGPTScripts/typeWorker.js');

        typingWorker.postMessage({ text: text, index: i, speed: 4 });

        typingWorker.onmessage = function (e) {
            if (e.data.done) {
                typingWorker.terminate();
                typingWorker = null;

                const tempEl = document.createElement("div");
                tempEl.innerHTML = text;
                renderMathInElement(tempEl, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });
                const renderedHtml = marked.parse(tempEl.innerHTML);
                answerDiv.innerHTML = renderedHtml;
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });

                if (callback) {
                    callback();
                }
                chatBoxwrap.scrollTop = chatBoxwrap.scrollHeight;
                isResponseComplete = true
                document.getElementById('sendBtn').innerHTML = '<i class="fa-solid fa-paper-plane"></i>';
            } else {
                const tempEl = document.createElement("div");
                tempEl.innerHTML = text.substring(0, e.data.index);
                renderMathInElement(tempEl, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });
                answerDiv.innerHTML = marked.parse(tempEl.innerHTML);
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });
            }
        };
    }

    function pauseShowingAnswer(){
        if (typingWorker) {
            typingWorker.terminate();
            typingWorker = null;  // Reset the worker reference
        }
        isResponseComplete = true;
        chatBoxwrap.scrollTop = chatBoxwrap.scrollHeight
        document.getElementById('sendBtn').innerHTML = '<i class="fa-solid fa-paper-plane"></i>';
    }
    formulaBtn.addEventListener('click',()=>{
        let inputHTML = "<div class='feedbackOptions'>" +
            "<div class='formulaModalContent'>" +
            "<p>Write Formula LaTex</p>" +
            "<textarea id='formulaInputField'></textarea>" +
            "</div>" +
            "<div id='formulaPreviewDiv' class='formulaPreviewDiv'>" +
            "</div>"+
            "<div class='formulaActions'>" +
            "<div>" +
            "<button class='addFormula'>Add Formula</button>" +
            "</div>" +
            "<a href='https://en.wikibooks.org/wiki/LaTeX/Mathematics' target='_blank'>LaTex Documentation</a> "+
            "</div>"+
            "</div>";

        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = ""
        modalBody.innerHTML = inputHTML
        modal.style.display = 'flex';

        const addFormula = document.querySelector('.addFormula')
        const formulaInputField = document.getElementById('formulaInputField')
        const formulaPreviewDiv = document.getElementById('formulaPreviewDiv')
        formulaInputField.value = "\\[ x^n + y^n = z^n \\]"
        renderFormula()
        addFormula.addEventListener('click',()=>{
            if(formulaInputField.value.trim()!=""){
                const chatInp = document.getElementById('inputField')
                const cursorPosition = chatInp.selectionStart;
                const value = chatInp.value;
                const newValue = formulaInputField.value
                chatInp.value = newValue
                chatInp.setSelectionRange(cursorPosition + formulaInputField.value.length, cursorPosition + formulaInputField.value.length);
            }
            modal.style.display = 'none';
        })
        function renderFormula() {
            try {
                formulaPreviewDiv.textContent = formulaInputField.value.trim()
                renderMathInElement(formulaPreviewDiv);
            } catch (error) {
                formulaPreviewDiv.innerHTML = "Invalid formula";
            }
        }
        formulaInputField.addEventListener('input', renderFormula);
    })
    closeModal.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    async function checkPdfExists(){
        const checkPDF = await fetch('/prompt/checkPDFExists?namespace='+namespace,{
            method:"get"
        })

        const isPDFExist = await checkPDF.json()
        if(!isPDFExist.isExist){
            isPDFReady = false
            namespace = isPDFExist.namespace
            await storePDF()
        }else{
            namespace = isPDFExist.namespace
            isPDFReady = true
        }
    }

    async function storePDF(){
        try {
            const end_point = "/prompt/storePdfVectors?bookId="+"${params.bookId}"+"&chapterId="+"${params.chapterId}"+"&resId="+"${params.resId}"
            const pdfres = await fetch(end_point)
            const pdfStore = await pdfres.json()
            if(pdfStore.status!='OK'){
                throw new Error("Something went wrong")
            }else{
                namespace = pdfStore.namespace;
                isPDFReady = true
                if(doubtEntered){
                    chatInitiate()
                }
            }
        }catch (e) {
            console.log(e)
            alert(e)
        }
    }
    checkPdfExists()
</script>
</body>
</html>
