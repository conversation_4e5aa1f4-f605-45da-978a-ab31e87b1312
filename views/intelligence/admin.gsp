<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/bookgpt/externalgpt.css">
    <title>${pageTitle}</title>
</head>

<body>
<div id="loader" class="loader-container">
    <div class="app-loader"></div>
</div>
<div class="admin-dash">
    <div class="container">
        <header class="header">
            <h1>GPT Books</h1>
        </header>
        <main>
            <div id="book-grid" class="book-grid">
                <!-- Books will be rendered here dynamically -->
            </div>
            <div class="pagination">
                <button id="prev-page" disabled>Previous</button>
                <button id="next-page">Next</button>
            </div>
        </main>
    </div>
</div>
</body>
<script>
    const loader = document.getElementById("loader");
    const API_URL = "/intelligence/getPublisherBooksWithType";
    const publisherId = "${params.publisherId}";
    const bookType = "bookgpt";
    var siteId=${session["siteId"]};
    let offset = 0;
    const limit = 10;
    const bookGrid = document.getElementById("book-grid");
    const prevPageButton = document.getElementById("prev-page");
    const nextPageButton = document.getElementById("next-page");

    async function fetchBooks(offset) {
        try {
            loader.style.display = "flex";
            const url = API_URL + "?publisherId=" + publisherId + "&bookType=" + bookType + "&offset=" + offset+"&siteId="+siteId+"&key="+"${params.key}";
            bookGrid.innerHTML = "";
            const response = await fetch(url);
            const data = await response.json();

            if (!data.error && data.books.length > 0) {
                renderBooks(data.books);
                togglePaginationButtons(data.books.length);
            } else {
                alert(data.message || "No more books available.");
                togglePaginationButtons(0);
            }
        } catch (error) {
            console.error("Error fetching books:", error);
            alert("Failed to fetch books. Please try again.");
        }finally {
            loader.style.display = "none";
        }
    }

    function renderBooks(books) {
        books.forEach(function (book) {
            const bookCard = document.createElement("div");
            bookCard.className = "book-card";

            const coverImage = document.createElement("div");
            coverImage.className = "book-image";

            coverImage.style.backgroundImage = book.coverImage
                ? "url('/funlearn/showProfileImage?id="+book.id+"&fileName="+book.coverImage+"&type=books&imgType=passport')"
                : "url('https://placehold.co/150x200?text=No+Image')";

            const bookTitle = document.createElement("div");
            bookTitle.className = "book-title";
            bookTitle.textContent = book.title;

            bookCard.appendChild(coverImage);
            bookCard.appendChild(bookTitle);
            bookCard.addEventListener("click", function () {
                window.location.href = "/intelligence/bookChapters?bookId=" + book.id +"&siteId="+siteId;
            });
            bookGrid.appendChild(bookCard);
        });
    }

    function togglePaginationButtons(booksFetched) {
        prevPageButton.disabled = offset === 0;
        nextPageButton.disabled = booksFetched < limit; // Disable next if fewer books than limit
    }

    prevPageButton.addEventListener("click", async function () {
        if (offset >= limit) {
            offset -= limit;
            await fetchBooks(offset);
        }
    });

    nextPageButton.addEventListener("click", async function () {
        offset += limit;
        await fetchBooks(offset);
    });

    document.addEventListener('DOMContentLoaded',()=>{
        fetchBooks(offset);
    })
    loader.style.display = "flex";
</script>
</html>
