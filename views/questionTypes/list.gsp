<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>
    .table-bordered th,td {
        padding: 10px;
    }

    @media (min-width: 576px) {
        .modal-dialog-centered {
            min-height: calc(100% - 3.5rem);
        }
    }
    @media (min-width: 576px) {
        .modal-dialog {
            margin: 1.75rem auto;
        }
    }
    /* Add a border to the table */
    table {
        border-collapse: collapse;
        width: 100%;
    }

    /* Add a border to table cells */
    th, td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    /* Set a light background color for the row headers */
    th {
        background-color: #f2f2f2;
        color: black;
    }
    .form-group a {
        color: white;
    }
    
    .btn-add {
        margin-bottom: 20px;
    }
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Question Types Management</h3>
                    
                    <g:if test="${flash.message}">
                        <div class="alert alert-info">${flash.message}</div>
                    </g:if>
                    
                    <div class="text-right">
                        <a href="${createLink(controller: 'questionTypes', action: 'create')}" class="btn btn-primary btn-add">
                            <i class="fa fa-plus"></i> Add New Question Type
                        </a>
                    </div>
                    
                    <div class="form-group table-responsive" id="question-types-table">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Question Type</th>
                                    <th>Created By</th>
                                    <th>Date Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <g:each in="${questionTypes}" var="type">
                                    <tr>
                                        <td>${type.id}</td>
                                        <td>${type.questionType}</td>
                                        <td>${type.createdBy}</td>
                                        <td>
                                            <g:formatDate date="${type.dateCreated}" format="dd-MMMM-yyyy" timeZone="IST"/>
                                        </td>
                                        <td>
                                            <button class="btn btn-danger btn-sm delete-btn" data-id="${type.id}">
                                                <i class="fa fa-trash"></i> Delete
                                            </button>
                                        </td>
                                    </tr>
                                </g:each>
                                <g:if test="${questionTypes.size() == 0}">
                                    <tr>
                                        <td colspan="5" class="text-center">No question types found.</td>
                                    </tr>
                                </g:if>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this question type?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    $(document).ready(function() {
        // Handle delete button click
        $('.delete-btn').click(function() {
            var id = $(this).data('id');
            $('#confirmDelete').data('id', id);
            $('#deleteModal').modal('show');
        });
        
        // Handle confirm delete button click
        $('#confirmDelete').click(function() {
            var id = $(this).data('id');
            
            $.ajax({
                url: '${createLink(controller: 'questionTypes', action: 'delete')}',
                type: 'POST',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while trying to delete the question type.');
                }
            });
            
            $('#deleteModal').modal('hide');
        });
    });
</script>

</body>
</html>
