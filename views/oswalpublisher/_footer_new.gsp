<g:render template="/wonderpublish/commonfooter_new"></g:render>
<script>
  var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
  var userLoggedIn=false;
</script>
<sec:ifLoggedIn>
  <script>
    userLoggedIn=true;
  </script>
</sec:ifLoggedIn>
<style>
  .copy-right-text-footer span{
    color: grey !important;
  }
</style>
<% if(params.tokenId==null && !isBookPage){%>

<footer class="footer-container py-0 border-top">
  <div class="main-footer bg-light">
    <div class="container top-footer mb-4">
      <div class="row align-items-center justify-content-between">
        <div class="col-12 col-lg-3 p-3">
          <img loading="lazy" src="${assetPath(src: 'oswalpublisher/oswalpub-logo.svg')}" alt="Oswal Publishers Logo" decoding="async" title="Oswal Publishers Logo" width="200" height="auto">
        </div>
        <div class="col-12 col-lg-6 p-3">
          <address>
            <strong>Address: </strong> 1/12 Sahitya Kunj, Mahatma Gandhi Rd, Near PNB Bank, Agra, Uttar Pradesh 282002.
            <a href="https://g.page/oswalpublishers?share" target="_blank"></a>
          </address>
        </div>
        <div class="col-12 col-lg-3 p-3">
          <p class="telephone mb-2"><strong>Phone: </strong> <a href="tel:************" target="_blank">(+91) 562-2527771</a></p>
          <p class="email-address"><strong>Mail: </strong> <a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
        </div>
      </div>
    </div>
  </div>
  <div class="container main-footer">
    <div class="middle-footer mb-4">
      <div class="row align-items-start justify-content-between">
        <div class="col-6 col-md-3">
          <h5>COMPANY</h5>
          <ul class="pl-4 list-style-circle">
            <li><a href="https://oswalpublishers.com/about-us/" target="_blank">About us</a></li>
            <li><a href="https://oswalpublishers.com/contact-us/" target="_blank">Contact us</a></li>
            <li><a href="https://oswalpublishers.com/blog/" target="_blank">Blog</a></li>
            <li><a href="https://oswalpublishers.com/media/" target="_blank">Media</a></li>
            <li><a href="https://oswalpublishers.com/faqs/" target="_blank">FAQs</a></li>
          </ul>
        </div>
        <div class="col-6 col-md-3">
          <h5>RESOURCES</h5>
          <ul class="pl-4 list-style-circle">
            <li><a href="https://oswal.io/" target="_blank">Oswal.io</a></li>
            <li><a href="https://oswalpublishers.com/syllabus/" target="_blank">Syllabus</a></li>
            <li><a href="https://oswalpublishers.com/ncert-solutions/" target="_blank">NCERT Solutions</a></li>
            <li><a href="https://oswalpublishers.com/notes/" target="_blank">Chapterwise Notes</a></li>
            <li><a href="https://oswalpublishers.com/download-solutions/" target="_blank">Download Solutions</a></li>
          </ul>
        </div>
        <div class="col-12 col-md-3">
          <h5>OUR POLICY</h5>
          <ul class="pl-4 list-style-circle pb-2">
            <li><a href="https://oswalpublishers.com/privacy-policy/" target="_blank">Privacy Policy</a></li>
            <li><a href="https://oswalpublishers.com/terms-and-conditions/" target="_blank">Terms & Conditions</a></li>
            <li><a href="https://oswalpublishers.com/refund_returns/" target="_blank">Refund & Returns Policy</a></li>
          </ul>
          <h5>STUDY MATERIAL</h5>
          <ul class="pl-4 list-style-circle pb-2 pb-md-0">
            <li><a href="https://oswalpublishers.com/cbse/" target="_blank">CBSE</a></li>
          </ul>
        </div>
        <div class="col-12 col-md-3 icon-with-links">
          <div class="social-links">
            <h5>FOLLOW US</h5>
            <a href="https://m.facebook.com/oswalpublishersindia/" target="_blank"><i class="fa fa-facebook"></i></a>
            <a href="https://twitter.com/oswalpublishers" target="_blank"><i class="fa fa-twitter"></i></a>
            <a href="https://www.instagram.com/oswalpublishers/?hl=en" target="_blank"><i class="fa fa-instagram"></i></a>
            <a href="https://www.youtube.com/channel/UCuNkDaAVD4MuagCXgEAy-1A" target="_blank"><i class="fa fa-youtube-play"></i></a>
            <a href="https://www.linkedin.com/company/oswal-publishers/?originalSubdomain=in" target="_blank"><i class="fa fa-linkedin"></i></a>
            <a href="https://api.whatsapp.com/send/?phone=************&text" target="_blank"><i class="fa fa-whatsapp"></i></a>
          </div>
          <hr>
          <div class="available-links">
            <h5>AVAILABLE ON</h5>
            <a href="https://www.amazon.in/stores/page/2C9F1469-0AB5-4361-8DDF-64A70B9E7A25?ingress=3" target="_blank" class="amazon">
              <img loading="lazy" src="${assetPath(src: 'oswalpublisher/amazon.svg')}" alt="Amazon" decoding="async" title="Amazon">
            </a>
            <a href="https://www.flipkart.com/all-categories/books/oswal-gurukul~contributor/pr?sid=search.flipkart.com%2Cbks" target="_blank" class="flipkart">
              <img loading="lazy" src="${assetPath(src: 'oswalpublisher/flipkart.svg')}" alt="Flipkart" decoding="async" title="Flipkart">
            </a>
            <a href="https://play.google.com/store/search?q=oswal%20gurukul&c=books" target="_blank" class="google-books">
              <img loading="lazy" src="${assetPath(src: 'oswalpublisher/google-books.svg')}" alt="Google Books" decoding="async" title="Google Books">
            </a>
            <a href="https://www.amazon.in/kindle-dbs/entity/author/B09JZHKW8K?_encoding=UTF8&node=1634951031&offset=0&pageSize=12&searchAlias=stripbooks&sort=author-sidecar-rank&page=1&langFilter=default#formatSelectorHeader" target="_blank" class="kindle">
              <img loading="lazy" src="${assetPath(src: 'oswalpublisher/kindle.svg')}" alt="Kindle" decoding="async" title="Kindle">
            </a>
            <a href="https://www.snapdeal.com/seller/1d6647" target="_blank" class="snapdeal">
              <img loading="lazy" src="${assetPath(src: 'oswalpublisher/snapdeal.svg')}" alt="Snapdeal" decoding="async" title="Snapdeal">
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="copyrights-wrapper bg-light">
    <div class="container d-flex align-items-center flex-column flex-lg-row justify-content-lg-between py-4 px-0">
      <div class="col-12 col-lg-6 text-center text-lg-left mb-3 mb-lg-0">
        <small>Copyright &copy; <span id="copyrightYear"></span> <a href="https://oswalpublishers.com" target="_blank"><strong>OSWAL PUBLISHERS</strong></a> Simplifying Exams</small><br>
        <small class="confident">Shop with confidence</small><br>
        <small>100% Secured Shopping</small>
      </div>
      <div class="col-12 col-lg-6 text-center text-lg-right">
        <img loading="lazy" src="${assetPath(src: 'oswalpublisher/payments.webp')}" alt="payments" decoding="async" class="payments" width="255" height="22">
      </div>
    </div>

    <h6 class="copy-right-text-footer text-center py-4 border-top mb-0"></h6>

  </div>
</footer>

<div class="mobile-footer-nav d-md-none" id="mobile-footer-nav"></div>
<%}%>

<script>
  //Dynamic Year in Footer
  var strDate = new Date();
  var shortYear = strDate.getFullYear();
  var nextYear = (new Date().getFullYear()+1);
  var twoDigitYear = nextYear.toString().substr(-2);
  $('.copy-right-text-footer').html('&copy; ' + shortYear +'-'+twoDigitYear+"<span> Powered by Wonderslate</span>");
  $('#copyrightYear').html(shortYear);

  var footerCategoryLinks="";
  for (var i = 0; i < activeCategories.length; i++) {
    footerCategoryLinks +="<li><a href='/oswalpublisher/store?level="+replaceAll(activeCategories[i].level.replace('&', '~'),' ','-')+"'>"+activeCategories[i].level+"</a></li>";
  }
  <% if(params.tokenId==null && !isBookPage){%>
  document.getElementById("footerCategoryLinks").innerHTML=footerCategoryLinks;
  <%}%>

  var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));

  var displayLevel = "";
  var categoryChanged = true;
  var categorySyllabusHtml="";
  for (var i = 0; i < activeCategoriesSyllabus.length; i++) {
    if(activeCategoriesSyllabus[i].level!=displayLevel){
      categoryChanged = true;
      displayLevel = activeCategoriesSyllabus[i].level;
      //closing tag logic
      if(displayLevel!=""){
        categorySyllabusHtml +=" </ul>\n" +
                "  </div>\n" +
                "  </div>";
      }
      categorySyllabusHtml +="<div class=\"col-sm-4\">\n" +
              "          <div class=\"manage-row-fluides-menu-big\">\n" +
              "          <ul class=\"manage-with-all-links-big-menus\">\n" +
              "          <h4>"+activeCategoriesSyllabus[i].level+"</h4>";
    }
    if("Medical Entrances"==activeCategoriesSyllabus[i].level&&"NEET"==activeCategoriesSyllabus[i].syllabus) continue;
    categorySyllabusHtml +="<li><a href='/oswalpublisher/store?level="+ replaceAll(activeCategoriesSyllabus[i].level.replace('&', '~'),' ','-')+"&syllabus="+replaceAll(activeCategoriesSyllabus[i].syllabus.replace('&', '~'),' ','-')+"&grade=null'>"+activeCategoriesSyllabus[i].syllabus+"</a></li>";

  }
  //last tag close
  categorySyllabusHtml +=" </ul>\n" +
          "  </div>\n" +
          "  </div>";

 document.getElementById("topMenuItems").innerHTML=categorySyllabusHtml;
</script>
<script>



  var mobileFooterNav =
          '        <div class="d-flex row justify-content-around w-100">\n' +
          '            <a href="/oswalpublisher/store" class="ebooks-menu d-flex align-items-center col">\n' +
          '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-library.svg')}">\n' +
          '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-ebooks-filled.svg')}">\n' +
          '                <p>Store</p>\n' +
          '            </a>\n' +
          '<sec:ifNotLoggedIn>\n' +
          '            <a href="javascript:loginOpen()" class="home-menu d-flex align-items-center col">\n' +
          '                <img class="mb-1 inactive" src="${assetPath(src: 'arihant/icon-arihant-library.svg')}">\n' +
          '                <img class="mb-1 active d-none" src="${assetPath(src: 'arihant/icon-arihant-library-filled.svg')}">\n' +
          '                <p>My Books</p>\n' +
          '            </a>\n' +
          '</sec:ifNotLoggedIn>'+
          '<sec:ifLoggedIn>'+
          '            <a href="/wsLibrary/myLibrary" class="home-menu d-flex align-items-center col">\n' +
          '                <img class="mb-1 inactive" src="${assetPath(src: 'arihant/icon-arihant-library.svg')}">\n' +
          '                <img class="mb-1 active d-none" src="${assetPath(src: 'arihant/icon-arihant-library-filled.svg')}">\n' +
          '                <p>My Books</p>\n' +
          '            </a>\n' +
          '</sec:ifLoggedIn>'+
          '        </div>';

  $(document).ready(function(){
    document.getElementById('mobile-footer-nav') ? document.getElementById('mobile-footer-nav').innerHTML = mobileFooterNav : null;

    var url = window.location.href;
    if(url.indexOf("/funlearn/quiz") != -1){
      //$('.mobile-footer-nav').addClass('hide-menus');
    } else if(url.indexOf("/wsLibrary/myLibrary") != -1){
      $('.mobile-footer-nav .home-menu').addClass('active-menu');
      $('.mobile-footer-nav .home-menu .active').removeClass('d-none');
      $('.mobile-footer-nav .home-menu .inactive').addClass('d-none');
      $('.mobile-footer-nav .ebooks-menu').addClass('common-footer-nav');
    } else if(url.indexOf("/oswalpublisher/store") != -1){
      $('.mobile-footer-nav .ebooks-menu').addClass('active-menu');
      $('.mobile-footer-nav .ebooks-menu .active').removeClass('d-none');
      $('.mobile-footer-nav .ebooks-menu .inactive').addClass('d-none');
      $('.mobile-footer-nav .home-menu').addClass('common-footer-nav');
    } else {
      $('.mobile-footer-nav a').addClass('common-footer-nav');
    }

  });

</script>
<asset:javascript src="landingpage/jquery.shorten.js" />
<asset:javascript src="whitelabel/popper.min.js"/>
<asset:javascript src="whitelabel/bootstrap.min.js"/>
<asset:javascript src="whitelabel/jquery-ui.min.js"/>
<asset:javascript src="arihant/waypoints.min.js"/>
<asset:javascript src="arihant/jquery.counterup.min.js"/>
<asset:javascript src="arihant/custom.js"/>
<asset:javascript src="landingpage/slick.js"/>
<asset:javascript src="wonderslate/material.min.js"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<g:render template="/books/pomodoro"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js"></script>
<script>
  window.addEventListener('keydown', function(event) {
    if (event.keyCode === 80 && (event.ctrlKey || event.metaKey) && !event.altKey && (!event.shiftKey || window.chrome || window.opera)) {
      event.preventDefault();
      if (event.stopImmediatePropagation) {
        event.stopImmediatePropagation();
      } else {
        event.stopPropagation();
      }
      return;
    }
  }, true);


  if($.browser.platform == "win") {
    $("html").addClass("windows");
  }

  if($.browser.name == "chrome") {
    $("html").addClass("chrome");
  } else if($.browser.name == "mozilla") {
    $("html").addClass("mozilla");
  } else if($.browser.name == "safari") {
    $("html").addClass("safari");
    if($(window).width() < 767){
      $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
        $(this).css('background','#eee');
        $('.btn-primary-modifier').css('background-color','#0b65b3 !important');
        $('.btn-success-modifier').css('background-color','#27AE60 !important');
        $('.btn-secondary-modifier').css('background-color','#8E8E8E !important');
        $('.btn-danger-modifier').css('background-color','#FF4B33 !important');
        $('.btn-warning-modifier').css('background-color','#FFD602 !important');
      });
      document.addEventListener('touchmove', function (event) {
        if (event.scale !== 1) { event.preventDefault(); }
      }, { passive: false });
    }
  } else {
    $("html").addClass("others");
    if($(window).width() < 767){
      $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
        $(this).css('background','#eee');
      });
      document.addEventListener('touchmove', function (event) {
        if (event.scale !== 1) { event.preventDefault(); }
      }, { passive: false });
    }
  }
</script>

<g:render template="/wsshop/cartScripts"></g:render>
<g:render template="/wsshop/searchScripts"></g:render>