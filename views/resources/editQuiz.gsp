<!DOCTYPE html>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/katex.min.css" integrity="sha384-Juol1FqnotbkyZUT5Z7gUPjQ9gzlwCENvUZTpQBAPxtusdwFLRy382PSDx5UUJ4/" crossorigin="anonymous">
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons%7CMaterial+Icons+Outlined%7CMaterial+Icons+Two+Tone%7CMaterial+Icons+Round%7CMaterial+Icons+Sharp" async>
<% if("books".equals(session["entryController"])){%>
<asset:stylesheet href="wonderslate/ws_webmcq.css"/>

<style>
.back_to_top {
    display: none;
}
</style>
<%}%>

%{--<asset:stylesheet href="landingpage/bootstrap.min.css"/>--}%
<asset:stylesheet href="landingpage/iconfont/icofont.css"/>
<asset:stylesheet href="landingpage/webquiz.css"/>
<asset:stylesheet href="wonderslate/quizcreator.css"/>
<asset:stylesheet href="fileuploadwithpreview.css"/>
<script>
    var defaultSiteName="${session['entryController']}";
    var resourceTitle = "${name}";
</script>
<style>
.modal-dialog {
    max-width: 1200px; /* Change this value as per your requirement */
    max-height: 900px;
}
.start-test .footer-test {
    height: 87px;
    box-shadow: none;
}
.quiz-related{
    display: none;
}
.backButton {
    cursor: pointer;
}
.mt-7{
    padding-top: 5rem;
}

#quizEditInPlace {
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    margin-top: 20px;
    margin-bottom: 40px;
    position: relative;
    z-index: 100;
}

#width_tmp_select{
    display: none;
}
.web-mcq .sub-header.section-header{
    height: auto !important;
}
.arihant .mt-fixed #resourceTitle{
    margin-top: 75px;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .grid .que-wrapper .que-no+.que-no a:first-child{
    display:flex !important;
}
.starttest-wrapper .header{
    z-index: 9 !important;
}
.web-mcq .result-menu{
    top:115px !important;
}

.error-highlight{
    border: 1px solid red !important;
}
.directions{
    line-height: 1.4 !important;
    margin-bottom: 1rem !important;
}
.marks{
    padding:10px 0;
}
.marks.positiveMrk{
    border-top: 1px solid #d4d4d4;
}
</style>
<style>
.sage-body .sage-banner {
    display: none;
}
.image-upload > input
{
    display: none;
}

.inlineEditor{
    min-height: 40px;
    border-bottom: 1px solid #828180;
}
.cke_textarea_inline
{
    height: 50px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.form-group .cke_textarea_inline
{
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

div:empty:before {
    content:attr(data-placeholder);
    color:gray
}
.red-border{
    border: 1px solid red;
}
.firstPage{
    background-color: #D3D3D3;
}
.smallText{
    font-size: 12px;
}

</style>

<%if("21".equals(""+session["siteId"])){%>
<style>
.bg-wsTheme{
    background:#F79420 !important;
}
</style>
<%}%>
<%if("1".equals(""+session["siteId"]) || "3".equals(""+session["siteId"]) || "37".equals(""+session["siteId"])){%>
<style type="text/css" media="print">
* { display: none; }
</style>
<%}%>
<% if("libwonder".equals(session["entryController"]) || "oswal".equals(session["entryController"])){%>
<style>

.libwonder footer {
    display: none;
}
.libwonder header.LibWonder  {
    position: fixed;
}
.libwonder #quizInformationSection,
.libwonder #learnInformationSection {
    margin-top: 97px;
}
.libwonder .web-mcq .sub-header {
    top: 100px;
    transition: all 0.2s linear;
}
.libwonder .web-mcq .result-menu {
    top: 5.8rem;
    transition: all 0.2s linear;
}
.libwonder .web-mcq .mt-fixed {
    margin-top: 140px !important;
    margin-bottom: 50px;
}
.libwonder .web-mcq .mt-fixed .que-side-menu {
    border-right: none;
}
.libwonder .web-mcq .mt-fixed form {
    border-left: 1px solid #ededed;
}
.libwonder .tab-wrappers {
    top: 200px;
}
.libwonder .result-menu > div {
    /*height: 86px;*/
}
.libwonder.custom-fix header.LibWonder {
    position: relative;
}
.libwonder.custom-fix #quizInformationSection,
.libwonder.custom-fix #learnInformationSection {
    margin-top: 0px;
}
.libwonder.custom-fix .web-mcq .sub-header {
    top: 0;
    z-index: 999;
}
.libwonder.custom-fix .web-mcq .web-position > div:first-child {
    top: 6rem;
}
.libwonder.custom-fix .web-mcq .mt-fixed {
    margin-top: 70px !important;
}
.libwonder.custom-fix .web-mcq .result-menu {
    top: 0;
    z-index: 999;
}
.libwonder .web-mcq .web-position > div:first-child {
    height: 70vh;
    padding-bottom: 0;
}
.libwonder.custom-fix .web-mcq .web-position > div:first-child {
    height: 80vh;
}
.libwonder .web-mcq .web-position #grid #collapseOne {
    padding-bottom: 0;
}
.libwonder .web-mcq .onclickScrollsList:last-child {
    padding-bottom: 0;
}
@media (max-width: 767px) {
    .libwonder #quizInformationSection,
    .libwonder #learnInformationSection {
        margin-top: 134px;
    }
    .libwonder .web-mcq .sub-header {
        top: 124px;
    }
    .libwonder .result-menu > div {
        height: 36px;
        justify-content: space-between !important;
    }
    .libwonder.custom-fix .result-menu > div {
        height: 50px;
    }
    .libwonder .result-menu > div i {
        display: block !important;
    }
    .libwonder .web-mcq .result-menu {
        top: 134px;
    }
}
@media (max-width: 768px) {
    .libwonder .web-mcq .mt-fixed {
        margin-top: 140px !important;
    }
    .libwonder.custom-fix .web-mcq .mt-fixed {
        margin-top: 50px !important;
    }
}
@media (max-width: 1024px) {
    .libwonder .web-mcq .web-position > div:first-child {
        height: 100%;
    }
}
@media (max-width: 480px) {
    .libwonder .web-mcq .web-position > div:first-child {
        width: 85%;
    }
}
</style>
<% } %>

<style>
    pre{
        overflow: hidden !important;
        white-space: pre-wrap !important;
        line-height: 28px !important;
        font-size: 14px !important;
    }
</style>
<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<form class="form-horizontal" enctype="multipart/form-data" role="form" name="addquiz" id="addquiz" action="/resourceCreator/addQuiz" method="post">
    <input type="hidden" name="resourceType">
    <input type="hidden" name="reAttemptValue">
    <input type="hidden" name="chapterId">
    <input type="hidden" name="quizId">
    <input type="hidden" name="mode" value="edit">
    <input type="hidden" name="resourceDtlId">
    <input type="hidden" name="objectiveMstId">
    <input type="hidden" name="finished">
    <input type="hidden" name="page">
    <input type="hidden" name="bookId" value="${params.bookId}">
    <input type="hidden" name="quickEdit" value="true">

<section class="start-test" id="learnInformationSection" style="display: none">
   <div class="starttest-wrapper">
       <div class="header">
            <% if("arihant".equals(session["entryController"])){%>
            <p>${name}</p>
            <%}else{%>
            <p>Test Name</p>
            <%}%>
        </div>
        <div class="container str-test" style="padding: 8px!important;">
            <div class="card col-lg-8">
                <div class="language  d-flex justify-content-around align-items-center">
                    <div><p>Default Language</p></div>
                    <div>:</div>
                    <div>
                        <select id="learnLanguageSelect">
                        </select>
                    </div>
                </div>
            </div>
            <div class="card col-lg-8" style="padding: 2px;">
                <div class="language  d-flex justify-content-around align-items-center">
                    <a href="javascript:backPressed();" class="btn btn-starts">Back</a>
                    <%if("1".equals(""+session["siteId"])){%><a href="javascript:practiseWithFlashCards()" class="btn btn-starts">Practice with Flashcards</a><%}%>
                    <a href="javascript:startLearn()" class="btn btn-starts">Study</a>
                </div>
            </div>

        </div>
        <div class="container">

        </div>
        <div class="footer-test">

        </div>
    </div>
</section>
<section class="web-mcq addwebmcq" style="display: none;" id="quizQuestionSection">
    <div class="mt-fixed">
    <div class="container">
        <div id="answer-block" style="display: none"></div>
    </div>
    </div>
</section>

<section class="container" id='quizEdit'>
        <div id="quizEditInPlace" style="display: none;">
            <div class="row mb-3">
                <div class="col-12">
                    <button type="button" onclick="javascript:formCancel()" class="btn btn-secondary"><i class="material-icons">arrow_back</i> Back to Questions</button>
                </div>
            </div>
                        <div class="row quiz7 mx-0 my-3 my-md-4 px-2 px-lg-5" >
                            <div class="col-sm-12">
                                <label id="directionLabel">DIRECTION</label>
                                <input type="hidden" name="directions">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="directions" class="inlineEditor"></div>
                                </div>

                            </div>
                        </div>
                        <div class="row quiz7 mx-0 my-3 my-md-4 px-2 px-lg-5" >
                            <div class="col-sm-12">
                                <label id="questionLabel">QUESTION</label>
                                <input type="hidden" name="question">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="question" class="inlineEditor"></div>
                                </div>

                            </div>
                        </div>
                        <div class="row quiz mb-4 px-2 px-lg-5">
                            <div class="col-1 col-md-1 text-left">
                                <br> <input type="checkbox" name="answer1" id="answer1" value="Yes">
                            </div>
                            <div class="col-11 col-md-5 pl-md-0">
                                <b>Option 1:</b>
                                <input type="hidden" name="option1">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="option1" class="inlineEditor"></div>
                                </div>

                            </div>

                            <div class="col-1 col-md-1 text-left">
                                <br><input type="checkbox" name="answer2" id="answer2" value="Yes">
                            </div>
                            <div class="col-11 col-md-5 pl-md-0">
                                <b>Option 2:</b>
                                <input type="hidden" name="option2">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="option2" class="inlineEditor"></div>
                                </div>
                            </div>

                        </div>

                        <div class="row quiz mb-4 px-2 px-lg-5">
                            <div class="col-1 col-md-1 text-left">
                                <br><input type="checkbox" name="answer3" id="answer3" value="Yes">
                            </div>
                            <div class="col-11 col-md-5 pl-md-0">
                                <b>Option 3:</b>
                                <input type="hidden" name="option3">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="option3" class="inlineEditor"></div>
                                </div>
                            </div>

                            <div class="col-1 col-md-1 text-left">
                                <br><input type="checkbox" name="answer4" id="answer4" value="Yes">
                            </div>
                            <div class="col-11 col-md-5 pl-md-0">
                                <b>Option 4:</b>
                                <input type="hidden" name="option4">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="option4" class="inlineEditor"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row quiz mb-4 px-2 px-lg-5">
                            <div class="col-1 col-md-1 text-left">
                                <br><input type="checkbox" name="answer5" id="answer5" value="Yes">
                            </div>
                            <div class="col-11 col-md-5 pl-md-0">
                                <b>Option 5:</b>
                                <input type="hidden" name="option5">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="option5" class="inlineEditor"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row quiz2 mb-4 px-2 px-lg-5 mx-0">
                            <div class="col-sm-12 ">
                                <b>Answer Explanation</b>
                                <input type="hidden" name="answerDescription">
                                <div class="cktext">
                                    <div  contenteditable="true"  id="answerDescription" class="inlineEditor"></div>
                                </div>

                            </div>
                        </div>
                        <div class="row quiz2 mb-4 px-2 px-lg-5 mx-0">
                            <div class="col-sm-12">
                                <div class="col-11 col-md-12 pl-md-0">
                                    <b>Positive Marks:</b>
                                    <input type="number" name="marks" id="marks" class="ml-2">
                                </div>

                                <div class="col-11 col-md-12 pl-md-0 mt-3">
                                    <b>Negative Marks:</b>
                                    <input type="number" name="negativeMarks" id="negativeMarks" class="ml-2">
                                </div>
                            </div>
                        </div>
                        <div class="row quiz2 mb-4 px-2 px-lg-5 mx-0">
                            <div class="col-sm-12 ">
                                <div class="col-11 col-md-5 pl-md-0 mb-1">
                                    <b>Answer Key Updated:</b>
                                </div>
                                <label>
                                    <input type="radio" name="answerKey" value="true" onclick="handleSelection()"  class="mr-1">Valid
                                </label>

                                <label>
                                    <input type="radio" name="answerKey" value="false" onclick="handleSelection()" class="mr-1">Not Valid
                                </label>
                                <input type="hidden" name="isValidAnswerKey">

                            </div>
                        </div>
                        <div class="row quiz6 ">
                            <div class="form-group">
                                <div class=" col-sm-12 text-center ">
                                   <button type="button" onclick="javascript:formCancel()" class="btn btn-secondary">Cancel</button>
                                    <button type="button" onclick="javascript:formSave('Done')" class="btn btn-primary">Save</button>
                                </div>
                            </div>
                        </div>
        </div>
    </section>
</form>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.18.0/ckeditor.js" ></script>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/katex.min.js" integrity="sha384-97gW6UIJxnlKemYavrqDHSX3SiygeOwIZhwyOKRfSaf0JWKRVj9hLASHgFTzT+0O" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.3/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>
<asset:javascript src="material-input.js"/>
<asset:javascript src="fileuploadwithpreview.js"/>
<asset:javascript src="quizcreator.js"/>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        renderMathInElement(document.body, {
            // customised options
            // • auto-render specific keys, e.g.:
            delimiters: [
                {left: '$$', right: '$$', display: true},
                {left: '$', right: '$', display: false},
                {left: '\\(', right: '\\)', display: false},
                {left: '\\[', right: '\\]', display: true}
            ],
            // • rendering keys, e.g.:
            throwOnError : false,
            ignoredTags: [],
        });
    });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX", "output/HTML-CSS"],
    tex2jax: {
      inlineMath: [ ['$','$'], ["\\(","\\)"] ],
      displayMath: [ ['$$','$$'], ["\\[","\\]"] ],
      processEscapes: true
    },
  });
</script>

<script>
    var appInApp="${session["appInApp"]}";
    var loggedInUser = false;

    <sec:ifLoggedIn>
    loggedInUser = true;
    </sec:ifLoggedIn>

    var myvalue = location.search.split('currentAffairs=')[1]?location.search.split('currentAffairs=')[1]:"";


</script>
<script>


</script>
<asset:javascript src="webmcq.js"/>
<asset:javascript src="timer.js"/>
<asset:javascript src="totalTimer.js"/>
<asset:javascript src="moment.min.js"/>

<script>

    var data1;
    var localLanguage;
    var resId='${params.resId}';
    var editQuestionIndex;
    quizEdit = true;
    let isValidAnswerKey = ""
    function getQuestionAnswers(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="funlearn" action="newQuizQA" params="'quizId='+${params.quizId}+'&resId='+${params.resId}" onSuccess = "initializeQuizDataWithAnswers(data);"/>
    }


    function initializeQuizDataWithAnswers(data){
        $('.loading-icon').addClass('hidden');
        $("#learnInformationSection").show();
        data1=data;
        startLearn();
    }

    function startLearn(){
        $('.loading-icon').removeClass('hidden');
        $("#learnInformationSection").hide();
        var useOtherLanguage=false;
        $("#quizQuestionSection").show();

        scoreAndShowAnswersMCQ(true,JSON.parse(data1.results),data1.isPassage,data1.passage,"web",useOtherLanguage);

        $('#answer-block').on('click', '.show-explanation-btn', function(e) {
            e.preventDefault();
            $(this).parents('.show-explanation').next('.correct-answer-explanation').slideToggle(100);
            $(this).html($(this).html() == 'Show Explanation' ? 'Hide Explanation' : 'Show Explanation');
        });
        $('.loading-icon').addClass('hidden');
    }

    getQuestionAnswers();

    function formCancel(){
        $('#quizEditInPlace').hide();
        $('#quizQuestionSection').show();
    }
    function handleSelection() {
        isValidAnswerKey = document.querySelector('input[name="answerKey"]:checked').value;
        console.log(isValidAnswerKey); // prints "true" or "false"
    }
    function formSave(){
        $('.loading-icon').removeClass('hidden');
        document.addquiz.mode.value="edit";
        document.addquiz.resourceType.value='Multiple Choice Questions';
        document.addquiz.chapterId.value=${params.chapterId};
        document.addquiz.quizId.value='${resourceDtl.resLink}';
        document.addquiz.resourceDtlId.value=${params.resId};
        document.addquiz.objectiveMstId.value=qa[editQuestionIndex].id;
        document.addquiz.page.value='notes';
        document.addquiz.directions.value=CKEDITOR.instances.directions.getData();
        document.addquiz.question.value=CKEDITOR.instances.question.getData();
        document.addquiz.option1.value=CKEDITOR.instances.option1.getData();
        document.addquiz.option2.value=CKEDITOR.instances.option2.getData();
        document.addquiz.option3.value=CKEDITOR.instances.option3.getData();
        document.addquiz.option4.value=CKEDITOR.instances.option4.getData();
        document.addquiz.option5.value=CKEDITOR.instances.option5.getData();
        document.addquiz.answerDescription.value=CKEDITOR.instances.answerDescription.getData();
        document.addquiz.isValidAnswerKey.value=isValidAnswerKey;
        qa[editQuestionIndex].directions=document.addquiz.directions.value;
        qa[editQuestionIndex].ps=document.addquiz.question.value;
        qa[editQuestionIndex].op1=document.addquiz.option1.value;
        qa[editQuestionIndex].op2=document.addquiz.option2.value;
        qa[editQuestionIndex].op3=document.addquiz.option3.value;
        qa[editQuestionIndex].op4=document.addquiz.option4.value;
        qa[editQuestionIndex].op5=document.addquiz.option5.value;
        if(document.getElementById("answer1").checked) qa[editQuestionIndex].ans1="Yes" ;
        else qa[editQuestionIndex].ans1=""  ;
        if(document.getElementById("answer2").checked) qa[editQuestionIndex].ans2="Yes";
        else qa[editQuestionIndex].ans2=""  ;
        if(document.getElementById("answer3").checked) qa[editQuestionIndex].ans3="Yes";
        else qa[editQuestionIndex].ans3=""  ;
        if(document.getElementById("answer4").checked) qa[editQuestionIndex].ans4="Yes";
        else qa[editQuestionIndex].ans4=""  ;
        if(document.getElementById("answer5").checked) qa[editQuestionIndex].ans5="Yes";
        else qa[editQuestionIndex].ans5=""  ;
        qa[editQuestionIndex].answerDescription=document.addquiz.answerDescription.value;
        qa[editQuestionIndex].negativeMarks=document.addquiz.negativeMarks.value;
        qa[editQuestionIndex].marks=document.addquiz.marks.value;
        var oData = new FormData(document.forms.namedItem("addquiz"));
        var url = "${createLink(controller:'resourceCreator',action:'addQuiz')}";

        $.ajax({
            url: url,
            type: 'POST',
            data: oData,
            processData: false,  // tell jQuery not to process the data
            contentType: false,
            success: function (req) {

                $('#quizEditInPlace').hide();
                $('#quizQuestionSection').show();
                if( document.getElementById("directions-"+(editQuestionIndex+1))){
                    document.getElementById("directions-"+(editQuestionIndex+1)).innerHTML=qa[editQuestionIndex].directions;
                }
                document.getElementById("question-"+(editQuestionIndex+1)).innerHTML=qa[editQuestionIndex].ps;
                document.getElementById("answerStrings-"+(editQuestionIndex+1)+"-1").innerHTML=qa[editQuestionIndex].op1;
                document.getElementById("answerStrings-"+(editQuestionIndex+1)+"-2").innerHTML=qa[editQuestionIndex].op2;
                document.getElementById("answerStrings-"+(editQuestionIndex+1)+"-3").innerHTML=qa[editQuestionIndex].op3;
                document.getElementById("answerStrings-"+(editQuestionIndex+1)+"-4").innerHTML=qa[editQuestionIndex].op4;
                if(elementExists("answerStrings-"+(editQuestionIndex+1)+"-5")) {
                    document.getElementById("answerStrings-" + (editQuestionIndex + 1) + "-5").innerHTML = qa[editQuestionIndex].op5;
                }
                if(document.getElementById("demo-"+(editQuestionIndex+1))){
                    document.getElementById("demo-"+(editQuestionIndex+1)).innerHTML=qa[editQuestionIndex].answerDescription;
                }

                if(isValidAnswerKey=="true"){
                    document.getElementById("questionIndex_"+(editQuestionIndex)).classList.remove('error-highlight')
                }

                document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-1").classList.remove("ans-correct");
                document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-1").classList.add("ans-neutral");
                document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-2").classList.remove("ans-correct");
                document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-2").classList.add("ans-neutral");
                document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-3").classList.remove("ans-correct");
                document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-3").classList.add("ans-neutral");
                document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-4").classList.remove("ans-correct");
                document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-4").classList.add("ans-neutral");
                if(elementExists("answerStrings-"+(editQuestionIndex+1)+"-5")) {
                    document.getElementById("answerDiv-" + (editQuestionIndex + 1) + "-5").classList.remove("ans-correct");
                    document.getElementById("answerDiv-" + (editQuestionIndex + 1) + "-5").classList.add("ans-neutral");
                }
                if(document.getElementById("answer1").checked) {
                    document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-1").classList.remove("ans-neutral");
                    document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-1").classList.add("ans-correct");
                    document.getElementById("answerSpan-"+(editQuestionIndex+1)+"-1").classList.add("correct-answer");

                }
                if(document.getElementById("answer2").checked) {
                    document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-2").classList.remove("ans-neutral");
                    document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-2").classList.add("ans-correct");
                    document.getElementById("answerSpan-"+(editQuestionIndex+1)+"-2").classList.add("correct-answer");
                }
                if(document.getElementById("answer3").checked) {
                    document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-3").classList.remove("ans-neutral");
                    document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-3").classList.add("ans-correct");
                    document.getElementById("answerSpan-"+(editQuestionIndex+1)+"-3").classList.add("correct-answer");
                }
                if(document.getElementById("answer4").checked) {
                    document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-4").classList.remove("ans-neutral");
                    document.getElementById("answerDiv-"+(editQuestionIndex+1)+"-4").classList.add("ans-correct");
                    document.getElementById("answerSpan-"+(editQuestionIndex+1)+"-4").classList.add("correct-answer");
                }
                if(elementExists("answerStrings-"+(editQuestionIndex+1)+"-5")) {
                    if (document.getElementById("answer5").checked) {
                        document.getElementById("answerDiv-" + (editQuestionIndex + 1) + "-5").classList.remove("ans-neutral");
                        document.getElementById("answerDiv-" + (editQuestionIndex + 1) + "-5").classList.add("ans-correct");
                        document.getElementById("answerSpan-" + (editQuestionIndex + 1) + "-5").classList.add("correct-answer");
                    }
                }

                if(document.getElementById("positive-mark_" + (editQuestionIndex))){
                    document.getElementById("positive-mark_" + (editQuestionIndex)).innerHTML = "Marks: "+qa[editQuestionIndex].marks;
                }

                if(document.getElementById("negative-mark_" + (editQuestionIndex))){
                    document.getElementById("negative-mark_" + (editQuestionIndex)).innerHTML = "Negative Marks: "+qa[editQuestionIndex].negativeMarks;
                }
                renderMathInElement(document.getElementById("answer-block"), {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true },
                        { left: "$$", right: "$$", display: true },
                        { left: "$", right: "$", display: false },
                    ],
                    ignoredTags: []
                });
                $('.loading-icon').addClass('hidden');
            }
        });
    }
    function editQuiz(index){
        editQuestionIndex = index;
        // Show the editor in place instead of in a modal
        $('#quizEditInPlace').show();
        $('#quizQuestionSection').hide();
        document.getElementById("questionLabel").innerText="Question "+(index+1)
        //reset first
        CKEDITOR.instances.directions.setData('');
        CKEDITOR.instances.question.setData('');
        CKEDITOR.instances.option1.setData('');
        CKEDITOR.instances.option2.setData('');
        CKEDITOR.instances.option3.setData('');
        CKEDITOR.instances.option4.setData('');
        CKEDITOR.instances.option5.setData('');
        CKEDITOR.instances.answerDescription.setData('');
        document.getElementById("answer1").checked = false;
        document.getElementById("answer2").checked = false;
        document.getElementById("answer3").checked = false;
        document.getElementById("answer4").checked = false;
        document.getElementById("answer5").checked = false;

        //set now
        CKEDITOR.instances.directions.setData(qa[index].directions);
        CKEDITOR.instances.question.setData(qa[index].ps);
        CKEDITOR.instances.option1.setData(qa[index].op1);
        CKEDITOR.instances.option2.setData(qa[index].op2);
        CKEDITOR.instances.option3.setData(qa[index].op3);
        CKEDITOR.instances.option4.setData(qa[index].op4);
        CKEDITOR.instances.option5.setData(qa[index].op5);
        CKEDITOR.instances.answerDescription.setData(qa[index].answerDescription);
        if('Yes' == qa[index].ans1) document.getElementById("answer1").checked = true;
        if('Yes' == qa[index].ans2) document.getElementById("answer2").checked = true;
        if('Yes' == qa[index].ans3) document.getElementById("answer3").checked = true;
        if('Yes' == qa[index].ans4) document.getElementById("answer4").checked = true;
        if('Yes' == qa[index].ans5) document.getElementById("answer5").checked = true;

        if(qa[index].marks) {
            document.getElementById("marks").value = qa[index].marks;
        }else{
            document.getElementById("marks").value = "";
        }
        if(qa[index].negativeMarks) {
            document.getElementById("negativeMarks").value = qa[index].negativeMarks;
        }else{
            document.getElementById("negativeMarks").value = "";
        }

        const radios = document.querySelectorAll('input[name="answerKey"]');
        radios.forEach(radio => radio.checked = false);
    }

    var mode = "${params.mode}";
    var resourceType = "${params.resourceType}";
    var chapterId = "${params.chapterId}";
    var bookId = "${params.bookId}";
    var page = "${params.page}";
    CKEDITOR.disableAutoInline = true;
    CKEDITOR.inline( 'directions',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    });
    CKEDITOR.inline( 'question',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    });
    CKEDITOR.inline( 'option1',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    });

    CKEDITOR.inline( 'option2',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    });

    CKEDITOR.inline( 'option3',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    });

    CKEDITOR.inline( 'option4',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    });

    CKEDITOR.inline( 'option5',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
    });


    CKEDITOR.inline( 'answerDescription',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page+'&htmlId='+resId,
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
        height: 250,
    });
</script>


