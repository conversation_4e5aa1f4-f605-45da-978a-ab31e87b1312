<style>

</style>
<div class="modal fade ws-modal" id="startMatch" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-md">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
            <button type="button" class="close" onclick="backToStart()">&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body text-center">
               <h3 class="hero-title">Match your Cards!</h3>
            <img src="${assetPath(src: 'wonderslate/flashcarddemo.gif')}" alt="Drag Drop" class="mt-1 ws-dragdrop">
                <p class="mt-2 ws-text">Drag corresponding items onto each other to match them.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" onclick="startGame();" class="btn btn-flashcard">Start game</button>
            </div>

        </div>
    </div>
</div>
<div class="modal fade ws-modal" id="finishMatch" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-md">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body text-center">
              <div id="firstPlace" style="display: none;">
                <p class="hero-title">You got 1st place and have unlocked the Match badge!</p>
                <p class="mt-3 ws-text">Your score of <span class="userScore">25.3</span> seconds puts you in 1st place.</p>
              </div>
                <div id="secondPlace" style="display: none;">
                    <h2 class="hero-title"><span class="userScore">27.9</span> seconds — you can do it!</h2>
                    <p class="mt-3 ws-text">Just one more attempt to see if you can beat your record of <span class="bestScore"></span> seconds!.</p>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
            <button type="button" class="btn btn-flashcard" onclick="javascript:backToMatch();">Done</button>
                <button type="button" onclick="playAgain();" class="btn btn-flashcard">Play again</button>
            </div>

        </div>
    </div>
</div>
<div class="container" id="matchCards">
<div id="play-match">

</div>
</div>
<script>

    var dragElement,dropElement,interval;
   var htmlStr='';
    function playMatch(data) {

        var matchCards=data.keyValues;

        shuffleArray(matchCards);

        htmlStr= "<div class='d-flex mobile-stocker align-items-center justify-content-between mt-lg-4' style='max-width: 750px;\n" +
            "    margin: 0 auto;'> " +
                "<div class='d-flex align-items-center'>";
        if(previousChapterId==-1) {
            htmlStr += "<i class='material-icons backfromcard d-none d-lg-block' onclick='backToMatch();'>keyboard_backspace</i>";
        }
        else{
            htmlStr +="<i class='material-icons backfromcard d-none d-lg-block' onclick='javascript:closeResourceScreen();'>keyboard_backspace</i>";
        }
        htmlStr += "<h4 class='hero-title ml-2'><span class='d-flex'> " + resname + "</span></h4>" +
                "</div>"+
                "<div class='d-flex justify-content-between time-header mr-2'>" +
                    "<div class='text-center'>" +
                        "<p class='best'>Best Time</p>" +
                        "<span id='bestTime'></span>" +
                    "</div>" +

                    "<div class='text-center ml-2 ml-lg-4'>" +
                        "<p>Your Time</p>" +
                        "<span id='timer'>0.0</span>" +
                    "</div>" +

                "</div>"+

            // "<button type='button' class='btn btn-flashcard d-lg-none reset'  onclick='resetMatch()' id='reset' disabled>Reset</button>"+
            "</div>" +
            "<div class='row match-cards pl-3 align-items-center justify-content-center justify-content-lg-between' style='max-width:700px;margin: 0 auto;'>" +
            "<p class='flDescription'>Match the following cards</p>" +
                // "<button type='button' class='btn btn-flashcard d-none d-lg-block reset'  onclick='resetMatch()' id='reset' disabled>Reset</button>"+
            "</div>";
        htmlStr+= " <div class=\"row d-flex flex-wrap playmatch\">";
        var listLength = matchCards.length;
        if(listLength > 6)  listLength = 6;
        for(var i=0;i<listLength;i++){
            htmlStr +="<div class=\"col-4 col-md-3 randomMix d-flex justify-content-center card-"+(matchCards[i].id)+"\">\n" +
                "            <div id=\"card-"+(matchCards[i].id)+"\" class='card'>\n" +
                "                <div class=\"card-body d-flex align-items-center justify-content-center\">\n" +
                "                    <p class=\"card-text text-radial-gradient d-block\">"+matchCards[i].definition+"</p>\n" +
                "                </div>\n" +
                "            </div>\n" ;
            if(matchCards[i].definition.indexOf('</table>') > -1 || matchCards[i].definition.match(/<img/)) {
                htmlStr +="<button type='button' class='btn btn-primary expandDragDrop' onclick='expandModal(" + i + " ,true)'>Expand</button>";
            }
            htmlStr += "        </div>";
            htmlStr +="<div class=\"col-4 col-md-3 randomMix d-flex justify-content-center card-"+(matchCards[i].id)+"\">\n" +
                "            <div id=\"card-"+(matchCards[i].id)+"\" class='card'>\n" +
                "                <div class=\"card-body d-flex align-items-center justify-content-center\">\n" +
                "                    <p class=\"card-text text-radial-gradient d-block\">"+matchCards[i].term+"</p>\n" +
                "                </div>\n" +
                "            </div>\n" ;
            if(matchCards[i].term.indexOf('</table>') > -1 || matchCards[i].term.match(/<img/)) {
                htmlStr += "<button type='button' class='btn btn-primary expandDragDrop' onclick='expandModal(" + i + ",false)'>Expand</button>";
            }
            htmlStr +="        </div>";

            // if($('.card-'+matchCards[i].id).find('.card-body table')){
            //     alert('dd');
            //     $('.card-'+matchCards[i].id).find('.expandDragDrop').hide();
            // }
            // else{
            //     $('.card-'+matchCards[i].id).find('.expandDragDrop').hide();
            // }
        }

        document.getElementById('play-match').innerHTML=htmlStr;
        if($('.randomMix .card-body').find('table').length || $('.randomMix .card-body').find('img').length){
            console.log('yesav');
            $('.randomMix .card-body').removeClass('d-flex');
            $('.expandDragDrop').show();
        }
        else{
            $('.randomMix .card-body').addClass('d-flex');
            $('.expandDragDrop').hide();
        }
        var cards = $(".randomMix");
        for(var i = 0; i < cards.length; i++){
            var target = Math.floor(Math.random() * cards.length -1) + 1;
            var target2 = Math.floor(Math.random() * cards.length -1) +1;
            cards.eq(target).before(cards.eq(target2));
        }
    }

function resetMatch() {
    $('.card').draggable('enable');
    $('.randomMix').droppable('enable');
    $('.card').removeClass('matched');
    $('.randomMix').removeClass('allmatched').addClass('d-flex').show();

    $('.reset').prop('disabled', true);
    $('#firstPlace,#secondPlace').hide();
    clearInterval();
}

    function backToMatch() {
        if(previousChapterId ==-1) {
            clearInterval(interval);
            resetMatch();
            $('#timer').html('0.0');
            document.querySelector('#htmlreadingcontent').style.display = 'block';
            document.querySelector('#allCards').style.display = 'block';
            document.querySelector('#play-match').style.display = 'none';
            $('#finishMatch').modal('hide');
        }
        else{
            location.reload();
        }

    }


    function startGame() {
        $('#startMatch').modal('hide');
        $('.card').draggable({
            helper:'clone',
            cursor:'move',
            start:function (event,ui) {
                dragElement=event.target.id;
                $('.randomMix,.card').removeClass('card-anim');
                $(ui.helper).addClass("ui-draggable-helper");
                $(event.target).parent().droppable('disable');
            },
            stop:function (event) {
                $(event.target).parent().droppable('enable');
            }

        });
        $('.randomMix').droppable({
            accept: ".card",
            drop: function( event, ui ) {
                dropElement=event.target.childNodes[1].id;
                if(dragElement===dropElement){
                    $(ui.draggable).addClass('matched');
                    $(ui.draggable).parent().addClass('allmatched');
                    $(ui.draggable).parent().fadeOut().removeClass('d-flex');
                    $(this).children().addClass('matched');
                    $(this).addClass('allmatched');
                    $(this).fadeOut().removeClass('d-flex');
                    $(ui.draggable).draggable('disable');



                    if(previousChapterId==-1){
                        var cardLength=$('.randomMix').length;
                    }
                    else{
                        var cardLength=$('.randomMix').length/2;
                    }
                    if(cardLength === $('.randomMix.allmatched').length){
                           clearInterval(interval);
                            $('#finishMatch').modal('show');
                            var userTime= Number($('#timer').text());
                            var bestTime=Number($('#bestTime').text());
                                if((userTime<bestTime) || (bestTime =='') || (bestTime ==null)){
                                    saveFastestTime(cardId,userTime);
                                    saveFlashcardTime(cardId,userTime);
                                    $('#firstPlace').show();
                                    $('.userScore').html(userTime);
                                }
                                else{
                                    $('#secondPlace').show();
                                    $('.bestScore').html(bestTime);
                                    $('.userScore').html(userTime);
                                }

                    }
                }
                else{
                    $(this).addClass('card-anim');
                    $(ui.draggable).addClass('card-anim');
                    <%if("android".equals(session["appType"])){%>
                    JSInterface.callVibrate();
                    <%}else if("ios".equals(session["appType"])){%>
                    webkit.messageHandlers.callVibrate.postMessage('');
                    <%}%>
                }
            }
        });
        var startTime = Date.now();

         interval = setInterval(function() {
            var elapsedTime = Date.now() - startTime;
            document.getElementById("timer").innerHTML = (elapsedTime / 1000).toFixed(1);
        }, 100);
    }

function playAgain() {
   $('#finishMatch').modal('hide');
    resetMatch();
    startGame();
}

var fastestTime;
function getBestTime(resId) {
    <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='getfastestTime(data);' params="'resId='+resId"></g:remoteFunction>
}
function getfastestTime(data){
       fastestTime=data.fastestTime;
       if(fastestTime !=null) {
           $('#bestTime,.best').show();
           $('#bestTime').html(fastestTime).show();
       }
       else{
                     $('#bestTime,.best').hide();
                     $('#bestTime').html('');

       }
}

function saveFlashcardTime(resId,timeTaken) {
  <g:remoteFunction controller="log" action="updateFlashCardTime" onSuccess='savedTime();' params="'resId='+resId+'&timeTaken='+timeTaken"></g:remoteFunction>
}

function savedTime() {

}

function saveFastestTime(resId,timeTaken) {
    <g:remoteFunction controller="log" action="updateFastestFlashCardTime" onSuccess='savedTime();' params="'resId='+resId+'&timeTaken='+timeTaken"></g:remoteFunction>
}

%{--function getBestTimeIfNull(resId) {--}%
%{--    <g:remoteFunction controller="funlearn" action="getFlashCards" onSuccess='getTime(data);' params="'resId='+resId"></g:remoteFunction>--}%
%{--}--}%

%{--function getTime(data) {--}%
%{--     var getCommonTime=data.fastestTime;--}%
%{--     if(getCommonTime!=null) {--}%
%{--         $('#bestTime,.best').show();--}%
%{--         $('#bestTime').html(getCommonTime).show();--}%
%{--     }--}%
%{--     else {--}%
%{--          $('#bestTime,.best').hide();--}%
%{--          $('#bestTime').html('');--}%
%{--     }--}%
%{--}--}%

    function backToSliderCard(resId,resName){
     reviseNow(resId,resName);
        clearInterval(interval);
        resetMatch();
        $('#timer').html('0.0');
    }

   function backToStart() {
       if(previousChapterId==-1) {
           document.querySelector('#htmlreadingcontent').style.display = 'block';
           document.querySelector('#allCards').style.display = 'block';
           document.querySelector('#play-match').style.display = 'none';
           $('#startMatch').modal('hide');
       }
       else{
           $('#startMatch').modal('hide');
           location.reload();
       }
   }

    function shuffleArray(array) {
        for (var i = array.length - 1; i > 0; i--) {
            var j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

</script>



