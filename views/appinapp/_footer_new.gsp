%{--<g:render template="/books/wsabout"></g:render>--}%
<g:render template="/wonderpublish/wsFooter"></g:render>
<asset:stylesheet href="wonderslate/footer.css" async="true"/>
<asset:javascript src="moment.min.js"/>



<asset:javascript src="landingpage/popper.min.js"/>
<asset:javascript src="landingpage/bootstrap.min.js"/>
%{--<script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0-rc.2/js/materialize.min.js"></script>--}%

<asset:javascript src="landingpage/jquery.shorten.js" />
<asset:javascript src="wonderslate/material.min.js"/>
<asset:javascript src="landingpage/slick.js"/>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js"></script>

<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script>
    $('#search-book-header').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }

                    }));
                    searchDataValues = data.searchValues;
                    searchStringValues = data.searchList;
                }
            })
        },
        afterSelect: function(){
            submitSearchHeader();
        }
    });
    $(document).ready(function(){
        $('#search-btn-header').attr('disabled',true);
        $('#search-book-header').keyup(function(){
            if($(this).val().length !=0)
                $('#search-btn-header').attr('disabled', false);
            else
                $('#search-btn-header').attr('disabled',true);
        })
    });
    $(document).on("keypress", "#search-book-header", function(e) {
        if( e.which === 32 && this.value === '' ) {
            return false;
        } else if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitSearchHeader();
            }
        }
    });

    function submitSearchHeader(){
        $(window).off('scroll');
        var searchString = encodeURIComponent(document.getElementById("search-book-header").value);
        if(searchString.length == 0) {

        } else {
            $('.app_in_app .global-search ul.typeahead').css('display','none');
            $('.loading-icon').removeClass('hidden');
            if (typeof submitSearchTop === 'function') {
                submitSearchTop();
            }else{
                window.location.href="/<%= session["entryController"] %>/store?searchString="+searchString;
            }
        }
    }
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";

    $(document).ready(function(){
        var url = window.location.href;
        if(url.indexOf("/appinapp/store") != -1 || url.indexOf("/ebook-details") != -1){
            $('#ebooksAppInApp').addClass('active');
        } else if(url.indexOf("/wsLibrary/myLibrary") != -1){
            $('#myLibAppInApp').addClass('active');
        }

        $('#ebooksAppInApp').on('click',function () {
            $(".loading-icon").removeClass('hidden');
            $(this).addClass('active');
            $('#myLibAppInApp').removeClass('active');
        });
        $('#myLibAppInApp').on('click',function () {
            $(".loading-icon").removeClass('hidden');
            $(this).addClass('active');
            $('#ebooksAppInApp').removeClass('active');
        });
    });

    $(".loading-icon").html('<svg class="spinner" viewBox="0 0 50 50"><circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="4"></circle></svg>');
</script>
