<!DOCTYPE html>
<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>


<asset:stylesheet href="imageoverlay.css"/>
<asset:stylesheet href="bootstrap-select.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>

<style>
.mcq-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: move;
}

.mcq-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    border-color: #4a6cf7;
}

.mcq-number {
    background: #4a6cf7;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin-right: 12px;
    flex-shrink: 0;
}

.mcq-question {
    flex: 1;
    font-size: 15px;
    line-height: 1.5;
    color: #333;
    white-space: break-spaces;
    overflow: scroll;
}



.ui-sortable-helper {
    transform: rotate(2deg);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.ui-state-highlight {
    height: 60px;
    background: #f8f9fa;
    border: 2px dashed #4a6cf7;
    border-radius: 8px;
    margin-bottom: 12px;
}
.update-btn{
    width:150px;
}
.hd_wrap{
    background: #fff;
    padding: 14px;
    position: sticky;
    top: 120px;
    z-index: 99;
}
</style>
<link href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css" rel="stylesheet">

    <div class="hd_wrap d-flex my-2 flex-column align-items-center justify-content-center mb-4 mt-4">
        <h3>${resourceDtl.resourceName}</h3>
        <button class="btn btn-sm btn-primary update-btn" onclick="updateSortOrder();">Update Sort Order</button>
    </div>
    <div class="container lightblue_bg border rounded my-5 py-2">

        <div class="list-chapters" id="chaptersList">
            <!-- MCQs will be rendered here by JavaScript -->
        </div>
</div>

<div class="loading-icon hidden">
   <div class="loader-wrapper">
       <div class="loader">Loading</div>
   </div>
</div>

<script src="/assets/katex.min.js"></script>
<script src="/assets/auto-render.min.js"></script>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<asset:javascript src="bootstrap-select.js"/>
<asset:javascript src="generic.js"/>
<asset:javascript src="topic.js"/>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<script>
    // MCQ data from server
    var mcqsData = [
        <g:each in="${mcqs}" var="mcq" status="i">
            {
                id: ${mcq.id},
                question: "${mcq.question?.encodeAsJavaScript()}"
            }<g:if test="${i < mcqs.size() - 1}">,</g:if>
        </g:each>
    ];

    // Function to strip HTML tags from text
    function stripHtmlTags(html) {
        var tmp = document.createElement("DIV");
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || "";
    }

    // Function to render MCQs
    function renderMCQs() {
        var container = $('#chaptersList');
        container.empty();

        mcqsData.forEach(function(mcq, index) {
            var questionText = stripHtmlTags(mcq.question);

            var mcqCard = $('<div>', {
                id: mcq.id,
                class: 'mcq-card d-flex align-items-start'
            });

            var numberDiv = $('<div>', {
                class: 'mcq-number',
                text: index + 1
            });

            var questionDiv = $('<div>', {
                class: 'mcq-question',
                text: questionText
            });

            mcqCard.append(numberDiv, questionDiv);
            container.append(mcqCard);
        });
    }

    function updateSortOrder(){
        var itemOrder = $('#chaptersList').sortable("toArray");
        var mcqSortIds="";
        for (var i = 0; i < itemOrder.length; i++) {
            mcqSortIds += itemOrder[i]+",";
        }
        mcqSortIds=mcqSortIds.substring(0,(mcqSortIds.length-1));
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="resourceCreator" action="updateMCQsSortId" params="'mcqSortIds='+mcqSortIds+'&resId=${params.resId}&bookId=${params.bookId}'" onSuccess="uploadMCQsSort(data)"></g:remoteFunction>
    }

    function uploadMCQsSort(data){
        if(data.status=="success") {
            $('.loading-icon').addClass('hidden');
            // Update the order numbers after successful sort
            $('#chaptersList .mcq-number').each(function(index) {
                $(this).text(index + 1);
            });
        }
    }

    $(document).ready(function(){
        // Render MCQs on page load
        renderMCQs();

        // Initialize sortable
        $("#chaptersList").sortable({
            placeholder: 'ui-state-highlight',
            tolerance: 'pointer',
            cursor: 'move',
            opacity: 0.8,
            start: function(event, ui) {
                ui.placeholder.height(ui.item.outerHeight());
            }
        });
        $("#chaptersList").disableSelection();
        renderMathInElement(document.body);
    });
</script>
