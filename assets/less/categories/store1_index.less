.store1_topbanner {
  .breadcrumb_area {
    background-color: #e9ecef;
  }
  .breadcrumb {
    @media @iPhone {
      padding: 0.5rem 1rem;
    }
    li {
      font-size: 14px;
      @media @iPhone {
        font-size: 13px;
      }
    }
    li a {
      color: #333333;
      &:hover {
        color: #000000;
      }
      @media @iPhone {
        font-size: 13px;
      }
    }
    li.active {
      font-weight: 500;
      color: @ws-lightOrange;
    }
    .breadcrumb-item+.breadcrumb-item::before {
      //content: "›";
      //color: #333333;
      content: "";
      background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 14l6-6-6-6'/></svg>");
      background-repeat: no-repeat;
      background-size: 0.5rem;
      height: 8px;
      width: 15px;
    }
  }
  .bg-banner {
    background-color: @ws-darkBlue;
    h1 {
      font-size: 50px;
      color: #ffffff;
      @media @iPhone {
        font-size: 24px;
      }
      @media @iPhone6-landscape {
        font-size: 36px;
      }
      @media @iPhone7-landscape {
        font-size: 36px;
      }
    }
    h3 {
      color: #ffffff;
      @media @iPhone {
        font-size: 18px;
      }
      @media @iPhone6-landscape {
        font-size: 20px;
      }
      @media @iPhone7-landscape {
        font-size: 20px;
      }
    }
  }
  .publisher_logo {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center bottom;
    height: 250px;
    @media @iPhone {
      height: 200px;
    }
    div {
      @media @iPhone {
        width: 100% !important;
      }
      @media @iPad-portrait {
        width: 100% !important;
      }
    }
    h1 {
      color: #E76619;
      font-family: "Times New Roman";
      @media @iPhone {
        font-size: 30px;
      }
    }
    h2 {
      color: #00FE00;
      font-family: "Times New Roman";
      @media @iPhone {
        font-size: 24px;
      }
    }
  }
  .banner_publisher {
    @media @iPhone {
      padding: 0;
    }
    @media @iPhone5-portrait {
      padding: 0;
    }
    @media @iPhone6-portrait {
      padding: 0;
    }
    @media @iPhone7-portrait {
      padding: 0;
    }
  }
}

.store1_index {
  min-height: auto;
  h3 {
    @media @iPhone {
      font-size: 22px;
    }
  }
  #subjectFilter {
    margin-top: 30px;
    margin-bottom: -63px;
    position: relative;
    z-index: 100;
    .dropdown {
      width: 200px;
      .btn {
        overflow: hidden;
        text-overflow: ellipsis;
      }
      @media @iPhone {
        width: 115px;
        .btn-primary {
          font-size: 12px;
          font-weight: normal;
        }
      }
    }
    .dropdown-menu.show {
        right: 0px;
        left: auto !important;
        transform: translate(0px) !important;
        margin-top: 30px;
        min-height: auto;
        max-height: 230px;
        overflow-y: auto;
    }
    .dropdown-menu li a {
      &:hover {
        background-color: @ws-lightOrange;
      }
    }
  }

  .topSchoolBooks {
    //padding: 15px;
    //padding-bottom: 15px;
    width: 152px;
    .image-wrapper {
      //width: 100%;
      //height: auto;
      //min-height: 175px;
      h3 {
        //bottom: 50px;
      }
      img {
        //width: 100%;
        //height: 175px;
      }
    }
    .content-wrapper {
      //margin-top: 15px;
      height: auto;
      //min-height: 100px;
      h3 {
        width: auto;height: auto;
        //min-height: 100px;
        //font-size: 14px;
      }
    }
  }
  #content-data-books-ebooks .col-lg-2 {
    @media only screen and (min-width: 992px) {
      flex: 0 0 20%;
      max-width: 20%;
    }
  }
  #content-data-books-printbooks .col-lg-2 {
    @media only screen and (min-width: 992px) {
      flex: 0 0 20%;
      max-width: 20%;
    }
  }
}

.store1_quiz {
  position: relative;
  z-index: 10;
  .quiz-wrapper {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
  .headerSlider h2 {
    @media @iPhone {
      font-size: 24px;
    }
  }
}

.store1_videos {
  position: relative;
  min-height: auto;
  @media @iPhone {
    min-height: auto;
  }
  .video-banner {
    top: 0;
    opacity: 0.7;
    @media @iPhone {
      top: -80px;
    }
  }
  .container {
    position: relative;
    z-index: 10;
  }
  .headerSlider h2 {
    @media @iPhone {
      font-size: 24px;
    }
  }
  h3 {
    @media @iPhone {
      width: 100%;
      padding-bottom: 0;
      border: none;
      font-size: 22px;
      line-height: normal;
    }
    @media @iPhone6-landscape {
      width: 100%;
      padding-bottom: 0;
      border: none;
      font-size: 22px;
      line-height: normal;
    }
    @media @iPhone7-landscape {
      width: 100%;
      padding-bottom: 0;
      border: none;
      font-size: 22px;
      line-height: normal;
    }
    span {
      @media @iPhone {
        padding: 0;
      }
    }
  }
  .store1_download_links {
    img.store1_appstore {
      @media @iPhone {
        height: 45px;
      }
    }
    img.store1_playstore {
      @media @iPhone {
        height: 51px;
        margin-left: 0 !important;
      }
    }
  }
}

.store1_banner_content {
  position: relative;
  li {
    position: relative;
    padding-left: 45px;
    i {
      position: absolute;
      left: 0;
      font-size: 30px;
    }
    p {
      font-size: 16px;
    }
  }
}

.my_books {
  @media @iPhone {
    margin-top: 0;
  }
  .nav-tabs {
    .nav-link {
      padding: 0;
      margin-right: 1.5rem;
      @media @iPhone {
        margin-right: 1rem;
      }
      @media @iPhone6-landscape {
        margin-right: 1rem;
      }
      @media @iPhone7-landscape {
        margin-right: 1rem;
      }
    }
  }
  .generate img {
    filter: brightness(1.5);
  }
  .generate:hover {
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25);
  }
}

.store1_social_icons {
  h5 {
    @media @iPhone {
      width: 100%;
      text-align: center;
    }
    @media @iphoneX-portrait {
      width: 100%;
      text-align: center;
    }
    @media @iphoneX-landscape {
      width: 100%;
      text-align: center;
    }
  }
  .social-icons {
    li {
      width: 40px;
      height: 40px;
      margin-right: 7px;
      text-align: center;
      a {
        line-height: 45px;
        font-size: 20px;
        color: #FFF;
        width: 40px;
        height: 40px;
        border: 1px solid transparent;
        border-radius: 50px;
        display: block;
        &:focus {
          outline: 0;
        }
      }
      &.facebook-icon a {
        background-color: #4267b2;
        border-color: #4267b2;
        &:focus {
          background-color: #4267b2 !important;
        }
      }
      &.twitter-icon a {
        background-color: #55acee;
        border-color: #55acee;
        &:focus {
          background-color: #55acee !important;
        }
      }
      &.youtube-icon a {
        background-color: #FF0000;
        border-color: #FF0000;
        &:focus {
          background-color: #FF0000 !important;
        }
      }
      &.instagram-icon a {
        background: #f09433;
        background: -moz-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
        background: -webkit-linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
        background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f09433', endColorstr='#bc1888',GradientType=1 );
        &:focus {
          background: #f09433 !important;
          background: -moz-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) !important;
          background: -webkit-linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%) !important;
          background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%) !important;
          filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f09433', endColorstr='#bc1888',GradientType=1 ) !important;
        }
      }
      &.telegram-icon a {
        background-color: #0088CC;
        border-color: #0088CC;
        &:focus {
          background-color: #0088CC !important;
        }
      }
    }
  }
}

.store1_index_accordion {
  a.icon_link {
    margin-right: 1rem;
    &:hover {
      text-decoration: none;
    }
    &:focus {
      text-decoration: none;
    }
  }
  i {
    width: 50px;
    height: 50px;
    background: darkred;
    font-size: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    //margin-right: 1rem;
    color: #fff;
    border-radius: 50px;
  }
  .youtube_link i {
    background: #e74c3c;
    &:before {
      margin-left: 0;
    }
  }
  .web_link i {
    background: #2980b9;
  }
  .read_materials i {
    background: #27ae60;
  }
  .card {
    box-shadow: 0 .25rem .5rem rgba(0,0,0,.1) !important;
    border-radius: 0;
    //border-color: #343a40 !important;
    &:hover {
      border-color: @ws-lightOrange !important;
    }
  }
  .card-header {
    background-color: transparent;
  }
  .card-header a {
    position: relative;
    background-color: @ws-lightOrange !important;
    color: #FFF;
    &:focus {
      background-color: @ws-lightOrange !important;
    }
    &:hover {
      text-decoration: none;
    }
    &.collapsed {
      background-color: @ws-lightOrange !important;
      i {
        transform: rotate(0);
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
      }
    }
    i {
      width: auto;
      height: auto;
      background-color: transparent;
      color: #FFF;
      position: absolute;
      right: 10px;
      top: 10px;
      margin: 0;
      transform: rotate(180deg);
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
    }
  }
  .card-body {
    @media @iPhone {
      justify-content: left !important;
    }
    .row {
      align-items: center;
      @media @iPad-landscape {
        max-width: 33.3333%;
        flex: 0 0 33.3333%;
        -ms-flex: 0 0 33.3333%;
      }
    }
    h6 {
      margin-bottom: 0;
      line-height: normal;
      font-weight: normal;
    }
    .info {
      width: 180px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      @media @iPad-portrait {
        width: 240px;
      }
      @media @iPad-landscape {
        width: 210px;
      }
      @media @iPhone5-portrait {
        width: 70%;
      }
      @media @iPhone6-portrait {
        width: 75%;
      }
      @media @iPhone7-portrait {
        width: 80%;
      }
      h6 {
        white-space: normal;
      }
    }
    .name_of_chapter {
      background-color: #cbeef1 !important;
      font-weight: 500;
      border-bottom: 2px solid #add;
    }
    .name_of_chapter:first-child {
      margin-top: 0 !important;
    }
  }
  .container {
    @media @iPhone {
      padding: 0;
    }
    @media @iPhone5-portrait {
      padding: 0;
    }
    @media @iPhone6-portrait {
      padding: 0;
    }
    @media @iPhone7-portrait {
      padding: 0;
    }
  }
}

.view_syllabus_area {
  position: relative;
  .btn-outline-info {
    color: #ffffff;
    border-color: #2ebac6 !important;
    background-color: #1399ae !important;
    &:hover {
      border-color: #2ebac6 !important;
      background-color: #1399ae !important;
    }
    &:active:focus {
      border-color: #2ebac6 !important;
      background-color: #1399ae !important;
      box-shadow: 0 .2rem 0.8rem rgba(0,0,0,.15)!important;
    }
    i {
      position: relative;
      top: 7px;
      transform: rotate(0deg);
      -webkit-transform: rotate(0deg);
      -moz-transform: rotate(0deg);
      transition: all 0.3s linear;
      -webkit-transition: all 0.3s linear;
      -moz-transition: all 0.3s linear;
      &.icon_animated {
        transform: rotate(90deg);
        -webkit-transform: rotate(90deg);
        -moz-transform: rotate(90deg);
      }
    }
  }
  span.justify-content-end {
    @media @iPad-portrait {
      justify-content: center !important;
    }
    @media @iPad-landscape {
      justify-content: center !important;
    }
    @media @iPhone {
      justify-content: center !important;
    }
    @media @iPhone5-portrait {
      justify-content: center !important;
    }
    @media @iPhone5-landscape {
      justify-content: center !important;
    }
    @media @iPhone6-portrait {
      justify-content: center !important;
    }
    @media @iPhone6-landscape {
      justify-content: center !important;
    }
    @media @iPhone7-portrait {
      justify-content: center !important;
    }
    @media @iPhone7-landscape {
      justify-content: center !important;
    }
    @media @iphoneX-portrait {
      justify-content: center !important;
    }
    @media @iphoneX-landscape {
      justify-content: center !important;
    }
  }
}

#emptyBooks1 {
  table {
    margin-bottom: 15px;
    @media screen and (max-width: 767px) {
      overflow-x: auto;
      min-height: 0.01%;
      width: 100%;
      display: block;
    }
    tr {
      td {
        padding: 0.5rem;
      }
    }
  }
}