@import "variables/color.less";
@import "variables/fonts.less";

.evidyaStore {
  @media (min-width: 1200px) {
    .container {
      max-width: 1300px;
    }
  }
  p {
    margin-bottom: 0;
    color: @grey1;
  }
  margin-top: 1rem;
  min-height: 600px;
  padding-bottom: 2rem;
  .Sidewrapper {
    background: @grey;
    padding: 1rem;
    .search {
      border-radius: 50px;
      padding: 5px 25px;
      width: 100%;
      border: 1px solid @sageTheme;
      outline: 0;
    }
    h4 {
      color: @grey1;
      font-family: @sageFont;
      font-size: 14px;
      margin-top: 1rem;
      font-weight: bold;
      text-transform: uppercase;
    }
    #langaugeList {
      list-style: none;
      padding-left: 0;
      li {
        padding: 5px 15px;
      }
    }
    .categories {
      //font-weight: bold;
      color: #000;
      font-size: 14px;
      padding: 5px 15px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      i {
        width: 150px;
        text-align: right;
      }
    }
    .keywords {
      li {
        list-style-type: none;
        a {
          color: @sageTheme;
          font-weight: normal;
          cursor: pointer;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
    .viewtitle {
      color: @blue;
    }
    ul {
      li {
        padding: 0.2rem 0;
        a {
          font-family: @sageFont;
          cursor: pointer;
          font-size: 14px;
          color: @grey1;
          &.active {
            color: @blue;
            font-weight: bold;
          }
        }
      }
    }

    ul.subCategory {
      .sub-menu {
        overflow-y: auto;
        max-height: 170px;
      }
    }
  }

  .result-wrapper {
    p {
      padding: 0;
      margin: 0;
    }
    button {
      margin-left: 10px;
      color: @blue;
      padding: 0.3rem 1.5rem;
      font-family: @sageFont;
      font-weight: @medium;
      i {
        color: fade(@grey1, 50%);
        margin-left: 10px;
      }
      &.modify {
        background: @sageTheme;
        color: #fff;
      }
    }
  }
  .showResult {
    > div {
      border-left: 1px solid fade(black, 30%);
      padding: 0px 1px;
      width: 30%;
      text-align: center;
      &:first-child {
        border: none;
      }
    }
    p {
      color: @grey1;
      span {
        font-weight: bold;
      }
    }
    input[type='number'] {
      width: 40px;
    }
  }
  .bg-gray {
    background: @grey;
    padding: 1rem;
  }


  .subCategory {
    padding: 0;
    list-style-type: none;
  }
  .bookImg {

    text-align: center;
  }
}
.pagination {
  padding: 0;
  margin: 0;
  .page-link {
    color: @grey1 !important;
  }
}
.pager {
  justify-content: flex-end;

  p {
    color: @grey1;
    margin-right: 10px;
    font-size: 16px;
  }
  input[type='text'] {
    width: 40px;
    margin-right: 10px;
  }
  .go{
    background: @grey1;
    border:none;
    outline:0;
    border-radius: 4px;
    width: 70px;
    color:#fff;
  }
}
.border-pagination{
  //border:0.5px solid @blue;
  border-radius: 4px;
  height:60px;
  padding: 0!important;
  overflow-y: hidden;
  overflow-x: auto;
}
.bookContainer {
  position: relative;
  &:last-child {
    border-bottom: none;
  }
  .language{
    background:#2b383e;
    min-width: 80px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px 0;
    img{
      width: 28px;
      height: 18px;
    }
    span{
      color:#fff;
      text-transform: uppercase;
      font-size: 12px;
    }
    &.hindi{
      background:#EE5670;
    }
    &.tamil{
      background:#3D8CFF;
    }
    &.marathi{
      background:#239A99;
    }
    &.english{
      background:#233982;
    }
    &.bengali{
      background:green;
    }
    &.kannada{
      background: #FF7560;
    }
    &.malayalam{
      background:#6753FF;
    }
    &.odiya{
      background:#FFE51E;
    }
    &.sanskrit{
      background: #FFBF00;
    }
    &.urdu{
      background: #0b163f;
    }
    &.telugu{
      background: #0b2e13;
    }
  }
  border-bottom: 1px solid rgba(0, 0, 0, .125);
  padding: 2rem 0;
  .textWrapper {
    margin-left: 1rem;

    h3 {
      font-size: 16px;
      font-weight: bold;
      color: @grey1;
    }
  }
  p {
    font-size: 14px;
    color: @grey1;
    &.editBy {
      font-weight: bold;
    }
    span {
      color: #233982;
      //margin-left: 5px;
    }
    a {
      color: @blue;
      font-weight: normal;
      margin-left: 5px;
    }
  }
  h4 {
    font-size: 14px;
    margin-bottom: 0;
    font-weight: bold;
    color: @grey1;
  }
  .bookImage {
    height: 233px;
    width: 153px;
  }
  .languageIcon {
    height: 20px;
  }
  .addLibrary {
    position: absolute;
    bottom: 30px;
    right: 10px;
    a {
      color: @blue;
      align-items: center;
      font-weight: bold;
      &:hover {
        text-decoration: none;

      }
      i {
        margin-right: 8px;
        color: @blue;
      }
    }
  }

}
.add{
  display: none;
}
.minus{
  .minimize{
    display: none;
  }
  .add{
    display:block;
  }
}
.lock-img{
  position: absolute;
  top:25px;
  left:-10px;
}
.loading-icon {
  width: 100%;
  height: 100%;
  background-color: rgba(68, 68, 68, 0.64);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999; }
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  border-radius: 4px; }

.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out; }

.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s; }

.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0; }

.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s; }

.loader:after {
  left: 3.5em; }

@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em; }
  40% {
    box-shadow: 0 2.5em 0 0; } }
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em; }
  40% {
    box-shadow: 0 2.5em 0 0; } }

.btn-primary1{
  background: #233982 !important;
  border: #233982 ;
}

.evidyaStore #displayResults {
  margin-top: 10px;
}
.evidyaStore .result-wrapper button i {
  font-size: 20px;
}
