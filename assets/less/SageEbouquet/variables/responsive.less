// Desktops
@24Desktop: ~"only screen and (min-width: 1920px) and (max-height: 1200px)";
@23Desktop: ~"only screen and (min-width: 1920px) and (max-height: 1080px)";
@22Desktop: ~"only screen and (min-width: 1680px) and (max-height: 1050px)";
@20Desktop: ~"only screen and (min-width: 1600px) and (height: 900px)";
@19Desktop: ~"only screen and (min-width: 1440px) and (height: 900px)";

@mediumScreen: ~"only screen and (max-width: 1400px)";

//Tabs
@largeTabs: ~"only screen and (min-width: 1366px) and (height: 1024px)";
@mediumTabs: ~"only screen and (min-width: 768px) and (max-width: 1199px)";
@smallTabs: ~"only screen and (min-width: 768px) and (max-width: 991px)";

@smallScreen: ~"only screen and (max-width: 1024px)";

// Mobiles
@largeMobiles: ~"only screen and (max-width: 768px)";
@smallMobiles: ~"only screen and (max-width: 575px)";
@extraSmallMobiles: ~"only screen and (max-width: 480px)";

@media @24Desktop {
  .content-Preview {
    .container-fluid {
      padding-top: 3rem !important;
    }
    .index-left {
      margin-top:4rem;
    }
    .desktop_image img {
      width: 600px;
    }
    .index_buttons {
      margin-top: 3rem;
    }
    .index_buttons ul li a.humanities, .index_buttons ul li a.management {
      width: 300px;
      font-size: 20px;
      height: 75px;
    }
    .index_buttons ul li a.management {
      padding: 20px 50px;
    }
    .index_quick_links {
      margin-top: 4rem;
    }
    .index_quick_links ul li a {
      font-size: 20px;
    }
    .bg_circle {
      width: 1300px;
      height: 1300px;
      top: -150px;
      right: -350px;
    }
    .bg_outline_circle {
      width: 1300px;
      height: 1300px;
      top: -150px;
      right: -330px;
    }
    .index-right .heading h1 {
      font-size: 40px;
    }
    .index-right .heading h4 {
      font-size: 20px;
    }
    .index-right .icon_texts .icon img {
      width: 100px;
    }
    .index-right .icon_texts .texts h5 {
      font-size: 20px;
    }
    .index-right .icon_texts .texts p {
      font-size: 18px;
    }
    .index-right .icon_texts {
      padding-bottom: 2rem;
    }
  }
}

@media @23Desktop {
  .content-Preview {
    .container-fluid {
      padding-top: 3rem !important;
    }
    .index-left {
      margin-top:3rem;
    }
    .index-right .icon_texts {
      padding-bottom: 1rem;
    }
  }
}

@media @22Desktop {
  .content-Preview {
    .container-fluid {
      padding-top: 3rem !important;
    }
    .index-left {
      margin-top:3rem;
    }
    .desktop_image img {
      width: 550px;
    }
    .index_buttons {
      margin-top: 3rem;
    }
    .index_buttons ul li a.humanities, .index_buttons ul li a.management {
      width: 300px;
      font-size: 20px;
      height: 75px;
    }
    .index_buttons ul li a.management {
      padding: 20px 50px;
    }
    .index_quick_links {
      margin-top: 4rem;
    }
    .index_quick_links ul li a {
      font-size: 20px;
    }
    .bg_circle {
      width: 1200px;
      height: 1200px;
      top: -130px;
      right: -370px;
    }
    .bg_outline_circle {
      width: 1200px;
      height: 1200px;
      top: -130px;
      right: -350px;
    }
    .index-right .heading h1 {
      font-size: 36px;
    }
    .index-right .heading h4 {
      font-size: 20px;
    }
    .index-right .icon_texts .icon img {
      width: 90px;
    }
    .index-right .icon_texts .texts h5 {
      font-size: 20px;
    }
    .index-right .icon_texts .texts p {
      font-size: 18px;
    }
    .index-right .icon_texts {
      padding-bottom: 1.5rem;
    }
  }
}

@media @20Desktop {
  .content-Preview {
    .container-fluid {
      padding-top: 2rem !important;
    }
    .index-left {
      margin-top:2rem;
    }
    .desktop_image img {
      width: 480px;
    }
    .index_buttons ul li a.humanities, .index_buttons ul li a.management {
      width: 270px;
      font-size: 18px;
      height: 70px;
    }
    .index_buttons ul li a.management {
      padding: 20px 50px;
    }
    .index_quick_links {
      margin-top: 2rem;
    }
    .index_quick_links ul li a {
      font-size: 18px;
    }
    .bg_circle {
      width: 1100px;
      height: 1100px;
      top: -170px;
      right: -370px;
    }
    .bg_outline_circle {
      width: 1100px;
      height: 1100px;
      top: -170px;
      right: -350px;
    }
    .index-right .heading h1 {
      font-size: 30px;
    }
    .index-right .heading h4 {
      font-size: 17px;
    }
    .index-right .icon_texts .icon img {
      width: 80px;
    }
    .index-right .icon_texts .texts h5 {
      font-size: 17px;
    }
    .index-right .icon_texts .texts p {
      font-size: 15px;
    }
    .index-right .icon_texts {
      padding-bottom: 1rem;
    }
  }
}

@media @19Desktop {
  .content-Preview {
    .container-fluid {
      padding-top: 2rem !important;
    }
    .index-left {
      margin-top:2rem;
    }
    .desktop_image img {
      width: 480px;
    }
    .index_buttons ul li a.humanities, .index_buttons ul li a.management {
      width: 250px;
      font-size: 17px;
    }
    .index_buttons ul li a.management {
      padding: 18px 50px;
    }
    .index_quick_links {
      margin-top: 2rem;
    }
    .index_quick_links ul li a {
      font-size: 17px;
    }
    .bg_circle {
      width: 1050px;
      height: 1050px;
      top: -150px;
      right: -370px;
    }
    .bg_outline_circle {
      width: 1050px;
      height: 1050px;
      top: -150px;
      right: -350px;
    }
    .index-right .heading h1 {
      font-size: 32px;
    }
    .index-right .heading h4 {
      font-size: 17px;
    }
    .index-right .icon_texts .icon img {
      width: 80px;
    }
    .index-right .icon_texts .texts h5 {
      font-size: 16px;
    }
    .index-right .icon_texts .texts p {
      font-size: 15px;
    }
    .index-right .icon_texts {
      padding-bottom: 0.8rem;
    }
  }
}

@media @largeTabs {
  .content-Preview {
    .container-fluid {
      padding-top: 2rem !important;
    }
    .index-left {
      margin-top:3rem;
    }
    .index-right .icon_texts {
      padding-bottom: 1.5rem;
    }
    .index_buttons, .index_quick_links {
      margin-top: 3rem;
    }
    .index-right .heading h1 {
      font-size: 32px;
    }
    .index-right .heading h4 {
      font-size: 17px;
    }
    .index-right .icon_texts .icon img {
      width: 80px;
    }
    .index-right .icon_texts .texts h5 {
      font-size: 16px;
    }
    .index-right .icon_texts .texts p {
      font-size: 15px;
    }
    .bg_circle {
      width: 1080px;
      height: 1080px;
      top: -130px;
      right: -440px;
    }
    .bg_outline_circle {
      width: 1080px;
      height: 1080px;
      top: -130px;
      right: -420px;
    }
    .index_quick_links ul li a {
      font-size: 17px;
    }
  }
}

@media @mediumTabs {
  /*.content-Preview {
    height: auto !important;
  }*/
  .bg_circle, .bg_outline_circle {
    display: none;
  }
  .index-left {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .index-right {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .index-right .heading h1 {
    font-size: 26px !important;
  }
  .index-right .heading h4 {
    font-size: 15px !important;
  }
  .desktop_image img {
    width: 400px !important;
  }
  .index_buttons ul li {
    padding: 0 10px !important;
  }
  .index_buttons ul li a.management {
    padding: 16px 30px !important;
    font-size: 15px !important;
    width: 180px !important;
    height: 60px !important;
  }
  .index_buttons ul li a.humanities {
    padding: 8px 20px !important;
    font-size: 15px !important;
    width: 180px !important;
    height: 60px !important;
  }
  .index_quick_links ul li a {
    font-size: 15px !important;
  }
  .searchbar {
    flex: 0 0 60%;
    max-width: 60%;
  }
  .footer-menu {
    height: auto !important;
    .col-8 {
      flex: 0 0 100%;
      max-width: 100%;
      ul {
        justify-content: center;
      }
    }
    .col-4 {
      flex: 0 0 100%;
      max-width: 100%;
      margin-top: 1.5rem;
    }
  }
}

@media @smallTabs {
  .index-left {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .index-right {
    flex: 0 0 80%;
    max-width: 80%;
    top: 0 !important;
  }
  .searchbar {
    margin: 0 auto;
    flex: 0 0 100%;
    max-width: 100%;
  }
  footer.footer-menu ul li a {
    font-size: 11px !important;
  }
  header .logo img {
    width: 250px !important;
  }
  .library #content-data-books .lib-showcase {
    min-height: 220px !important;
  }
  .library #content-data-books .card {
    width: 190px !important;
    margin-bottom: 20px !important;
  }
  .library #content-data-books .card img {
    height: 220px !important;
  }
  .library #content-data-books .uncover {
    height: 220px !important;
  }
}

@media @largeMobiles {
  .ebouquet .banner-info h1.maintext, .ebouquet .banner-info h1.subtext, .ebouquet .ask-options h1 {
    font-size: 22px !important;
  }
  .ebouquet .banner-info img {
    width: 100% !important;
  }
  .ebouquet .big-links a h3 {
    font-size: 16px !important;
    padding: 1.5rem 1rem !important;
  }
  .ebouquet .big-links {
    margin-top: 20px !important;
  }
  .ebouquet .big-links .mb-4 {
    margin-bottom: 15px !important;
  }
  .ebouquet .big-links a {
    border-radius: 50px !important;
  }
  .ebouquet .ask-options .options-info {
    padding-bottom: 0 !important;
  }
  footer .promise-icon img {
    width: 100px;
  }
  .ebouquet header .ws-header .navbar-nav.right-menu {
    position: initial !important;
  }
  .content-Preview {
    height: auto !important;
  }
  .bg_circle, .bg_outline_circle {
    display: none;
  }
  .index-left {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .index-right {
    flex: 0 0 100%;
    max-width: 100%;
    top: 0 !important;
  }
  .searchbar {
    margin: 0 auto;
    flex: 0 0 100%;
    max-width: 100%;
  }
  footer.footer-menu ul li {
    padding: 0 5px !important;
  }
  header .logo img {
    width: 250px !important;
  }
  .footer-menu {
    height: auto !important;
    .col-8 {
      flex: 0 0 100%;
      max-width: 100%;
      padding: 0;
      .d-flex.social-icons {
        display: flex !important;
        img {
          width: 35px;
          height: 35px;
        }
      }
      .d-flex {
        display: inline-block !important;
        width: 100%;
        p {
          display: inline-block;
          &:first-child {
            display: block;
          }
        }
      }
      ul {
        justify-content: center;
        padding-left: 0;
        a {
          padding-right: 0 !important;
        }
      }
    }
    .col-4 {
      flex: 0 0 100%;
      max-width: 100%;
      margin-top: 1.5rem;
    }
  }
  .index_buttons ul li a.management {
    padding: 16px 30px !important;
    font-size: 15px !important;
    width: 180px !important;
    height: 60px !important;
  }
  .index_buttons ul li a.humanities {
    padding: 8px 20px !important;
    font-size: 15px !important;
    width: 180px !important;
    height: 60px !important;
  }
  .index_quick_links ul li a {
    font-size: 15px !important;
  }
  .index-right .heading h1 {
    font-size: 26px !important;
  }
  .index-right .heading h4 {
    font-size: 15px !important;
  }
  .desktop_image img {
    width: 400px !important;
  }
  .index_buttons ul li {
    padding: 0 10px !important;
  }
  footer.footer-menu > .row {
    margin: 0;
  }
  .ws-header div.mobile-profile {
    display: flex !important;
    right: 0;
    text-align: center;
    width: 100%;
  }
  .etexts .ws-header div.mobile-profile .nav-item {
    color: black !important;
    padding-top: 0;
  }
  .etexts .ws-header div.mobile-profile .nav-item .nav-link {
    font-family: 'Roboto-Regular';
    font-size: 12px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .etexts .ws-header div.mobile-profile .nav-item a img {
    width: 30px;
    height: 30px;
  }
  .etexts .ws-header div.mobile-profile p.user-name {
    margin-bottom: 0;
  }
  .etexts .ws-header div.mobile-profile .edit-btn {
    width: 18px;
    height: 18px;
    display: inline-block;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    position: absolute;
    left: 40px;
    top: 35px;
    text-align: center;
    i {
      font-size: 12px;
    }
  }
  .etexts .ws-header div.mobile-profile #logout {
    border-top: 1px solid #ddd;
  }
  .etexts .ws-header div.mobile-profile .nav-item .nav-link.login {
    padding: .5rem 1rem;
    color: #ffffff;
  }
  .ws-header .navbar-nav.right-menu li.nav-item {
    display: none !important;
  }
  footer p {
    //font-size: 11px;
  }
  .etexts .ws-header {
    border-bottom: none !important;
    height: 60px;
  }
  .etexts .user_profile .tab-content .jumbotron form .media .continue {
    width: 100% !important;
  }
  .ebouquet header .ebouquet-logo {
    padding: 30px 10px 10px !important;
  }
  .evidyaStore {
    margin-top: 2rem !important;
  }

  nav #menu {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    background: #172051;
    -webkit-transition: all .35s ease;
    transition: all .35s ease;
  }
  nav #menu ul {
    margin: 0;
    -webkit-transform: translateY(15vh);
    transform: translateY(15vh);
    -webkit-transition: all .35s ease;
    transition: all .35s ease;
    padding-left: 0;
  }
  nav ul li {
    list-style: none;
    display: block;
    text-align: center;
    margin: 0;
  }
  nav #menu ul li a:hover {
    color: #FFF;
  }
  nav #menu ul li a {
    color: #FFF;
    //text-transform: uppercase;
    display: inline-block;
    padding: 15px;
    font-size: 24px;
    line-height: 20px;
    -webkit-transition: all 0.2s ease-out;
    transition: all 0.2s ease-out;
  }
  #toggle.open {
    position: fixed;
  }
  #toggle {
    width: 31px;
    height: 30px;
    position: relative;
    top: 0px;
    right: 0;
    background: #26358c;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: .25s ease-in-out;
    transition: .25s ease-in-out;
    cursor: pointer;
    z-index: 555;
    border-radius: 3px;
  }
  #toggle span {
    display: block;
    position: absolute;
    height: 2px;
    width: 20px;
    background: #FFF;
    border-radius: 25px;
    opacity: 1;
    left: 50%;
    margin-left: -10px;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: .25s ease-in-out;
    transition: .25s ease-in-out;
  }
  #toggle span:nth-child(1) {
    top: 8px;
  }
  #toggle span:nth-child(2),
  #toggle span:nth-child(3) {
    top: 14px;
  }
  #toggle span:nth-child(4) {
    top: 20px;
  }
  #toggle.open span:nth-child(1) {
    top: 29px;
    width: 0%;
    left: 50%;
    margin: 0;
  }
  #toggle.open span:nth-child(2) {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  #toggle.open span:nth-child(3) {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }
  #toggle.open span:nth-child(4) {
    top: 29px;
    width: 0%;
    left: 50%;
    margin: 0;
  }
  body.active nav #menu {
    opacity: 1;
    visibility: visible;
  }
  body.active nav #menu ul {
    -webkit-transform: translateY(20vh);
    transform: translateY(20vh);
  }
  .suggest_this_book {
    position: relative !important;
    bottom: -20px !important;
    right: 0 !important;
  }
}

@media @smallMobiles {
  footer.footer-menu ul li {
    margin-bottom: 10px;
  }
  .etexts .ws-menu-start {
    //padding: 1rem 1.5rem !important;
  }
  .desktop_image img {
    width: 310px !important;
  }
  .index_quick_links ul li a {
    font-size: 14px !important;
  }
  header .logo img {
    width: 200px !important;
  }
  .index_buttons ul li {
    width: 100%;
    margin-bottom: 30px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .index_buttons ul li a.humanities, .index_buttons ul li a.management {
    width: 100% !important;
  }
  .searchbar {
    padding-left: 15px !important;
    .input-group-append {
      position: absolute;
      right: 0;
      button {
        right: 25px;
      }
    }
  }
  .footer-menu div.col-4 p br:first-child {
    display: none !important;
  }
  .index_quick_links ul {
    display: block;
    text-align: center;
    li {
      padding: 10px 10px !important;
      border-bottom: 1px solid #EEE;
      border-right: none !important;
    }
  }
  body.active nav #menu ul {
    padding-top: 30px;
  }
}

@media @extraSmallMobiles {
  .content-Preview {
    .container-fluid {
      padding-top: 2rem !important;
    }
  }
  .desktop_image img {
    width: 230px !important;
  }
  .ws-header div.mobile-profile {
    //position: relative;
    text-align: center;
    width: 100%;
    right: 0;
    //border-top: 1px solid #eee;
  }
  .etexts .ws-header {
    height: 50px !important;
    padding: 0;
    a {
      width: 100%;
      text-align: center;
      .logo img {
        margin: 0 auto;
        margin-bottom: 10px;
      }
    }
  }
  .index-right {
    padding-left: 15px !important;
  }
  .index-right .heading h1 {
    font-size: 22px !important;
  }
  .index-right .icon_texts .icon img {
    width: 50px !important;
  }
  .index-right .icon_texts .texts h5 {
    font-size: 13px !important;
  }
  .index-right .icon_texts .texts p {
    font-size: 12px !important;
  }
  .footer-menu .col-8 ul {
    display: block !important;
    li {
      line-height: normal;
      border-right: none !important;
      //border-bottom: 1px solid #eee;
      //padding: 7px !important;
      margin-bottom: 0 !important;
    }
  }
  footer.footer-menu ul li a {
    font-size: 11px !important;
  }
  .etexts .evidyaLogin {
    top: 3.8rem;
    right: 0px;
    left: 0;
    margin: 0 auto;
    width: 270px;
  }
  .etexts .evidyaLogin::before {
    left: 70px;
  }
}

@media @mediumScreen {
  .evidyaStore {
    .bookContainer .textWrapper {
      margin-left: 1.5rem;
    }
  }
  .ebouquet .ask-options .options-info .circle-style {
    margin: 0 15px 30px !important;
  }
  .ebouquet .big-links a h3 {
    font-size: 28px ;
  }
  header .ws-header .navbar-nav.right-menu li .nav-link {
    padding: 0 2rem !important;
    text-align: center;
  }
}

@media @smallScreen {
  .ebouquet .banner-info h1.maintext, .ebouquet .banner-info h1.subtext, .ebouquet .ask-options h1 {
    font-size: 30px;
  }
  .ebouquet .banner-info img {
    width: 80% !important;
  }
  .ebouquet .ask-options ul.title-style li {
    margin: 0 15px !important;
  }
  .ebouquet .big-links a h3 {
    font-size: 22px;
  }
  header .ws-header .navbar-nav.right-menu li .nav-link {
    padding: 0 1.5rem !important;
    text-align: center;
  }
  .evidyaStore {
    .bookContainer .bookImage {
      height: 180px;
      width: 120px;
    }
    .bookContainer > div:first-child {
      width: 20%;
    }
    .bookContainer > div:nth-child(3) {
      width: 20%;
    }
    .bookContainer .language {
      min-width: 100px;
    }
  }
  .etexts .libraryDimension {
    h3 {
      padding: 0 1rem;
    }
    p {
      padding: 0.5rem 1rem;
    }
    .bookContainer > div:first-child {
      width: 20%;
    }
    .bookContainer > div:nth-child(3) {
      width: 20%;
    }
    .bookContainer .bookImage {
      height: 180px;
      width: 120px;
    }
    .bookContainer .textWrapper {
      margin-left: 1.5rem;
    }
    .bookContainer .language {
      min-width: 100px;
    }
    .bookContainer .addLibrary {
      top: 80px;
      right: 0;
    }
  }
}

@media @largeMobiles {
  .ebouquet .ask-options ul.title-style li {
    margin: 0 10px !important;
  }
  .evidyaStore {
    .bookContainer > div:first-child {
      width: 120px;
      margin-right: 15px;
    }
    .bookContainer > div:nth-child(2) {
      width: 100%;
      margin-left: 0;
      //margin-top: 1rem;
    }
    .bookContainer > div:nth-child(3) {
      width: 120px;
      position: absolute;
      top: 35px;
      right: 0;
    }
    .bookContainer .language {
      margin-left: 0;
      //margin-top: 1rem;
    }
    .showResult > div {
      width: auto !important;
    }
  }
  .etexts .libraryDimension {
    .bookContainer > div:first-child {
      width: 120px;
    }
    .bookContainer > div:nth-child(2) {
      width: 100%;
      margin-left: 0;
      margin-top: 1rem;
    }
    .bookContainer > div:nth-child(3) {
      width: 100px;
      position: absolute;
      top: 30px;
      right: 0;
    }
    .bookContainer .language {
      margin-left: 0;
      //margin-top: 1rem;
      min-width: 80px;
    }
    h3 {
      padding: 0;
    }
    p {
      padding: 0;
    }
    #library {
      flex: 0 0 100%;
      max-width: 100%;
    }
    ul.nav {
      display: inline-flex;
    }
    .nav-tabs li a {
      font-size: 14px;
      padding: 1rem !important;
    }
    .container-wrapper input.search {
      width: 100%;
      font-size: 15px;
      margin-bottom: 1rem;
      margin-top: 1rem;
    }
    h4 {
      padding: 0;
    }
    .bookContainer .addLibrary {
      top: 0;
      right: 0;
      position: relative;
    }
  }
}

@media only screen and (min-width: 992px) {
  .show_filters_btn {
    display: none;
  }
  #showFilters {
    display: block;
  }
}
@media only screen and (max-width:991px) {
  .show_filters_btn {
    display: block;
  }
}

@media only screen and (max-width: 1400px) {
  .content-Preview {
    height: auto !important;
  }
}

@media only screen and (max-width: 1250px) {
  header .ws-header .navbar-nav.right-menu li .nav-link {
    font-size: 14px !important;
  }
}

@media only screen and (max-width: 767px) {
  .library #content-data-books .card {
    margin-bottom: 20px;
  }
  .library .tab-content {
    margin-top: 0;
  }
  #ebpagination .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
  #ebpagination .pagination .page-link {
    padding: 0.5rem;
  }
  .evidyaStore .bookContainer > div:nth-child(2) {
    margin-top: 1rem;
  }
}
@media only screen and (max-width: 575px) {
  .library #content-data-books .card {
    width: 180px !important;
  }
  .library #content-data-books .lib-showcase {
    min-height: 200px !important;
  }
  .library #content-data-books .card img {
    height: 200px !important;
  }
  .library #content-data-books .uncover {
    height: 200px !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) and (orientation : portrait) {
  .library #content-data-books .card {
    width: 100% !important;
    margin-bottom: 10px !important;
  }
  .library #content-data-books .lib-showcase {
    //min-height: 150px !important;
  }
  .library #content-data-books .card img {
    //height: 150px !important;
  }
  .library #content-data-books .uncover {
    //height: 150px !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) and (orientation : landscape) {
  .library #content-data-books .card {
    width: 180px !important;
    margin-bottom: 10px !important;
  }
  .library #content-data-books .lib-showcase {
    min-height: 220px !important;
  }
  .library #content-data-books .card img {
    height: 220px !important;
  }
  .library #content-data-books .uncover {
    height: 220px !important;
  }
}
@media only screen and (max-width: 330px) {
  .library #content-data-books .card {
    width: 100% !important;
    margin-bottom: 10px !important;
  }
  .library #content-data-books .lib-showcase {
    min-height: 160px !important;
  }
  .library #content-data-books .card img {
    height: 160px !important;
  }
  .library #content-data-books .uncover {
    height: 160px !important;
  }
}
