@import "ws_color_theme.less";
@import "ws_fonts.less";
@import "responsive.less";
.sliders {
  padding-bottom: 20px;
  .quick-menu {
    .navbar-nav {
      .nav-item {
        .nav-link {
          color: @ws-darkBlack;
          font-size: @ws-menu-fontSize;
          font-family: @ws-header-font;
          font-weight: @ws-header-fontWeight;
          &:hover {
            color: @ws-lightOrange;
          }
        }
        &.active {
          .nav-link {
            color: @ws-lightOrange;
          }
        }
      }
    }
  }
  .header {
    padding-bottom: 10px;
    padding-top: 10px;
    border-bottom: none;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
    &.card {
      border: 1px solid @ws-border;
    }
    .card-body {
      padding-top: 0;
      padding-bottom: 0;
    }
    p {
      color: @ws-darkBlack;
      font-family: @ws-header-font;
      font-size: @ws-header-fontSize;
      margin-bottom: 0;
      strong {
        font-weight: @ws-header-fontWeight;
      }
    }
    button {
      background-image: linear-gradient(270deg, @ws-gradient-start 0%, @ws-gradient-end 100%);
      color: @ws-white;
      text-transform: uppercase;
      font-family: @ws-header-font;
      font-size: @ws-menu-fontSize;
      width: 160px;
      height: 40px;
      margin-left: 1rem;
      &:focus {
        outline: 0;
      }
    }
  }
  #topSellers {
    padding-bottom: 1.5rem;
    border: 1px solid @ws-border;
    //border-top: none;
    .item{
      position: relative;
      padding:10px ;
    }
    > h4 {
      font-size: @ws-menu-fontSize;
      font-family: @ws-header-font;
      color: fade(@ws-darkBlack, 40%);
      text-transform: uppercase;
      padding-top: 20px;
      padding-left: 20px;
    }
    .item {
      .image-wrapper {
        width: 132px;
        height: 165px;
        position: relative;
        z-index: 9999;
        img {
          width: 132px;
          height: 165px;
          padding: 10px;
          position: relative;
          border-radius: 14px;
        }
        h3 {
          position: absolute;
          font-size: @ws-menu-fontSize - 4;
          font-weight: @ws-header-fontWeight;
          color: @ws-white;
          background-color: @ws-lightOrange;
          padding: 7px 14px;
          bottom: 32px;
          left: 4px;
          margin-bottom: 0;
          border-top-right-radius: 2px;
          border-bottom-right-radius: 2px;
          -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
          -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
          box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
          &:after {
            content: ' ';
            position: absolute;
            width: 0;
            height: 0;
            left: 0;
            top: 100%;
            border-width: 2px 3px;
            border-style: solid;
            border-color: @ws-darkOrange @ws-darkOrange transparent transparent;
          }

        }
      }
      .content-wrapper {
        width: 288px;
        height: 113px;
        position: absolute;
        bottom: 5px;
        padding: 10px;
        border-radius: 6px;
        -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
        -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
        background: @ws-white;
        @media @iPhone {
          width: 258px;
        }
        h3 {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          height: 43px; /* fallback */
          -webkit-line-clamp: 3; /* number of lines to show */
          -webkit-box-orient: vertical;
        }
        h3, p {
          float: right;
          width: 140px;
          font-size: @ws-menu-fontSize - 2;
          font-family: @ws-header-font;
          color: @ws-darkBlack;
          font-weight: normal;
          margin-bottom: 0;
          @media @iPhone {
            width: 120px;
          }
        }
        p {
          &.sub-name {
            font-weight: @ws-header-fontWeight;
            font-size: @ws-menu-fontSize - 4;
            margin-bottom: 0.5rem;
            span {
              color: fade(@ws-darkBlack, 40%);
              font-size: @ws-menu-fontSize - 6;
            }
          }
          &.complete {
            color: fade(@ws-darkBlack, 40%);
            font-size: @ws-menu-fontSize - 6;
          }
          &.price {
            color: @ws-red;
            font-size: @ws-menu-fontSize;
            font-family: @ws-banner-font;
            span {
              &.ori-price {
                font-size: @ws-menu-fontSize - 4;
                text-decoration: line-through;
                color: fade(@ws-darkBlack, 60%);
                margin-left: 5px;
              }
            }
          }
        }
      }
    }
  }
}
.books-types {
  padding-top: 2rem;
  padding-bottom: 1rem;
}
  #topSchoolBooks,#topCollegeBooks,#topTestBooks,#featuredBooks,#recentRead {
    .content-header{
      > h4 {
        font-size: @ws-menu-fontSize - 2;
        font-family: @ws-header-font;
        color: fade(@ws-darkBlack, 40%);
        text-transform: uppercase;
        margin:0;
        padding:16px 20px;
        @media @iPad-portrait{
          padding:10px 0 10px 20px;
        }
      }
      >a{
        padding:16px 20px;
        font-size: @ws-menu-fontSize - 2;
        font-family: @ws-header-font;
        color: @blue;
        @media @iPad-portrait{
          padding:10px 8px 10px 0;
        }
      }
    }
    .topSchoolBooks {
      .item {
        padding: 10px;
        .image-wrapper {
          width: 132px;
          height: 165px;
          position: relative;
          z-index: 999;
          img {
            width: 132px;
            height: 165px;
            position: relative;
            border-radius: 4px;
            box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
          }
          h3 {
            position: absolute;
            font-size: @ws-menu-fontSize - 4;
            font-weight: @ws-header-fontWeight;
            color: @ws-white;
            background-color: @ws-lightOrange;
            padding: 7px 14px;
            bottom: 32px;
            left: -6px;
            margin-bottom: 0;
            border-top-right-radius: 2px;
            border-bottom-right-radius: 2px;
            -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
            -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
            box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
            &:after {
              content: ' ';
              position: absolute;
              width: 0;
              height: 0;
              left: 0;
              top: 100%;
              border-width: 2px 3px;
              border-style: solid;
              border-color: @ws-darkOrange @ws-darkOrange transparent transparent;
            }

          }
        }
        .content-wrapper {
          height: 113px;
          margin-top: 8px;
          h3{
            height:43px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3; /* number of lines to show */
            -webkit-box-orient: vertical;
          }
          h3, p {
            font-size: @ws-menu-fontSize - 2;
            font-family: @ws-header-font;
            color: @ws-darkBlack;
            font-weight: normal;
            margin-bottom: 0;
            width: 132px;
          }
          p {
            &.sub-name {
              font-weight: @ws-header-fontWeight;
              font-size: @ws-menu-fontSize - 4;
              margin-bottom: 0.5rem;
              span {
                color: fade(@ws-darkBlack, 40%);
                font-size: @ws-menu-fontSize - 6;
              }
            }
            &.complete {
              color: fade(@ws-darkBlack, 40%);
              font-size: @ws-menu-fontSize - 6;
            }
            &.price {
              color: @ws-red;
              font-size: @ws-menu-fontSize;
              font-family: @ws-banner-font;
              span {
                &.ori-price {
                  font-size: @ws-menu-fontSize - 4;
                  text-decoration: line-through;
                  color: fade(@ws-darkBlack, 60%);
                  margin-left: 5px;
                }
              }
            }
          }
          @media @iPhone{
            width: 132px;
          }
        }
      }

    }
  }
  @sliderBg:#f5eee4;
  .slider-container{
    background: fade(@sliderBg,40%);
    border-bottom-left-radius: 600px 100px;
    border-bottom-right-radius: 600px 100px;
    min-height: 500px;
    padding-bottom: 5rem;
    margin-bottom: 2rem;
    h2{
      font-family: @ws-banner-font;
      font-weight: bold;
    }
    .viewAll{
      color:@ws-darkBlack;
      border-radius: 4px;
      background: @ws-white;
      padding: 3px 10px;
      white-space: nowrap;
    }
    .popular-images{
      height: 250px;
      width: 200px;
      @media @iPad-portrait{
        height: 160px;
        width: 120px;
      }
    }
    .price {
      margin-top: 1rem;
      color: @ws-red;
      font-size: @ws-menu-fontSize;
      font-family: @ws-banner-font;
      @media @iPhone{
        margin-left: 3rem;
      }
      span {
        &.ori-price {
          font-size: @ws-menu-fontSize - 4;
          text-decoration: line-through;
          color: fade(@ws-darkBlack, 60%);
          margin-left: 5px;
        }
      }

    }
  }
  .pt-8{
    padding-top: 4rem;
  }
.video {
  min-height: 500px;
  padding-bottom: 2rem;

  a {
    &:focus {
      outline: 0;
    }
  }

  .video-banner {
    background: url("../../images/landingpageImages/pattern.svg") no-repeat right center;
    background-size: contain;
    position: absolute;
    right: 0;
    height: 250px;
    width: 300px;
  }

  h2 {
    font-family: @ws-banner-font;
    font-weight: bold;
    position: relative;
  }

  .video-images {
    width: 320px;
    height: 220px;
    border-radius: 20px;
    @media @iPad-portrait {
      width: 220px;
      height: 170px;
    }
  }

  .play-circle {
    background: fade(@ws-darkBlack, 80%);
    position: absolute;
    top: 40%;
    left: 35%;
    text-align: center;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    border: none;
    outline: 0;

    i {
      color: @ws-white;
      line-height: 2;
    }
  }

  h3 {
    width: 70%;
    text-align: center;
    border-bottom: 2px solid fade(@ws-darkBlack, 30%);
    line-height: 0.1em;
    margin: 0 auto;
    font-family: @ws-header-font;
    font-weight: normal;
    @media @iPhone {
      width: 100%;
      font-size: 16px;
    }
    @media @iPad-portrait {
      width: 100%;
    }
  }

  h3 span {
    background: #fff;
    padding: 0 10px;
  }

  .d-flex {
    img {
      @media @iPhone {
        height: 40px;
      }
    }
  }
}
#VideoModal{
  text-align: center;
  .modal-content{
    background: transparent;
    border:none;
  }
  .modal-header{
    border:none;
  }
  .modal-footer{
    border:none;
  }
  #wsVideo{
    width: 720px;
    height: 405px;
    @media @iPhone{
      width:100%;
      height: 100%;
    }
    @media @iPad-portrait {
      width: 100%;
    }
    @media @iPad-landscape {
      width: 100%;
    }
  }
  .close{
    text-shadow: none;
    color:@ws-lightOrange;
    opacity: 1;
  }
}
.quiz-wrapper{
  padding-bottom: 4rem;
  h2{
    font-family: @ws-banner-font;
    font-weight: bold;
  }
  .card{
    border:none;
    background: @ws-white;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.16);
    border-radius: 10px;
    width: 250px;
    padding: 1rem;
    margin: 1rem auto;
    .free{
      background:linear-gradient(to bottom, #41e25a, #18ae5c 73%, #099b5d);
      font-size: 12px;
      border-radius: 10px;
      width: 50px;
      text-align: center;
      color:@ws-white;
      display: block;
    }
    h3{
      font-size: 16px;
      margin-top: 1rem;
      min-height: 40px;
    }
    .tags{
      span{
        background: #cbcbcb;
        color:@ws-white;
        padding: 2px 10px;
        border-radius: 2px;
        font-size: 10px;
        margin-right: 1rem;
      }
    }
  }
  .currentAffairs{
    margin-top: 1rem;
    img{
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    p{
      margin-bottom: 0;
      font-size: 12px;
      font-family:@ws-header-font;
      color:@ws-darkBlack;
      width: 200px;
      white-space: nowrap;
      overflow: hidden;
      display: inline-block;
      text-overflow: ellipsis;
    }
  }
  .bb-1{
    border-bottom: 1px solid fade(@ws-darkBlack,20%);
    padding-bottom: 0.5rem;
  }
  .test-details{
    margin-top: 1rem;
    p{
      color: @ws-darkBlack;
      span{
        color:fade(@ws-darkBlack,50%);
        margin-right: 5px;
      }
    }
  }
  .btn-practice{
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    color:@ws-white;
    font-size: 16px;
    font-family: @ws-header-font;
    font-weight: normal;
    border:none;
    width: 100%;
  }
}
.categories-explore{
  background: #fafafa;
  min-height: 500px;
  h2{
    font-family: @ws-banner-font;
    font-weight: bold;
  }
  .tabHead{
    .nav{
      display: block;
      @media @iPad-portrait,@iPhone{
        display: flex;
      }
      @media @iPhone{
        overflow-x: auto;
        flex-wrap:nowrap ;
      }
    }
    .nav-pills{
      border-right: 1px solid fade(@ws-darkBlack,30%);
      line-height: 3;
      @media @iPad-portrait,@iPhone{
        border-right: none;
      }
    }
    .nav-link{
      display: flex;
      align-items: center;
      text-transform: uppercase;
      font-family: @ws-header-font;
      color:@ws-darkBlack;
      font-size: 16px;
      white-space: nowrap;
      span{
        margin-left:15px;
      }
    }
  }
  .nav-pills .nav-link.active, .nav-pills .show>.nav-link{
    background: none;
    color:@ws-lightOrange;
    i{
      color:@ws-lightOrange;
    }
  }
  .tab-content{
    .nav{
      .nav-link{
        font-size: 16px;
        color:@ws-darkBlack;
      }
    }
  }
}
.abt-ws{
  min-height: 400px;
  position: relative;
  padding-bottom: 4rem;
  h2{
    font-family: @ws-banner-font;
    font-weight: bold;
    position: relative;
  }
  .patterns{
    background: url("../../images/landingpageImages/pattern.svg") no-repeat right center;
    background-size:contain;
    position: absolute;
    right: 0;
    top:0;
    height: 250px;
    width: 400px;
  }
  .ws-circle{
    width: 150px;
    height: 150px;
    border-radius: 75px;
    background-color: #2ebac6;
    text-align: center;
    margin: 0 auto;
    img{
      margin-top: 2.5rem;
      width: 80px;
    }
  }
  h3{
    text-align: center;
    font-size: 24px;
    font-family: @ws-header-font;
  }
  p{
    text-align: center;
    font-size: 14px;
    font-family: @ws-header-font;
    color:#707070
  }
}
.app-link {
  background: #fdfbf9;
  min-height: 400px;
 padding-bottom: 2rem;
  form {
    input {
      border: none;
      background: #fff;
      border-radius: 8px;
      border-top-right-radius: 0px;
      border-bottom-right-radius: 0px;
      padding: 1rem;
      outline: 0;
      width: 350px;
      @media @iPhone{
        width: 200px;
      }
    }

    button {
      background: #42cea3;
      padding: 1rem 2rem;
      color: #fff;
      outline: 0;
      border: none;
      font-size: 16px;
      font-family: @ws-header-font;
      margin-left: -10px;
      text-transform: uppercase;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
      @media @iPhone{
        font-size: 14px;
      }
    }
  }
  p.or {
    width: 40%;
    text-align: center;
    border-bottom: 1px solid fade(@ws-darkBlack, 30%);
    line-height: 0.1em;

    font-family: @ws-header-font;
    font-weight: normal;
    margin-left:3rem;
    span {
      background: #fdfbf9;
      padding: 0 10px;
      color:fade(@ws-darkBlack,30%);
    }
  }
  .playstore{
    width: 150px;
  }
  .appstore{
    width: 135px;
    margin-left: 2rem;
  }
  h2{
    font-weight: bold;
    font-family: @ws-banner-font;
  }
  .mobileImg{
    img{
      height: 500px;
      float: right;
      position: relative;
      top: 2rem;
    }
  }
}
.publisher{
  min-height: 100px;
  text-align: center;
  padding-top: 4rem;
  padding-bottom: 4rem;
  position: relative;
  background: url("../../images/landingpageImages/publisherBanner.png") no-repeat;
  &:before{
    position: absolute;
    content:" ";
    top:0;
    left:0;
    width:100%;
    height:100%;
    display: block;
    z-index:0;
    background-color: rgba(0,0,0,0.5);
  }
  .container{
    position: relative;
  }
  h2{
    font-size: 24px;
    font-family: @ws-banner-font;
    font-weight: bold;
    color:@ws-white;
  }
  p{
    text-transform: uppercase;
    font-family: @ws-header-font;
    font-size: 16px;
    color:@ws-white;
  }
  a{
    font-family: @ws-header-font;
    font-size: 14px;
    font-weight: normal;
    padding: 0.5rem 2rem;
    background: #42cea3;
    color:@ws-white;
    &:focus,&:hover{
      background: #42cea3 !important;
    }
  }

}
html{
  overflow-x: hidden;
}
.slider-container {
  .slick-slider {
    img {
      border-radius: 4px;
      @media @iPhone {
        margin: 0 auto;
      }
    }
  }
}