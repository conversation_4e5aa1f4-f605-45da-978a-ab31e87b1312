@import "../ws_color_theme.less";
@import "../ws_fonts.less";
@import "../responsive.less";

.store {
  padding-top: 2rem;
  min-height: 75vh;
  @media @iPhone{
    padding-top: 2rem;
    min-height: auto;
  }
  .search {
    width: 248px;
    height: 48px;
    background: @ws-white;
    padding-left: 40px;
    position: relative;
    @media @iPhone,@iPad-portrait,@iPad-landscape{
      width:100%;
    }
  }
  i.searchIcon {
    color: fade(@ws-darkBlack, 70%);
    position: absolute;
    top: 14px;
    left: 13px;
  }
  .bl-left {
    border-left: 1px solid @ws-border;
    min-height:70vh;
    @media @iPhone{
      min-height: 100%;
    }
  }
  .tab-menu {
    .nav-pills {
      padding: 1rem 0.5rem;
      border-bottom: 2px solid @ws-border;
      @media @iPhone{
        border-bottom: 1px solid @ws-border;
      }
      .nav-link {
        color: @ws-darkBlack;
        text-align: center;
        padding: 0;
        background: none !important;
        &:active, &:focus {
          background: none !important;
        }
        i{
          color:fade(@ws-darkBlack,80%);
        }
        span {
          display: block;
          color: @ws-darkBlack;
          font-family: @ws-header-font;
          font-size: @ws-menu-fontSize;
          width: 64px;
          min-height: 34px;
        }
        &.active {
          color: @ws-lightOrange;
          background: none !important;
          i{
            color:@ws-lightOrange;
          }
          span {
            font-weight: @ws-header-fontWeight;
            color: @ws-lightOrange;
          }

        }
      }
      &.show {
        > .nav-link {
          color: @ws-lightOrange;
          background: none;
        }
      }
    }
  }

  .ai-generate {
    .card {
      border-bottom: none;
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      .card-body {
        padding: 10px;
        text-align: center;
      }
    }
    .btn-start {
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      border-color: transparent;
      background: linear-gradient(270deg, @ws-gradient-start 0%, @ws-gradient-end 100%);
      color: @ws-white;
      text-transform: uppercase;
      &:focus {
        outline: none !important;
        border: none;
      }
      &:active {
        outline: none !important;
        border: none;
      }
    }
  }
}
.filter,.mobile-filter {
  form {
    label {
      font-family: @ws-header-font;
      font-size: @ws-menu-fontSize - 2;
      color: fade(@ws-darkBlack, 40%);
      margin-top: 1rem;
      padding: 0 2px;
    }

    .class-selection-btn{
      &.disabled{
        color:@ws-darkBlack;
      }
      background: none;
      border-bottom: 1px solid fade(@ws-caret,12%);
      padding: 8px 2px;
      width:100%;
      text-align: left;
      border-radius: 0px;
      &:after{
        float: right;
        position: relative;
        top: 9px;
        right:7px;
        border: 6px solid transparent;
        border-color: rgba(1, 1, 1, 0.5) transparent transparent transparent;
      }
    }
    >div{
      &.show{
        position: relative;
        #class-selection-btn{
          &:after{
            top: 0;
            border-color: transparent transparent rgba(1, 1, 1, 0.5) transparent;
          }
        }

        ul{
          position: absolute;
          top:125% !important;
          left: 0;
          right: 0;
          z-index: 99;
          transform: none !important;
          box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
          border-radius: 6px;
          width: 100%;
          max-height: 200px;
          overflow: auto;
          border:none;
          background: @ws-white;
          li{
            padding:0;
            cursor: pointer;
            user-select: none;
            a{
              padding: 10px 20px;
              width: 100%;
              display: flex;
              color:@ws-darkBlack;
              font-size: @ws-menu-fontSize;
            }
            &:first-child{
              border-top-right-radius: 6px;
              border-top-left-radius: 6px;
            }
            &:last-child{
              border-bottom-right-radius: 6px;
              border-bottom-left-radius: 6px;
            }
            &:hover{
              background: @ws-border;
              border-radius: 0;
              a{
                text-decoration: none;
              }
            }
          }
        }
      }
    }
  }
}
.mobile-filter{
  width:100%;
  .mob-sort{
    >button{
      position: fixed;
      top: 80%;
      z-index: 999;
      left: 37%;
      background: @ws-lightOrange;
      border: none;
      outline: none;
      border-radius: 35px;
      width: 95px;
      height:30px;
      color: @ws-white;
      justify-content: center;
      box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
      //-webkit-transform: translate3d(0, 0, 0);
      //transform : translate3d(0, 0, 0);
    }
  }

}
#filter-mode{
  z-index: 9999;
  .modal-dialog{
    margin:0;
    .modal-content{
      height:100vh;
      border-radius: 0;
      border:none;
      .modal-body{
        form{
          > div{
            button{
              width: 100%;
              text-align: left;
              &::after{
                float: right;
                margin-top: 8px;
              }
            }
          }
        }
      }
      .reset{
        background: none;
        border:none;

      }
    }
  }
}
body.modal-open {
  overflow: hidden;
}
.eutkarsh{
  &.custom-fix{
    .store{
      div{
        >.quick-sortmenu{
          margin-top: 4rem;
        }
      }
    }
  }
}