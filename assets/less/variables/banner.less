@import "ws_color_theme.less";
@import "ws_fonts.less";
@import "responsive.less";

@form-textWidth:400px;
@form-textHeight:48px;

.banner {
  position: relative;
  padding-bottom: 5rem;
  padding-top: 4rem;
  @media @iphoneX-portrait,@iPhone6-portrait,@iPhone7-portrait,@iPhone,@iPhone5-portrait,@iphoneX-landscape,@iPhone7-landscape,@iPhone6-landscape,@iPhone5-landscape {
    padding-top: 0rem;
    margin-top: -2rem;
  }
  .banner-image {
    background: url('../../images/landingpageImages/banner.svg') no-repeat right center;
    width: 100%;
    height: 100%;
    background-size: contain;
    position: absolute;
    right: -15rem;
    top: 10px;
    @media @iPad-portrait{
      width: 400px;
      height: 400px;
      right:-12rem;
    }
    @media @iPhone {
      width: 100%;
      height: 100px;
    }
    @media @iPhone6-landscape,@iPhone7-landscape {
      width: 200px;
      height: 200px;
    }
  }
  .container {
    position: relative;
    top: 0;
    //z-index: 99;
  }
  .jumbotron {
    background-color: transparent;
    h1 {
      font-size: @ws-banner-fontSize;
      font-family: @ws-banner-font;
      text-align: center;
      color: @ws-darkBlack;
      font-weight: bold;
      @media @iPhone {
        font-size: @ws-banner-fontSize - 8;
        margin-top: 4rem;
        text-align: left;
      }
    }
    @media @iPhone {
      margin-bottom: 0;
    }
  }
  .form-container {
    input[type="text"] {
      width: @form-textWidth + @form-textHeight;
      height: @form-textHeight;
      padding-left: 10px;
      padding-right: 10px;
      border: none;
      background: @ws-white;
      box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.24), 0px 0px 2px rgba(0, 0, 0, 0.12);
      &:focus {
        outline: none;
      }
      @media @iPhone {
        width: @form-textWidth - 100;
      }
      @media @iPhone6-portrait {
        width: @form-textWidth - 50;
      }
    }
    button {
      width: @form-textHeight;
      height: @form-textHeight;
      background: @ws-lightOrange;
      border: none;
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
      margin-left: -48px;
      i {
        color: @ws-white;
        font-size: @ws-header-fontSize + 8;
      }
    }
  }
  .categories {
    .category-line {
      width: 160px;
      border: 1px solid @ws-border;
      background: @ws-border;
      position: relative;
      top: -20px;
    }
    h2 {
      color: fade(@ws-darkBlack, 70%);
      font-family: @ws-header-font;
      font-size: @ws-title-size;
      font-weight: @ws-header-fontWeight;
    }
    .category-content {
      margin-top: 2.5rem;
      > a {
        &.nav-link {
          padding-left: 1.5rem;
          padding-right: 1.5rem;
          text-align: center;
          display: flex;
          align-items: center;
          border-right:1px solid fade(@ws-darkBlack,20%);
          padding: 0 1rem;
          @media @iPhone{
            margin-top: 1rem;
            border: none;
            display: flex;
            justify-content: space-evenly;
            width: 60%;
          }
          i {
            color: fade(@ws-darkBlack, 80%);
            @media @iPhone{
              width: 20%;
            }
          }
          span {
            display: block;
            color: @ws-darkBlack;
            text-decoration: none;
            font-size: @ws-menu-fontSize;
            font-family: @ws-header-font;
            margin-left: 1rem;
            @media @iPhone {
              font-size: @ws-menu-fontSize - 2;
              width: 80%;
              white-space: nowrap;
            }
          }

        }
        &:hover{
          i,span{
            color:@ws-lightOrange;
          }
        }
        &:last-child{
          &.nav-link{
            border: none;
          }
        }
      }
    }
  }
  .visit-store{
    border:1px solid @ws-lightOrange;
    border-radius: 2px;
    padding: 1rem 2rem;
    color:@ws-lightOrange;
    font-size: 16px;
    font-family: @ws-header-font;
  }

}
.mt-8{
  margin-top: 4rem;
}
.eutkarsh{
  .customAppbg{
    background: url('../../images/eutkarsh/eutkarshBanner.jpeg') no-repeat center center;
    background-size: contain;
    margin-top: -1rem;
    border-radius: 4px;
  }
  #openApps{
    .logoWs{
      top: -40px;
    }
  }
}
.customAppbg{
  background: url('../../images/landingpageImages/customAppbg.jpeg') no-repeat center center;
  height: 200px;
  background-size:cover;
  background-position: 90% 65%;
  border-radius: 4px;
}
#openApps{
  .modal-body{
    padding: 0;
    text-align: center;
    h4{
      font-size: 16px;
      font-family: @ws-header-font;
      color:@ws-darkBlack;
    }
    p{
      font-size: 12px;
      font-family: @ws-banner-font;
      color:fade(@ws-darkBlack,80%);

    }
  }
  .get-app{
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    color:@ws-white;
  }
  .modal-footer{
    border: none;
    a{
      color:fade(@ws-darkBlack,80%);
      font-size: 12px;
    }
  }
  .logoWs{
    background:@ws-white;
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    top: -22px;
    border-radius: 4px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.25);
  }
}

.customChatWrapper{
  display: none;
  opacity: 0;
  position: fixed;
  bottom:4rem;
  right:1.5rem;
  z-index: 999;
  align-items: center;
  justify-content: center;
  > div{
    background: @ws-white;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.25);
    padding: 0.5rem 1rem;
    border-radius: 16px;
    margin-right: 0.8rem;
    h4{
      font-size: 12px;
      font-weight: bold;
      font-family: @ws-header-font;
    }
    p{
      font-size: 14px;
      padding: 0;
      margin: 0;
      color:@ws-darkBlack;
      font-family: @ws-header-font;
    }

  }
  .div-right{
    display: none;
    position:relative;
    background:@ws-white;
    border-radius:5px;
    &.arrow-up{
      &:before{
        top:15px;
      }
    }
    &.show{
      display: block;
      transition: all 5s;
    }
  }
  .div-right:before{
    content:"";
    position:absolute;
    width:0px;
    height:0px;
    border: 10px solid;
    border-color: transparent transparent transparent @ws-white;
    right:-20px;
    top:30px;
  }
  button{
    background: @ws-lightOrange;
    border:0;
    outline:0;
    height: 50px;
    width: 50px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 16px rgba(0, 0, 0, 0.25);
    i{
      font-size: 32px;
      color:@ws-white;
    }
  }
  &#mobileChat{
    position: static;
  }
}

footer {
  .mobile-menu {
    .nav-item {
      .nav-link {
        margin: 0 auto;
      }

      p {
        color: @ws-darkBlack;
        padding: 0;
        margin: 0;
      }
      .drift-open-chat{
        display: flex;align-items: center;
        justify-content: center;
        i{
          color:fade(@ws-darkBlack,70%);
          font-size: 28px;
        }
      }
    }
  }
}
