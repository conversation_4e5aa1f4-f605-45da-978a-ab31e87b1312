.manageExams {
  .btn {
    color: @ws-white;
    background-color: @ws-lightOrange;
    &:focus {
      background-color: @ws-lightOrange !important;
    }
    &:active {
      background-color: @ws-lightOrange !important;
    }
  }
  select {
    border: 1px solid #ced4da;
    font-family: @ws-header-font;
  }
  .form-control {
    border: 1px solid #ced4da;
  }
  a.btn.disabled {
    background-color: @ws-lightOrange;
    opacity: 0.5;
  }
  .col-form-label {
    line-height: 1;
    font-weight: 500;
  }
  .text-warning {
    color: @ws-lightOrange !important;
  }
  textarea {
    min-height: 120px;
    font-family: @ws-header-font;
  }
}