@import "../ws_color_theme.less";
@import "../ws_fonts.less";
@import "../responsive.less";
html{
  scroll-behavior: smooth;
}
.utkarsh{
  .logo{
    height: 60px;
    width: 75px;
  }
  .ws-header{
    background: @ws-lightOrange;
    .navbar-nav{
      .nav-link{
        color:@ws-white;
        display: flex;
        align-items: center;
        height: 64px;
        &.active{
          background: none;
          border-bottom:2px solid @ws-white ;
        }
      }

      &.right-menu{
        li{
            a{
              color:@ws-white;
            }

        }
      }
    }
  }
  .ws-menu-start{
    background: @ws-lightOrange;
  }
  .test-slider {
    height: 62px;
    align-items: center;
    display: flex;
    .item-wrapper {
      display: flex;
      width: 319px;
      border:0.5px solid #ededed;
      border-radius: 4px;
      justify-content: space-around;
      align-items: center;
       height:46px;
      margin-right: 2rem;
      .test-name{
        color:@ws-darkBlack;
        font-family: @ws-header-font;
        font-weight: @ws-header-fontWeight;
        font-size: @ws-header-fontSize - 2;
        margin: 0;
      }
      .test-duration{
        color:@ws-darkBlack;
        font-size: @ws-header-fontSize - 6;
        display: block;
      }
      div {
        .btn-register {
          background: linear-gradient(270deg, @ws-blue-start 0%, @ws-blude-end 100%);
          width: 130px;
          height: 35px;
          font-weight: normal;
          color:@ws-white;
          font-size: @ws-menu-fontSize;
          font-family: @ws-header-font;
        }
        .btn-free{
          background: linear-gradient(270deg, @ws-gradient-start 0%, @ws-gradient-end 100%);
          width: 130px;
          height: 35px;
          font-weight: normal;
          color:@ws-white;
          font-size: @ws-menu-fontSize;
          font-family: @ws-header-font;
        }
      }
    }
    .slick-next{
      height:62px;
      border-radius: 0px;
    }
  }
  &.header-shadow{
    .test-slider{
      display: none;
    }
    .ws-menu-start{
      position: fixed;
      width: 100%;
      z-index: 999;
      top:0;
    }
  }
}
.custom-fix{
  .store{
    padding-top: 14rem;
  }
}
.eutkarsh{
  #loginSignup .modal-body .alternatelogin{
    display: none;
  }
  .ws-header .navbar-nav .nav-item.active{
    background: none;
  }
  .loader.book{
    border: 4px solid fade(@ws-darkBlack,60%);
  }
  .loader.book .page{
    border: 4px solid fade(@ws-darkBlack,60%);
  }
  .book-details{
    margin-top: 0;
  }
  .store,.library{
    padding-top: 2rem;
  }
  .mobile-menu{
    .navbar-nav{
      .nav-item{
        .nav-link{
          &.store-btn{
            background: url('../../images/eutkarsh/utkarsh-inactive.svg') center center no-repeat;
          }
          &.active{
            &.store-btn{
              background: url('../../images/eutkarsh/utkarsh-active.svg') center center no-repeat;
            }
          }
        }
      }
    }
  }
  .user_profile{
    margin-top: 0;
  }

}
@media only screen and (max-device-width: 480px) and (min-device-width: 320px) {

  .eutkarsh {
    .store {
      padding-top: 4rem;
    }
  }
}
#addChapters #addedContents {
  text-align: center;
  td{
    &.d-flex {
      border: none;
    }
    .btn-primary{
      margin-right: 5px;
    }
  }
}
#content-data-books-CompetitiveExams,#content-data-books-School,#content-data-books-College{
  .topSchoolBooks{
    .uncover{
      height:165px;
    }
  }
}
