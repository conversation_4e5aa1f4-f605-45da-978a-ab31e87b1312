.flex_st {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.adminForm a{
    color: #F79420;
    padding: 10px;
    font-size: 18px;
}
.adminForm a:hover{
  color: #F79420;
}

  .adminForm .form-control {
    width: 33.33%;
  }
.adminForm .form-control.admin {
  width: 90%;
}


.dflex {
  .form-group {
    .form-control {
      width: 90%;
    }
  }
}
  textarea.form-control {
    height: auto;
  }
  .dflex .form-group {
    width: 33.33%;
  }
  .dflex {
    display: flex;
  }
#content-books{
    padding: 0px;
    .form-group {
      margin-bottom: 15px;
    }
}
.ad {
  #endDate {
    border: 1px solid black;
    margin-left: 20px;
  }
}

  ::-webkit-input-placeholder{
    font-size: 17px;
    color: grey;
  }
  #batchName{
    margin-left: 20px;
  }

#addChapters #addedContents {
  text-align: center;
  td{
    &.d-flex {
      border: none;
    }
    .btn-primary{
      margin-right: 5px;
    }
  }
}
