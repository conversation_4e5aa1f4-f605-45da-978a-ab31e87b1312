@import "color.less";
@import "fonts.less";
body {
  font-family: @ebooksAccessFont;
  margin: 0;
  padding: 0;
  font-size: 15px;
}
a:hover {
  cursor: pointer !important;
}
.btn {
  font-family: @ebooksAccessFont;
}
.btn-warning {
  background-color: @btn-Orange !important;
  border-color: @btn-Orange !important;
  color: white !important;
}
.btn-primary {
  background-color: @btn-Blue !important;
  border-color: @btn-Blue !important;
  color: white !important;
}
.btn-outline-primary {
  border-color: @btn-Blue !important;
  color: @btn-Blue !important;
  &:hover {
    background-color: transparent !important;
  }
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px white inset;
}
::-webkit-input-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: @ebooksAccessFont;
  font-size: 15px !important;
}
::-moz-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: @ebooksAccessFont;
  font-size: 15px !important;
}
:-ms-input-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: @ebooksAccessFont;
  font-size: 15px !important;
}
:-moz-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: @ebooksAccessFont;
  font-size: 15px !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  color: rgba(68, 68, 68, 0.3);
  font-family: @ebooksAccessFont;
  font-size: 15px !important;
}
input[type='text'] {
  font-family: @ebooksAccessFont;
}
