@import "_common";

.videoExplanation {
  .container-fluid {
    padding: 5rem;
    background-color: #F3F7FA;
  }

  .staticContainer {
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 2px 10px #e9eef5;
  }

  label {
    font-weight: 500;
    font-size: 12px;
    font-family: 'Quicksand', sans-serif !important;
  }

  input {
    border: 1px solid #B4CDDE;
  }

  textarea {
    height: 120px !important;
    resize: unset !important;
    border: 1px solid #B4CDDE;
  }

  .notes {
    padding: 0 !important;

  }

  .cke_chrome {
    border: 1px solid #B4CDDE !important;
  }

  .buttonsVideos {
    padding: 1.1em 3em 5px 16em;
    margin-top: 2rem;

    .btn {
      text-transform: uppercase;
      font-size: 13px;
      color: #007bff !important;
      border: 1px solid #007bff !important;
      background-color: @white !important;
    }

    .finishBtn {
      background: @green !important;
      box-shadow: 0 2px 4px #0000001a;
      border-color: @green !important;
      color: @white !important;
    }
  }

  .points {
    padding-top: 1rem;
  }

  #containerRow {
    padding: 3rem;
  }

  .pagenumber-green {
    height: 30px;
    width: 35px;
    padding: 4px;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    margin-bottom: 10px;
    border-width: 2px;
    font-weight: bold;
    border-color: @green;
    color: @green;
    background-color: white !important;
  }

  @media @extraSmallDevices, @smallDevices {
    .container-fluid {
      padding-left: 0 !important;
      padding-right: 0 !important;
      padding-top: 3rem;
    }

    .btn {
      margin-bottom: 10px;
      width: 100%;
    }

    .mobile_column_swap {
      flex-direction: column-reverse;
    }

  }
}