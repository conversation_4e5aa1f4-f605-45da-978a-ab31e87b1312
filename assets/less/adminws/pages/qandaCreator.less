@import "_common";

.qandaCreator {
  .container-fluid {
    background-color: #F3F7FA;

  }
#resourceName{
  font-size: 15px;
}
 #static-content{
   padding: 3rem !important;
   box-shadow: 0 2px 10px #e9eef5;

 }

  label {
    font-weight: 500;
    font-size: 12px;
    font-family: 'Quicksand', sans-serif !important;
  }

  .qanda{
    padding: 0.1em 3em 5px 1em;
  }
  .qanda1{
    padding: 0.1em 2em 1px 2em;
  }
  .qandaAlert{
    padding: 1rem;
  }

  .cke_chrome {
    border: 1px solid #B4CDDE !important;
  }

  .buttonsQandA {
    margin-top: 2rem;

    .btn {
      text-transform: uppercase;
      font-size: 13px;
      color: #007bff !important;
      border: 1px solid #007bff !important;
      background-color: @white !important;
    }

    .finishBtn {
      background: @green !important;
      box-shadow: 0 2px 4px #0000001a;
      border-color: @green !important;
      color: @white !important;
    }
  }


  .pagenumber-green {
    height: 30px;
    width: 35px;
    padding: 4px;
    float: left;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    margin-bottom: 10px;
    border-width: 2px;
    font-weight: bold;
    border-color: @green;
    color: @green;
    background-color: white !important;
  }
 #alertbox{
   width: 100%;
   margin-left: 12px;
 }
  @media @extraSmallDevices, @smallDevices {
    .container-fluid {
      padding-left: 0 !important;
      padding-right: 0 !important;
      padding-top: 3rem;
    }

    .btn {
      margin-bottom: 10px;
      width: 90%;
    }

    .mobile_column_swap {
      flex-direction: column-reverse;
    }

  }
}