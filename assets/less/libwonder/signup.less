@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";

#loginOpen,#signup,#forgotPasswordmodal{
  -webkit-font-smoothing: antialiased;
  .modal-dialog {
    @media @extraSmallDevices, @smallDevices {
      align-items: flex-start;
      margin: 0;
      border-radius: 0;
      height: 100vh;
    }
  }
  .modal-header{
    display: flex;
    align-items: center;
    font-family: @primary-font;
    padding-bottom: 0;
  }
  @media (min-width: 576px) {
    .modal-dialog {
      //max-width: 375px;
    }
  }
  .head-title {
    color: @black;
    font-size: 24px;
    font-weight: @bold;
    line-height: normal;
    margin-bottom: 5px;
  }
  .modal-body {
    //align-items: center;
    background: lighten(@blue, 35%);
    @media @extraSmallDevices, @smallDevices, @mediumDevices {
      //align-items: unset;
      background-color: transparent;
    }
    .close {
      font-size: 18px;
      position: absolute;
      right: 15px;
      top: 15px;
      z-index: 2;
    }
    p {
      color:@black;
      font-size: 14px;
      font-family: @primary-font;
      line-height: normal;
    }
    input{
      &:focus{
        box-shadow: 0 0 5px 1px @gray-light-shadow;
        border-color: @theme-primary-color;
      }
      color:@black;
      border:1px solid lighten(@light-gray, 20%);
      border-radius: 7px;
      font-family: @primary-font;
      height: 45px;
      border-left-width: 3px;
      border-left-color: @theme-primary-color;
      &::placeholder{
        font-size: 12px;
      }
      &.input-error {
        border-color: @red;
      }
      &#password, &#signup-password {
        padding-right: 45px;
      }
    }
    .login-btn{
      background: @theme-primary-color;
      box-shadow: 0 0 7px @gray-light-shadow;
      border-radius: 5px;
      border: none;
      color:@white;
      padding: 0.5rem 1.5rem;
      font-weight: @medium;
      font-family: @primary-font;
      font-size: 16px;
      width: 100%;
    }
    .error-text {
      color: @red;
      margin-top: 2px;
    }
    .register-success {
      color: @green;
    }
    .logo {
      margin-bottom: 10px;
      display: none;
    }
  }
  .modal-footer{
    p{
      color:@black;
      font-family: @primary-font;

      a{
        color: @black;
        font-weight: @bold;
        font-family: @primary-font;
      }
    }
  }
  .modal-content{
    border-radius: 20px;
    overflow-x: hidden;
    flex-direction: unset;
    @media @extraSmallDevices, @smallDevices {
      height: 100%;
      border-radius: 0;
      flex-direction: unset;
    }
  }
}
.forgot{
  color:@theme-primary-color;
  cursor: pointer;
  font-size: 14px;
  font-weight: @bold;
  font-family: @primary-font;
  &:hover{
    color:@theme-primary-color;
  }
}
.error-msg{
  color:@red !important;
}
#loginOpen form, #signup form, #forgotPasswordmodal form {
  text-align: center;
}
#mobilemail-error{
  white-space: nowrap;
  display: block;
  margin-top: 10px;
  font-family: @primary-font;
}

// V.R
.login_signup_loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: none;
  width: 100%;
  z-index: 1;
  .progressbar {
    background-color: @theme-primary-color !important;
  }
  .bufferbar {
    background-image: linear-gradient(to right, rgba(255,255,255, 0.7), rgba(255,255,255, 0.7)), linear-gradient(to right, @theme-primary-color, @theme-primary-color) !important;
  }
  .auxbar {
    background-color: @theme-primary-color !important;
  }
}
.modal-text-content {
  flex-direction: column;
  background: lighten(@blue, 35%);
  @media @extraSmallDevices, @smallDevices {
    border-radius: 0 0 50px 50px;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px @gray-dark-shadow;
  }
  lottie-player {
    position: relative;
    width: 100%;
    height: 100%;
    top: 0;
    @media @extraSmallDevices, @smallDevices, @mediumDevices {
      display: none;
    }
  }
  h1 {
    font-weight: @regular;
    width: 100%;
    text-align: center;
    font-size: 2rem;
    span {
      font-weight: @bold;
      color: @theme-primary-color;
    }
  }
}
.modal-form-content {
  background-color: @white;
  @media @extraSmallDevices, @smallDevices, @mediumDevices {
    background-color: transparent;
  }
}
.hide-password {
  position: absolute;
  top: 12px;
  right: 12px;
  color: lighten(@light-gray, 15%);
  font-size: 22px;
  border-left: 1px solid lighten(@light-gray, 25%);
  padding-left: 7px;
  &:hover {
    color: lighten(@light-gray, 15%);
    text-decoration: none;
  }
}