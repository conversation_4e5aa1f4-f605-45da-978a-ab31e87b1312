@import "../variables/responsive.less";
.ramaiah-indexbg{
  background:#718FC9;
  height: 100vh;
  width:100vw;
  .bg-img1{
    background: url('../../images/ramaiah/bg-img.png') no-repeat;
    height: 200px;
    background-size: contain;
    width:100%;
    @media @iPhone{
      height: 200px;
    }
  }
}
.btn-wrapper{
  margin-top: 4rem;
  text-align: center;
  background: #E7D8E4;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  div{
    h4{
      font-size: 20px;
      color:#913030;
      text-transform: uppercase;
      margin-bottom: 1rem;
      font-weight: bold;
    }
  }
.r-primaryBg{
  background:#718FC9;
  color:#fff;
  text-transform: uppercase;
}
  .r-secondaryBg{
    background:#79538B;
    color:#fff;
    text-transform: uppercase;
  }
}
.ramiahHomepage{
  header{
    display: none;
  }
  footer{
    display: none;
  }
  .footer-nav{
    display: none;
  }
}

.bg-color-10{
  background: #151922;
  height: 100vh;
  position: relative;
  text-align: center;
  //display: -webkit-box;
  //display: -moz-box;
  //display: -ms-flexbox;
  //display: -webkit-flex;
  //display: flex;
  //justify-content: center;
  //align-items: center;
  padding: 10px 10px;
  padding-left: 25px;
  @media @iPhone{
    padding-left: 0;
  }
}

.bg-img{
  background-size: cover;
  top: 0;
  width: 100%;
  bottom: 0;
  min-height: 100vh;
  z-index: 999;
  background: rgba(0, 0, 0, 0.5) url('../../images/ramaiah/bg-image.jpg') top left repeat;
  background-size: cover;
  opacity: 1;
  position: relative;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
.info{
  color: #fff;
  margin: 0 100px;
  h1{
    margin-bottom: 25px;
    font-size: 60px;
    color: #fff;
  }
  p{
    color: #fff;
    font-weight: 600;
    font-size: 15px;
    line-height: 30px
  }
}

}
.link-btn {
  padding: 5px 20px;
  font-size: 13px;
  border: solid 2px #2a3148;
  background: #2a3148;
  margin-right: 5px;
  letter-spacing: 1px;
  border-radius: 3px;
  font-weight: 400;
  color: #d6d6d6;
  text-decoration: none;
  text-decoration: blink;
  font-weight: bold;
}
.btn-section{
  margin-top: 4rem;
}
.logotext{
  h1{
    color:#fff;
    font-size: 24px;

  }
  p{
    color:#fff;
    font-size: 12px;
  }
}
.footer-text{
  position: absolute;
  bottom: 20px;
  width: 100%;
  text-align: center;
  margin-left: -10px;
  p{
    color:fade(#fff,80%);
    padding: 0;
    a{
      color:#F79420;
    }
  }
}