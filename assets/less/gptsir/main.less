@import "./variables.less";

// Base Styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: @font-family !important;
  background: @bg-secondary;
  min-height: 100vh;
  color: @text-primary !important;
  line-height: @line-height-loose !important;
}

.container {
  max-width: @container-max-width !important;
  margin: 0 auto;
  padding: 0 @container-padding !important;
}

// Main Content Styles
main {
  padding-top: @spacing-7xl;
  text-align: center;
}

.hero {
  padding: 100px 0 80px;
  background: #fff;
  min-height: 80vh;
  display: flex;
  align-items: center;

  &-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    width: 100%;
  }

  &-image {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-img {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 20px;
  }

  &-text {
    text-align: left;
  }

  &-title {
    font-size: 54px;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 16px;
    color: #1e293b;

    .highlight {
      color: #ff6b35;
    }
  }
  &-subtitle-main {
    font-size: 36px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 24px;
    color: #1e293b;
  }

  &-description {
    font-size: 18px;
    color: #64748b;
    line-height: 1.6 !important;
    margin-bottom: 32px;
    max-width: 500px;
  }

  &-cta {
    margin-top: 8px;
  }

  &-subtitle {
    font-size: @font-size-lg;
    color: @text-secondary;
    max-width: 600px;
    margin: 0 auto @spacing-5xl;
    line-height: @line-height-loose;
  }
}

.cta-button {
  background-color: @primary-color;
  color: white;
  padding: @font-size-base @spacing-3xl;
  border-radius: @border-radius-md;
  text-decoration: none;
  font-weight: @font-weight-semibold !important;
  font-size: @font-size-md;
  display: inline-block;
  transition: background-color @transition-fast;
  margin-bottom: @spacing-sm;
  box-shadow: @shadow-primary;

  &:hover {
    background-color: @primary-hover !important;
    transform: translateY(-2px);
    box-shadow: @shadow-primary-lg;
  }
}

.cta-note {
  font-size: @font-size-xs;
  color: @text-muted;
  margin-top: @spacing-sm;
}

// Section Background Patterns
.section-bg-pattern-1 {
  background: @bg-secondary;
  position: relative;

  > .container {
    position: relative;
    z-index: 1;
  }
}

.section-bg-pattern-2 {
  background: @bg-primary;
  position: relative;

  > .container {
    position: relative;
    z-index: 1;
  }
}

// Mind Map Section Styles
.mindmap-section {
  padding: @spacing-8xl 0;
  position: relative;
  overflow: hidden;
  align-items: center;
  gap: @spacing-7xl;
}

.mindmap-content {
  flex: 1;

  .section-title {
    text-align: center;
  }
}

.section-title {
  font-size: @font-size-5xl;
  font-weight: @font-weight-bold;
  text-align: left;
  margin-bottom: @spacing-lg;
  color: @text-primary;
  position: relative;
  z-index: 1;

  .highlight {
    color: @primary-color;
  }
}

.section-subtitle {
  font-size: @font-size-lg;
  color: @text-secondary;
  text-align: left;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
}

.mindmap-container {
  position: relative;
  height: 700px;
  flex-shrink: 0;
  z-index: 1;
}

.features-rotating-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  height: 600px;
  transition: transform 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.central-node {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background: @bg-primary;
  border-radius: @border-radius-full;
  box-shadow: @shadow-xl;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  animation: pulse 3s infinite;
}

.central-content {
  text-align: center;
  padding: @spacing-xl;
}

.central-icon {
  font-size: @spacing-5xl;
  margin-bottom: 10px;
}

.central-node h3 {
  font-size: @font-size-md;
  font-weight: @font-weight-semibold;
  color: @text-primary;
  margin: 0;
}

.feature-node {
  position: absolute;
  width: 280px;
  background: @bg-primary;
  border-radius: @border-radius-lg;
  box-shadow: @shadow-lg;
  padding: @spacing-xl;
  transition: all 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(@spacing-xl);

  &.active {
    opacity: 1;
    transform: translateY(0);
  }
}

.node-content {
  position: relative;
  z-index: 2;
}

.node-icon {
  font-size: @spacing-2xl;
  margin-bottom: @spacing-md;
}

.feature-node h4 {
  font-size: @font-size-lg;
  font-weight: @font-weight-semibold;
  color: @text-primary;
  margin: 0 0 @spacing-sm 0;
}

.feature-node p {
  font-size: @font-size-base;
  color: @text-secondary;
  margin: 0;
  line-height: @line-height-relaxed;
}

.node-connector {
  display: none; /* Hide connecting lines for clean design */
}

// Feature positioning
.feature-1 {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%) translateY(@spacing-xl);
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

.feature-2 {
  position: absolute;
  top: 50%;
  right: -150px;
  transform: translateY(-50%) translateX(@spacing-xl);
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    transform: translateY(-50%) translateX(0);
    opacity: 1;
  }
}

.feature-3 {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%) translateY(-@spacing-xl);
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

.feature-4 {
  position: absolute;
  top: 50%;
  left: -150px;
  transform: translateY(-50%) translateX(-@spacing-xl);
  opacity: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    transform: translateY(-50%) translateX(0);
    opacity: 1;
  }
}

// Keyframes
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(255, 107, 53, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
  }
}

// Rotation states for step-based animation - clockwise rotation
.features-rotating-container {
  &.rotate-step-1 {
    transform: translate(-50%, -50%) rotate(90deg);

    .feature-4 { left: -90px; }
    .feature-3 { bottom: -100px; }
    .feature-1 { top: -110px; }
    .feature-2 { right: -100px; }

    .feature-1.active { transform: translateX(-50%) translateY(0) rotate(-90deg); }
    .feature-2.active { transform: translateY(-50%) translateX(0) rotate(-90deg); }
    .feature-3.active { transform: translateX(-50%) translateY(0) rotate(-90deg); }
    .feature-4.active { transform: translateY(-50%) translateX(0) rotate(-90deg); }
  }

  &.rotate-step-2 {
    transform: translate(-50%, -50%) rotate(180deg);

    .feature-1.active { transform: translateX(-50%) translateY(0) rotate(-180deg); }
    .feature-2.active { transform: translateY(-50%) translateX(0) rotate(-180deg); }
    .feature-3.active { transform: translateX(-50%) translateY(0) rotate(-180deg); }
    .feature-4.active { transform: translateY(-50%) translateX(0) rotate(-180deg); }
  }

  &.rotate-step-3 {
    transform: translate(-50%, -50%) rotate(270deg);

    .feature-4 { left: -100px; }
    .feature-3 { bottom: -100px; }
    .feature-1 { top: -100px; }
    .feature-2 { right: -90px; }

    .feature-1.active { transform: translateX(-50%) translateY(0) rotate(-270deg); }
    .feature-2.active { transform: translateY(-50%) translateX(0) rotate(-270deg); }
    .feature-3.active { transform: translateX(-50%) translateY(0) rotate(-270deg); }
    .feature-4.active { transform: translateY(-50%) translateX(0) rotate(-270deg); }
  }

  &.rotate-step-4 {
    transform: translate(-50%, -50%) rotate(360deg);

    .feature-1.active { transform: translateX(-50%) translateY(0) rotate(0deg); }
    .feature-2.active { transform: translateY(-50%) translateX(0) rotate(0deg); }
    .feature-3.active { transform: translateX(-50%) translateY(0) rotate(0deg); }
    .feature-4.active { transform: translateY(-50%) translateX(0) rotate(360deg); }
  }
}

// AI Features Comparison Section
.ai-features-section {
  padding: @spacing-8xl 0;

  .section-title {
    text-align: center;
  }
}

.ai-features-container {
  display: flex;
  align-items: flex-start;
  gap: @spacing-8xl;
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @container-padding;
}

.ai-features-content {
  flex: 1;
  max-width: 500px;

  .section-title {
    font-size: @font-size-5xl;
    font-weight: @font-weight-bold;
    text-align: left;
    margin-bottom: @spacing-lg;
    color: @text-primary;
  }

  .section-subtitle {
    font-size: @font-size-lg;
    color: @text-secondary;
    text-align: left;
  }
}

.learning-modes-section .section-title {
  text-align: center;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: @spacing-2xl;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: @spacing-lg;
  opacity: 0;
  transform: translateX(-30px);
  transition: all @transition-slower ease;

  &.active {
    opacity: 1;
    transform: translateX(0);
  }
}

.feature-icon {
  font-size: @font-size-xl;
  margin-top: 2px;
  flex-shrink: 0;
}

.feature-text {
  text-align: justify;

  h4 {
    font-size: @font-size-lg;
    font-weight: @font-weight-semibold;
    color: @text-primary;
    margin: 0 0 @spacing-xs 0;
  }

  p {
    font-size: @font-size-base;
    color: @text-secondary;
    margin: 0;
    line-height: @line-height-relaxed;
  }
}

// Comparison Table Styles
.comparison-table-container {
  flex: 1;
  max-width: 600px;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  background: @bg-primary;
  border-radius: @border-radius-lg;
  overflow: hidden;
  box-shadow: @shadow-lg;
  margin-bottom: @spacing-2xl;

  th {
    background: @bg-tertiary;
    padding: @spacing-lg @spacing-xl;
    font-weight: @font-weight-semibold;
    color: @text-primary;
    font-size: @font-size-base;
    border-bottom: 1px solid @border-color;

    &.gptsir-column {
      background: @primary-bg;
      color: @primary-color;
    }
  }

  td {
    padding: @spacing-lg @spacing-xl;
    border-bottom: 1px solid @border-light;
    font-size: @font-size-base;
  }
}

.table-row {
  opacity: 0;
  transform: translateY(@spacing-xl);
  transition: all @transition-slower cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    opacity: 1;
    transform: translateY(0);
  }

  &:hover {
    background-color: @bg-tertiary;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
}

.feature-name {
  font-weight: @font-weight-semibold;
  color: @text-primary;
}

.traditional-value {
  color: @text-secondary;
}

.gptsir-value {
  color: @text-primary;
  font-weight: @font-weight-medium;
  background: #fefefe;
}

.check {
  color: #10b981;
}

.cross {
  color: #ef4444;
}

.table-note {
  display: flex;
  align-items: flex-start;
  gap: @spacing-md;
  background: @primary-bg-light;
  padding: @spacing-xl;
  border-radius: @border-radius-lg;
  border-left: 4px solid @primary-color;
  opacity: 0;
  transform: translateY(@spacing-xl);
  transition: all @transition-slower ease;

  &.active {
    opacity: 1;
    transform: translateY(0);
  }

  p {
    margin: 0;
    font-size: @font-size-base;
    color: @text-primary;
    line-height: @line-height-relaxed;

    strong {
      color: @primary-color;
    }
  }
}

.note-icon {
  font-size: @font-size-xl;
  margin-top: 2px;
  flex-shrink: 0;
}

// Learning Modes Section Styles
.learning-modes-section {
  padding: @spacing-8xl 0;
  position: relative;
  overflow: hidden;
}

.learning-modes-header {
  text-align: center;
  margin-bottom: @spacing-7xl;

  .section-title {
    font-size: @font-size-5xl;
    font-weight: @font-weight-bold;
    color: @text-primary;
    line-height: @line-height-tight;
    margin: 0;
  }
}

.learning-modes-carousel {
  max-width: 1000px;
  margin: 0 auto;
  position: relative;
}

.carousel-container {
  position: relative;
  overflow: hidden;
  border-radius: @border-radius-3xl;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  background: @bg-primary;
}

.carousel-track {
  display: flex;
  transition: transform @transition-slower cubic-bezier(0.4, 0, 0.2, 1);
  width: 300%;
  will-change: transform;
}

.mode-card {
  width: 33.333%;
  flex-shrink: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: @spacing-5xl;
  padding: @spacing-7xl;
  min-height: 300px;
  background: @bg-primary;
  border: 2px solid @border-light;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, @primary-color, @primary-light);
    transform: scaleX(0);
    transition: transform @transition-slower ease;
  }

  &.active::before {
    transform: scaleX(1);
  }
}

.mode-card-content {
  position: relative;
  z-index: 2;
}

.mode-icon {
  font-size: 56px;
  margin-bottom: @spacing-xl;
  display: block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.mode-title {
  font-size: @font-size-4xl;
  font-weight: @font-weight-bold;
  color: @text-primary;
  margin-bottom: @spacing-lg;
  line-height: @line-height-tight;
}

.mode-description {
  font-size: @font-size-lg;
  color: @text-secondary;
  line-height: @line-height-loose;
  margin: 0;
}

.mode-image-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mode-image {
  width: 100%;
  max-width: 350px;
  height: 350px;
  object-fit: contain;
  border-radius: @border-radius-xl;
  transition: transform @transition-normal ease;
}

.mode-card.active .mode-image {
  transform: scale(1.02);
}

.carousel-dots {
  display: flex;
  justify-content: center;
  gap: @spacing-md;
  margin-top: @spacing-5xl;
}

.dot {
  width: @spacing-md;
  height: @spacing-md;
  border-radius: @border-radius-full;
  border: 1px solid #999;
  background: @text-light;
  cursor: pointer;
  transition: all @transition-normal ease;
  position: relative;

  &:hover {
    background: @text-muted;
    transform: scale(1.1);
  }

  &.active {
    background: @primary-color;
    transform: scale(1.2);

    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border: 2px solid rgba(255, 107, 53, 0.3);
      border-radius: @border-radius-full;
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

// Testimonials Section Styles
.testimonials-section {
  padding: @spacing-8xl 0;
  text-align: center;
}

.testimonials-container {
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @container-padding;
}

.testimonials-header {
  margin-bottom: @spacing-7xl;

  .section-title {
    font-size: @font-size-5xl;
    font-weight: @font-weight-bold;
    margin-bottom: @spacing-lg;
    color: @text-primary;
    text-align: center;
  }

  .section-subtitle {
    font-size: @font-size-lg;
    color: @text-secondary;
    margin: 0;
    text-align: center;
  }
}

.testimonials-scroll-container {
  overflow-x: auto;
  overflow-y: hidden;
  padding: @spacing-xl 0;
  margin: 0 -@container-padding;
  scrollbar-width: none;
  -ms-overflow-style: none;
  position: relative;
  --scroll-indicator-opacity: 0.8;

  &::-webkit-scrollbar {
    display: none;
  }
}

@keyframes pulseScroll {
  0%, 100% {
    opacity: 0.8;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
}

.testimonials-grid {
  display: flex;
  gap: @spacing-2xl;
  padding: 0 @container-padding;
  min-width: max-content;
}

.testimonial-card {
  background: @bg-primary;
  border-radius: @border-radius-2xl;
  padding: @spacing-4xl @spacing-3xl;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid @border-light;
  transition: all @transition-slow ease;
  opacity: 0;
  transform: translateY(30px);
  min-width: 320px;
  max-width: 320px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, @primary-color, @primary-light);
    transform: scaleX(0);
    transition: transform @transition-slow ease;
  }

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.03) 0%, transparent 70%);
    opacity: 0;
    transition: opacity @transition-slow ease;
    pointer-events: none;
  }

  &.active {
    opacity: 1;
    transform: translateY(0);
    animation: slideInUp @transition-slower ease forwards;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);

    &::before {
      transform: scaleX(1);
    }

    &::after {
      opacity: 1;
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.popular-plan {
  animation: float 6s ease-in-out infinite;
}

.testimonial-content {
  text-align: left;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.quote-icon {
  font-size: @spacing-6xl;
  color: @primary-color;
  font-family: Georgia, serif;
  line-height: 1;
  margin-bottom: @spacing-lg;
  opacity: 0.8;
}

.testimonial-text {
  font-size: @font-size-md;
  line-height: @line-height-loose;
  color: @text-primary;
  margin-bottom: @spacing-2xl;
  flex: 1;
  font-weight: @font-weight-medium;
}

.testimonial-author {
  display: flex;
  flex-direction: column;
  gap: @spacing-xs;
}

.author-name {
  font-size: @font-size-base;
  font-weight: @font-weight-semibold;
  color: @text-primary;
}

.author-location {
  font-size: @font-size-sm;
  color: @text-secondary;
  font-weight: @font-weight-normal;
}

// Pricing Section Styles
.pricing-section {
  padding: 100px 0;
  text-align: center;
  position: relative;
  display: none !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.02) 0%, transparent 50%);
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 25% 25%, @primary-color 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, @secondary-color 1px, transparent 1px);
    background-size: 100px 100px, 150px 150px;
    opacity: 0.1;
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
  }
}

.pricing-container {
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @container-padding;
  position: relative;
  z-index: 1;
}

.pricing-header {
  margin-bottom: @spacing-8xl;

  .section-title {
    font-size: @font-size-6xl;
    font-weight: @font-weight-bold;
    margin-bottom: @spacing-xl;
    color: @text-primary;
    text-align: center;
  }

  .section-subtitle {
    font-size: @font-size-xl;
    color: @text-secondary;
    margin: 0;
    text-align: center;
  }
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: @spacing-2xl;
  max-width: 900px;
  margin: 0 auto;
}

.pricing-card {
  background: @bg-primary;
  border-radius: @border-radius-2xl;
  padding: @spacing-3xl @spacing-xl;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 2px solid @border-light;
  transition: all @transition-slow ease;
  opacity: 0;
  transform: translateY(40px);
  position: relative;
  overflow: hidden;
  min-height: 480px;
  display: flex;
  flex-direction: column;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, @border-color, @text-light);
    transition: all @transition-slow ease;
  }

  &.active {
    opacity: 1;
    transform: translateY(0);
    animation: slideInUp 0.8s ease forwards;
  }

  &:hover {
    transform: translateY(-12px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: @primary-color;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 107, 53, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity @transition-slow ease;
    pointer-events: none;
    border-radius: @border-radius-3xl;
  }

  &:hover::after {
    opacity: 1;
  }
}

// Pricing Plan Specific Styles
.starter-plan {
  &::before {
    background: linear-gradient(90deg, #64748b, #475569);
  }

  &:hover::before {
    background: linear-gradient(90deg, @primary-color, @primary-light);
  }
}

.popular-plan {
  border-color: @primary-color;
  box-shadow: 0 12px 40px rgba(255, 107, 53, 0.15);
  transform: scale(1.02);

  &::before {
    background: linear-gradient(90deg, @primary-color, @primary-light);
  }

  &:hover {
    transform: scale(1.02) translateY(-12px);
    box-shadow: 0 24px 64px rgba(255, 107, 53, 0.25);
  }

  .plan-price {
    background: linear-gradient(135deg, @primary-bg-light 0%, #fed7aa 100%);
  }

  .amount {
    color: @primary-color;
  }

  .custom-price {
    .currency {
      color: @primary-color;
    }

    .amount {
      color: @primary-color;
    }
  }
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, @primary-color, @primary-light);
  color: white;
  padding: @spacing-sm @spacing-2xl;
  border-radius: @spacing-xl;
  font-size: @font-size-base;
  font-weight: @font-weight-semibold;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.5);
  }
}

.enterprise-plan {
  &::before {
    background: linear-gradient(90deg, @secondary-color, @secondary-light);
  }

  &:hover::before {
    background: linear-gradient(90deg, @secondary-hover, @secondary-color);
  }
}

.plan-header {
  text-align: center;
  margin-bottom: @spacing-2xl;
}

.plan-icon {
  font-size: @font-size-5xl;
  margin-bottom: @spacing-md;
  display: block;
}

.plan-name {
  font-size: @font-size-xl;
  font-weight: @font-weight-bold;
  color: @text-primary;
  margin-bottom: @spacing-sm;
}

.plan-description {
  font-size: @font-size-base;
  color: @text-secondary;
  margin: 0;
  font-weight: @font-weight-medium;
}

.plan-price {
  text-align: center;
  margin-bottom: @spacing-2xl;
  padding: @spacing-lg 0;
  border-radius: @border-radius-lg;
  background: linear-gradient(135deg, @bg-tertiary 0%, @border-light 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
}

.currency {
  font-size: @font-size-md;
  font-weight: @font-weight-semibold;
  color: @text-secondary;
  vertical-align: top;
}

.amount {
  font-size: @font-size-4xl;
  font-weight: @font-weight-extrabold;
  color: @text-primary;
  margin: 0 2px;
  line-height: 1;
}

.period {
  font-size: @font-size-base;
  color: @text-secondary;
  font-weight: @font-weight-medium;
}

.custom-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: @spacing-xs;

  .currency {
    font-size: @font-size-lg;
    font-weight: @font-weight-semibold;
    color: @text-secondary;
    margin: 0;
  }

  .amount {
    font-size: @font-size-3xl;
    font-weight: @font-weight-bold;
    color: @text-primary;
    margin: 0;
    line-height: 1;
  }
}

.plan-features {
  margin-bottom: @spacing-2xl;
  text-align: left;
  flex: 1;

  .feature-item {
    display: flex;
    align-items: center;
    gap: @spacing-sm;
    margin-bottom: @spacing-md;
    padding: @spacing-xs 0;
  }
}

.feature-icon {
  font-size: @font-size-base;
  color: #10b981;
  flex-shrink: 0;
  transition: transform @transition-normal ease;
}

.pricing-card:hover .feature-icon {
  transform: scale(1.2);
}

.feature-text {
  font-size: @font-size-base;
  color: @text-primary;
  font-weight: @font-weight-medium;
  line-height: @line-height-normal;
}

.plan-button {
  width: 100%;
  padding: @spacing-md @spacing-xl;
  border: none;
  border-radius: @border-radius-lg;
  font-size: @font-size-base;
  font-weight: @font-weight-semibold;
  cursor: pointer;
  transition: all @transition-normal ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: auto;
}

.starter-button {
  background: linear-gradient(135deg, #64748b, #475569);
  color: white;

  &:hover {
    background: linear-gradient(135deg, #475569, #334155);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(71, 85, 105, 0.3);
  }
}

.popular-button {
  background: linear-gradient(135deg, @primary-color, @primary-light);
  color: white;
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);

  &:hover {
    background: linear-gradient(135deg, #ff5722, @primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);
  }
}

.enterprise-button {
  background: linear-gradient(135deg, @secondary-color, @secondary-light);
  color: white;

  &:hover {
    background: linear-gradient(135deg, @secondary-hover, @secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(139, 92, 246, 0.3);
  }
}

// Categories Section Styles
.categories-section {
  padding: @spacing-8xl 0;
}

.categories-container {
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @container-padding;
}

.categories-header {
  text-align: center;
  margin-bottom: @spacing-7xl;

  .section-title {
    font-size: @font-size-6xl;
    font-weight: @font-weight-bold;
    margin-bottom: @spacing-lg;
    color: @text-primary;
    text-align: center;
  }

  .section-subtitle {
    font-size: @font-size-lg;
    color: @text-secondary;
    margin: 0;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  }
}

.category-group {
  margin-bottom: 50px;

  &:last-child {
    margin-bottom: 0;
  }
}

.category-title {
  font-size: @font-size-3xl;
  font-weight: @font-weight-bold;
  color: @text-primary;
  margin-bottom: @spacing-2xl;
  text-align: left;
  position: relative;
  padding-bottom: @spacing-sm;
  display: flex;
  justify-content: flex-start;
  text-decoration: none;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, @primary-color, @primary-light);
    border-radius: 2px;
  }
}

.category-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: @spacing-xl;
  margin-bottom: @spacing-xl;
  max-width: 900px;
}

.category-card {
  background: @bg-primary;
  border-radius: @border-radius-xl;
  padding: @spacing-2xl @spacing-xl;
  box-shadow: @shadow-md;
  border: 2px solid @border-light;
  transition: all @transition-normal ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  display: block;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, @border-color, @text-light);
    transition: all @transition-normal ease;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
    border-color: @primary-color;
    text-decoration: none;
    color: inherit;

    &::before {
      background: linear-gradient(90deg, @primary-color, @primary-light);
    }
  }
}

.category-card-title {
  font-size: @font-size-lg;
  font-weight: @font-weight-semibold;
  color: @text-primary;
  margin: 0;
  text-align: center;
  line-height: @line-height-normal;
}

.explore-more {
  text-align: right;
  margin-top: @spacing-lg;
}

.explore-more-link {
  color: @primary-color;
  text-decoration: none;
  font-size: @font-size-base;
  font-weight: @font-weight-semibold;
  display: inline-flex;
  align-items: center;
  gap: @spacing-xs;
  transition: all @transition-normal ease;

  &:hover {
    color: @primary-hover;
    text-decoration: none;
    transform: translateX(4px);
  }

  &::after {
    content: '→';
    font-size: @font-size-md;
    transition: transform @transition-normal ease;
  }

  &:hover::after {
    transform: translateX(2px);
  }
}

// FAQ Section Styles
.faq-section {
  padding: @spacing-8xl 0;
  text-align: center;
}

.faq-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 @container-padding;
}

.faq-header {
  margin-bottom: @spacing-7xl;

  .section-title {
    font-size: @font-size-5xl;
    font-weight: @font-weight-bold;
    margin-bottom: @spacing-lg;
    color: @text-primary;
    text-align: center;
  }

  .section-subtitle {
    font-size: @font-size-lg;
    color: @text-secondary;
    margin: 0;
    text-align: center;
  }
}

.faq-accordion {
  display: flex;
  flex-direction: column;
  gap: @spacing-lg;
}

.faq-item {
  background: @bg-primary;
  border-radius: @border-radius-xl;
  box-shadow: @shadow-md;
  overflow: hidden;
  transition: all @transition-normal ease;
  opacity: 0;
  transform: translateY(@spacing-xl);
  border: 1px solid @border-light;

  &.active {
    opacity: 1;
    transform: translateY(0);
    animation: fadeInUp @transition-slower ease forwards;
  }

  &:hover {
    box-shadow: @shadow-lg;
    transform: translateY(-2px);
  }

  &.expanded {
    border-color: @primary-color;
    box-shadow: 0 8px 24px rgba(255, 107, 53, 0.15);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.faq-question {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: @spacing-2xl @spacing-3xl;
  cursor: pointer;
  transition: all @transition-normal ease;
  background: @bg-primary;

  &:hover {
    background: #fafbfc;
  }

  h3 {
    font-size: @font-size-lg;
    font-weight: @font-weight-semibold;
    color: @text-primary;
    margin: 0;
    text-align: left;
    flex: 1;
    padding-right: @spacing-xl;
  }
}

.faq-item.expanded .faq-question {
  background: @primary-bg-light;
  border-bottom: 1px solid #fed7aa;
}

.faq-icon {
  position: relative;
  width: @spacing-4xl;
  height: @spacing-4xl;
  background: @border-light;
  border-radius: @border-radius-full;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all @transition-normal ease;
  flex-shrink: 0;

  .plus,
  .minus {
    position: absolute;
    font-size: @font-size-xl;
    font-weight: @font-weight-normal;
    color: @text-secondary;
    transition: all @transition-normal ease;
  }

  .minus {
    opacity: 0;
    transform: rotate(90deg);
  }
}

.faq-item.expanded .faq-icon {
  background: @primary-color;
  transform: rotate(180deg);

  .plus,
  .minus {
    color: @bg-primary;
  }

  .plus {
    opacity: 0;
    transform: rotate(90deg);
  }

  .minus {
    opacity: 1;
    transform: rotate(0deg);
  }
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: all @transition-slow cubic-bezier(0.4, 0, 0.2, 1);
  background: @bg-primary;

  p {
    padding: 0 @spacing-3xl @spacing-2xl @spacing-3xl;
    margin: 0;
    font-size: @font-size-md;
    line-height: @line-height-loose;
    color: @text-secondary;
    text-align: left;
  }
}

.faq-item.expanded .faq-answer {
  max-height: 200px;
  background: #fffbf7;
}

// Pre-footer Section Styles
.pre-footer-section {
  padding: @spacing-7xl 0;
  background: linear-gradient(135deg, @primary-bg-light 0%, #fed7aa 100%);
  border-top: 1px solid #fed7aa;
}

.pre-footer-container {
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @container-padding;
}

.pre-footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: @spacing-5xl;
  padding: @spacing-5xl;
  background: @bg-primary;
  border-radius: @border-radius-2xl;
  box-shadow: 0 12px 40px rgba(255, 107, 53, 0.1);
  border-left: 6px solid @primary-color;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.02) 0%, rgba(255, 140, 66, 0.02) 100%);
    pointer-events: none;
  }
}

.pre-footer-icon {
  font-size: @spacing-6xl;
  flex-shrink: 0;
  filter: drop-shadow(0 4px 8px rgba(255, 107, 53, 0.2));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.pre-footer-text {
  flex: 1;
  text-align: left;

  h2 {
    font-size: @font-size-4xl;
    font-weight: @font-weight-bold;
    color: @text-primary;
    margin-bottom: @spacing-md;
    line-height: @line-height-tight;
  }

  p {
    font-size: @font-size-lg;
    color: @text-secondary;
    margin: 0;
    line-height: @line-height-relaxed;
  }
}

.pre-footer-button {
  background: linear-gradient(135deg, @primary-color, @primary-light);
  color: white;
  padding: @spacing-lg @spacing-4xl;
  border-radius: @border-radius-lg;
  text-decoration: none;
  font-weight: @font-weight-semibold;
  font-size: @font-size-md;
  transition: all @transition-normal ease;
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    background: linear-gradient(135deg, @primary-hover, @primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);

    &::before {
      left: 100%;
    }
  }
}

// Footer Styles
.footer {
  background: @bg-tertiary;
  border-top: 1px solid @border-color;
  padding: @spacing-7xl 0 @spacing-5xl;
  margin-top: 0;
}

.footer-container {
  max-width: @container-max-width;
  margin: 0 auto;
  padding: 0 @container-padding;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: @spacing-7xl;
  align-items: start;
}

.footer-brand {
  max-width: 350px;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: @spacing-xl;

  img {
    height: 40px;
    margin-right: @spacing-md;
  }
}

.brand-name {
  font-size: @font-size-2xl;
  font-weight: @font-weight-bold;
  color: @text-primary;
}

.footer-description {
  font-size: @font-size-base;
  color: @text-secondary;
  line-height: @line-height-loose;
  margin-bottom: @spacing-2xl;
}

.footer-social {
  display: flex;
  gap: @spacing-md;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: @spacing-5xl;
  height: @spacing-5xl;
  background: @bg-primary;
  border: 1px solid @border-color;
  border-radius: @border-radius-md;
  color: @text-secondary;
  text-decoration: none;
  transition: all @transition-normal ease;

  &:hover {
    background: @primary-color;
    color: white;
    border-color: @primary-color;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
  }
}

.footer-section-title {
  font-size: @font-size-xs;
  font-weight: @font-weight-semibold;
  color: @text-primary;
  letter-spacing: 0.5px;
  margin-bottom: @spacing-xl;
  text-transform: uppercase;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    margin-bottom: @spacing-md;
  }

  a {
    color: @text-secondary;
    text-decoration: none;
    font-size: @font-size-base;
    font-weight: @font-weight-normal;
    transition: color @transition-normal ease;
    line-height: @line-height-relaxed;

    &:hover {
      color: @primary-color;
    }
  }
}

.footer-contact {
  display: flex;
  flex-direction: column;
  gap: @spacing-md;
}

.contact-link {
  display: flex;
  align-items: center;
  gap: @spacing-sm;
  color: @text-secondary;
  text-decoration: none;
  font-size: @font-size-base;
  font-weight: @font-weight-normal;
  transition: color @transition-normal ease;
  line-height: @line-height-relaxed;

  &:hover {
    color: @primary-color;
  }

  svg {
    flex-shrink: 0;
    opacity: 0.7;
  }
}

.user-avatar {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #ff6b35;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
  transition: all 0.3s ease;
}

.user-avatar img:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(255, 107, 53, 0.3);
}

// Responsive Design
@media (max-width: 1024px) {
  .mindmap-section {
    flex-direction: column;
    gap: @spacing-5xl;
    margin-bottom: @spacing-xl;
  }

  .mindmap-content {
    max-width: none;
    text-align: center;
  }

  .section-title {
    text-align: center;
  }

  .section-subtitle {
    text-align: center;
  }

  .mindmap-container {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: @spacing-xl;
  }

  .central-node {
    position: relative;
    top: 0;
    left: 0;
    transform: none;
    margin-bottom: @spacing-5xl;
    width: 150px;
    height: 150px;
    display: none;
  }

  .feature-node {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    width: 100%;
    max-width: 500px;
    transform: none !important;

    &.active {
      transform: none !important;
    }
  }

  .node-connector {
    display: none;
  }

  // AI Features Section Responsive
  .ai-features-container {
    flex-direction: column;
    gap: @spacing-5xl;
  }

  .ai-features-content {
    max-width: none;

    .section-title {
      text-align: center;
    }

    .section-subtitle {
      text-align: center;
    }
  }

  .comparison-table-container {
    max-width: none;
  }

  .comparison-table {
    font-size: @font-size-sm;

    th,
    td {
      padding: @spacing-md @spacing-lg;
    }
  }

  // FAQ Section Responsive
  .faq-section {
    padding: @spacing-7xl 0;
  }

  .faq-container {
    padding: 0 @spacing-lg;
  }

  .faq-header {
    margin-bottom: @spacing-5xl;

    .section-title {
      font-size: @font-size-3xl;
    }

    .section-subtitle {
      font-size: @font-size-md;
    }
  }

  .pre-footer-content {
    flex-direction: column;
    text-align: center;
    gap: 30px;
    padding: 30px;
  }

  .pre-footer-text h2 {
    font-size: @font-size-3xl;
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: @spacing-5xl;
  }

  .footer-brand {
    grid-column: 1 / -1;
    max-width: none;
    text-align: center;
    margin-bottom: @spacing-xl;
  }
}

// Tablet Styles
@media (max-width: 1024px) and (min-width: 769px) {
  .category-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 18px;
  }
}

@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hero {
    padding: 80px 0 60px;
    min-height: auto;
    &-content {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
    &-image {
      order: 2;
    }
    &-text {
      order: 1;
      text-align: center;
    }
    &-title {
      font-size: 34px !important;
      margin-bottom: 12px;
    }

    &-subtitle-main {
      font-size: 28px !important;
      margin-bottom: 20px;
    }

    &-description {
      font-size: 14px !important;
      padding: 0 20px;
      max-width: none;
      margin-bottom: 28px;
    }

    &-img {
      max-width: 350px;
    }
  }

  .container {
    padding: 0 @spacing-lg !important;
  }

  .section-title {
    font-size: @font-size-3xl;
  }

  // Learning Modes Mobile Styles
  .learning-modes-section {
    padding: @spacing-7xl 0;
  }

  .learning-modes-header {
    margin-bottom: @spacing-5xl;

    .section-title {
      font-size: @font-size-2xl;
      line-height: 1.3;
    }
  }

  .learning-modes-carousel {
    max-width: 100%;
    margin: 0;
  }

  .carousel-container {
    border-radius: @border-radius-xl;
    margin: 0 @spacing-lg;
  }

  .mode-card {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: @spacing-5xl 30px;
    min-height: auto;
    text-align: center;
  }

  .mode-icon {
    font-size: @spacing-6xl;
    margin-bottom: @spacing-lg;
  }

  .mode-title {
    font-size: @font-size-2xl;
    margin-bottom: @spacing-md;
  }

  .mode-description {
    font-size: @font-size-md;
    margin-bottom: @spacing-xl;
  }

  .mode-image {
    max-width: 280px;
    height: 200px;
    object-fit: contain;
  }

  .carousel-dots {
    margin-top: 30px;
    gap: 10px;
  }

  .dot {
    width: 10px;
    height: 10px;
  }

  // Categories Mobile Styles
  .categories-section {
    padding: @spacing-7xl 0;
  }

  .categories-container {
    padding: 0 @spacing-lg;
  }

  .categories-header .section-title {
    font-size: @font-size-3xl;
    line-height: 1.3;
    margin-bottom: @spacing-lg;
  }

  .categories-header .section-subtitle {
    font-size: @font-size-md;
  }

  .category-group {
    margin-bottom: @spacing-5xl;
  }

  .category-title {
    font-size: 22px;
    margin-bottom: @spacing-xl;
  }

  .category-cards {
    grid-template-columns: 1fr;
    gap: @spacing-lg;
    margin-bottom: @spacing-lg;
  }

  .category-card {
    padding: @spacing-xl @spacing-lg;
  }

  .category-card-title {
    font-size: @font-size-md;
  }

  // FAQ Mobile Styles
  .faq-section {
    padding: 50px 0;
  }

  .faq-header .section-title {
    font-size: @font-size-2xl;
    line-height: 1.3;
  }

  .faq-header .section-subtitle {
    font-size: 15px;
  }

  .faq-question {
    padding: @spacing-xl @spacing-2xl;

    h3 {
      font-size: @font-size-md;
      padding-right: @spacing-lg;
    }
  }

  .faq-icon {
    width: @spacing-3xl;
    height: @spacing-3xl;

    .plus,
    .minus {
      font-size: @font-size-lg;
    }
  }

  .faq-answer p {
    padding: 0 @spacing-2xl @spacing-xl @spacing-2xl;
    font-size: 15px;
  }

  .pre-footer-section {
    padding: @spacing-5xl 0;
    margin-top: @spacing-7xl;
  }

  .pre-footer-content {
    padding: @spacing-2xl;
    gap: @spacing-2xl;
  }

  .pre-footer-icon {
    font-size: @font-size-5xl;
  }

  .pre-footer-text h2 {
    font-size: @font-size-2xl;
    line-height: 1.3;
  }

  .pre-footer-text p {
    font-size: @font-size-md;
  }

  .pre-footer-button {
    padding: @font-size-base @spacing-2xl;
    font-size: 15px;
    width: 100%;
    text-align: center;
  }

  .footer {
    padding: @spacing-5xl 0 30px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-brand {
    margin-bottom: 0;
  }

  .footer-social {
    justify-content: center;
  }

  .footer-section-title {
    margin-bottom: @spacing-lg;
  }

  .footer-links li {
    margin-bottom: @spacing-sm;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
  }

  .user-avatar img {
    border-width: 2px;
  }
}
