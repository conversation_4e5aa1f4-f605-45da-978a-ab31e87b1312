@prep-primary: #0F0839;
@prep-secondary:#04001D;
@prep-white:#fff;
@prep-red:#E83500;

.prepjoy_cta {
  background: #000;
  position: relative;
  margin-top: 3rem;
  @media screen and (max-width: 767px){
    padding: 2.5rem 1.5rem 3rem;
  }
  .curved-bg {
    //background-image: url('../../../images/wonderslate/wave-bg.png');
    //background-size: cover;
    //background-repeat: no-repeat;
    //background-position: center center;
    //width: 100%;
    //height: 400px;
    //position: absolute;
    //opacity: 0.15;
    //top: 20px;
    //right: 0;
    //left:0;
  }
  .cta_inner {
    border: 1px solid @prep-red;
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 2px 4px #000;
  }
  .cta_info {
    @media screen and (max-width: 767px){
      width: 100%;
    }
    h3 {
      color: white;
      font-weight: 100;
      line-height: 35px;
      span {
        font-weight: 600;
      }
    }
    h4 {
      font-weight: normal;
      span {
        font-size: 15px;
        @media screen and (max-width: 767px){
          padding: 5px 0;
          display: inline-block;
        }
      }
      a {
        color: @prep-red !important;
        &:hover {
          opacity: 0.7;
        }
      }
    }
  }
  .cta_btn {
    @media screen and (max-width: 767px){
      width: 100%;
    }
    p {
      color: white;
    }
    a {
      padding: 10px 20px;
      font-size: 16px;
      font-weight: 600;
      color: white;
      background-color: @prep-red;
      border-color: @prep-red;
      border-radius: 7px;
      box-shadow: 0 2px 4px #444;
      -webkit-box-shadow: 0 2px 4px #444;
      -moz-box-shadow: 0 2px 4px #444;
      &:hover,&:active,&:focus,&:active:focus {
        background-color: @prep-red !important;
        border-color: @prep-red !important;
        box-shadow: none !important;
        outline: 0 !important;
      }
      @media screen and (max-width: 767px){
        margin-top: 10px !important;
        margin-bottom: 10px !important;
      }
    }
  }
}

.prepjoy-footer{
  background: @prep-primary;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 999;
  &__socialIcons{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    ul{
      display: flex;
      justify-content: center;
      align-items: center;
      padding-left: 0;
      li{
        list-style: none;
        font-size: 1.4rem;
        margin-right: 20px;
        &:nth-child(4){
          margin-right: 0;
        }
        i{
          color: @prep-white;
        }
      }
    }
  }
}