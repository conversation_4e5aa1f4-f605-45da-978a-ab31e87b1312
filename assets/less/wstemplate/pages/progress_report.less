@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Progress Report Page Styles
.progress-report {

  .report-start {
    background: #FFF0CC;
    border-style: dashed;
    border-color: var(--warning);
    .card-title {
      font-weight: @regular;
      strong {
        color: @black;
        font-weight: @medium;
      }
    }
    .current-badge {
      a {
        cursor: pointer;
        i {
          position: relative;
          top: 5px;
          font-size: 20px;
        }
      }
    }
  }

  .report-filter {
    h6 {
      margin: 0;
    }
    .position-relative {
      background-color: @black;
      border-radius: 5px;
    }
    #dateRange {
      -moz-appearance: none;
      -webkit-appearance: none;
      appearance: none;
      border-radius: 5px;
      min-width: 120px;
      background-color: transparent;
      color: @white;
      position: relative;
      z-index: 2;
      padding-right: 25px;
      option {
        color: @black;
      }
      &:hover {
        cursor: pointer;
      }
      &:active, &:focus {
        outline: 0;
        box-shadow: none;
      }
    }
    i {
      position: absolute;
      top: 4px;
      right: 2px;
      font-size: 20px;
      color: @white;
      z-index: 1;
    }
  }

  .time-report {
    .card-body {
      h6 {
        font-weight: @regular;
      }
      h5 {
        margin: 0;
      }
    }
  }

  .subjects-report {
    h5 {
      margin: 0;
    }
    p {
      font-size: 13px;
    }
    table {
      thead {
        tr {
          th {
            &:first-child {
              min-width: 120px;
              @media @mediumDevices {
                min-width: 150px;
              }
              @media @largeDevices, @extraLargeDevices {
                min-width: 200px;
              }
            }
            &:last-child {
              min-width: 100px;
              @media @mediumDevices, @largeDevices, @extraLargeDevices {
                min-width: 125px;
              }
            }
          }
        }
      }
    }
  }

  .observation-report {
    table {
      thead {
        tr {
          th {
            min-width: 150px;
            @media @mediumDevices, @largeDevices, @extraLargeDevices {
              min-width: 200px;
            }
            &:first-child {
              min-width: 80px;
              @media @mediumDevices, @largeDevices, @extraLargeDevices {
                min-width: 150px;
              }
            }
          }
        }
      }
    }
  }

  // List - Common style
  ol {
    li {
      line-height: normal;
      padding-bottom: 0.5rem;
    }
  }

  // Table - Common style
  table {
    thead {
      tr {
        &.text-muted {
          color: #A1A5B7 !important;
        }
        th {
          border: none;
          padding: 1rem;
          &:first-child {
            border-radius: 5px 0 0 5px;
          }
          &:last-child {
            border-radius: 0 5px 5px 0;
          }
        }
      }
    }
    tbody {
      tr {
        border-bottom: 1px dashed #EEE;
        &:last-child {
          //border: none;
        }
        td {
          border: none;
          padding: 1rem;
          &:first-child {
            padding-left: 0;
          }
          a {
            &:hover, &:focus {
              color: var(--dark) !important;
              background-color: #FFF0CC !important;
            }
            &:active {
              transform: scale(0.97);
            }
          }
        }
      }
    }
  }

}

// Modal
#letsImprove {
  h6 {
    b {
      width: 120px;
      display: inline-block;
    }
  }
  a {
    font-weight: @medium;
    &:active {
      transform: scale(0.97);
    }
  }
}

// Chart Loader
.chart-loader {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  z-index: 1;
  width: 50px;
  height: 50px;
  border: 5px solid #F3F3F3;
  border-radius: 50%;
  border-top: 5px solid #3498DB;
  -webkit-animation: spinLoader 1s linear infinite;
  animation: spinLoader 1s linear infinite;
}
@-webkit-keyframes spinLoader {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}
@keyframes spinLoader {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}