@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Form Element Styles
.form-group-modifier {
  position: relative;
  margin-bottom: 0;
  &:after {
    background-color: @theme-primary-color;
    bottom: 0;
    content: '';
    height: 2px;
    left: 45%;
    position: absolute;
    transition-duration: 0.2s;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    visibility: hidden;
    width: 10px;
  }
  &.is-focused:after {
    left: 0;
    visibility: visible;
    width: 100%;
  }
  .form-control-modifier {
    border: none;
    padding: .375rem 0;
    border-radius: 0;
  }
}

.form-control-modifier {
  color: @black;
  //box-shadow: inset 0 0 3px @gray-light-shadow;
  &:focus {
    box-shadow: none;
    //border-color: @theme-primary-color;
  }
}


// ===> MDL Form Elements Styles
.mdl-textfield-modifier {
  padding-bottom: 0;
  .mdl-textfield__label {
    font-size: 12px;
    margin-bottom: 0;
    &:after {
      background-color: @theme-primary-color;
      bottom: 0;
    }
  }
  &.is-focused, &.is-dirty {
    .mdl-textfield__label {
      color: @theme-primary-color;
      margin-bottom: 0;
    }
  }
}

// ===> CK Editor Styles
#cke_1_contents {
  min-height: 300px;
}