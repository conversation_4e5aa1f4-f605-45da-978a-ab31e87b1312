@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Preloader Styles
//.loading-icon {
//  position: fixed;
//  width: 100%;
//  height: 100%;
//  top: 0;
//  background-color: @loader-color;
//  z-index: 9999;
//  overflow: hidden;
//}

//.loader-wrapper {
//  width: 139px;
//  height: 65px;
//  background-color: @white;
//  position: relative;
//  top: 50% !important;
//  -webkit-transform: translateY(-50%);
//  -moz-transform: translateY(-50%);
//  -ms-transform: translateY(-50%);
//  -o-transform: translateY(-50%);
//  transform: translateY(-50%);
//  box-shadow: 0 0 10px #00000025;
//  margin: 0 auto;
//  border-radius: 4px;
//}

//.loader,
//.loader:before,
//.loader:after {
//  border-radius: 50%;
//  width: 2.5em;
//  height: 2.5em;
//  -webkit-animation-fill-mode: both;
//  -moz-animation-fill-mode: both;
//  -o-animation-fill-mode: both;
//  animation-fill-mode: both;
//  -webkit-animation: load7 1.8s infinite ease-in-out;
//  -moz-animation: load7 1.8s infinite ease-in-out;
//  -o-animation: load7 1.8s infinite ease-in-out;
//  animation: load7 1.8s infinite ease-in-out;
//}
//
//.loader {
//  color: @theme-primary-color;
//  font-size: 9px;
//  margin: 0 auto;
//  position: relative;
//  text-indent: -9999em;
//  -webkit-transform: translateZ(0);
//  -moz-transform: translateZ(0);
//  -ms-transform: translateZ(0);
//  -o-transform: translateZ(0);
//  transform: translateZ(0);
//  -webkit-animation-delay: -0.16s;
//  -moz-animation-delay: -0.16s;
//  -o-animation-delay: -0.16s;
//  animation-delay: -0.16s;
//  &:before, &:after {
//    content: '';
//    position: absolute;
//    top: 0;
//  }
//  &:before {
//    left: -3.5em;
//    -webkit-animation-delay: -0.32s;
//    -moz-animation-delay: -0.32s;
//    -o-animation-delay: -0.32s;
//    animation-delay: -0.32s;
//  }
//  &:after {
//    left: 3.5em;
//  }
//}

// Loading animation
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}

// Pace loader style
.pace {
  -webkit-pointer-events: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.pace-inactive {
  display: none;
}
.pace .pace-progress {
  background: @theme-primary-color;
  position: fixed;
  z-index: 2000;
  top: 0;
  right: 100%;
  width: 100%;
  height: 3px;
}