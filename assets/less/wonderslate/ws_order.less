@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";

//Purchase order success page
.purchase-details-container {
  min-height: 600px;
  &.ws-orders {
    .purchase-heading {
      margin-top: 40px;
      @media (max-width: 991px) {
        margin-top: 30px;
      }
      h3 {
        font-weight: @semi-bold;
        font-size: 24px;
        color: @theme-primary-color;
      }
    }
    .purchase-details-wrapper {
      margin: 0 auto 40px;
      min-height: auto;
      .purchase-details, .browse-purchase-book {
        //width: 50%;
        @media (max-width: 991px) {
          width: 100%;
        }
      }
      .browse-purchase-book {
        .browse-wrapper {
          padding: 0 40px 0 0;
          @media (max-width: 991px) {
            padding: 0;
          }
        }
      }
      .learn-btn {
        display: inline-block;
        margin-top: 0;
      }
    }
  }
  .purchase-details-wrapper {
    margin: 40px auto;
    min-height: 400px;
    .purchase-heading {
      font-weight:@medium;
      font-size: 24px;
      background: @theme-primary-color;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .purchase-success-confirmation {
      font-size: 16px;
      margin-bottom: 7px;
      @media (max-width: 575px) {
        font-size: 15px;
        line-height: normal;
      }
      strong {
        font-weight: @semi-bold;
      }
    }
    .purchase-success-id {
      font-weight: @medium;
      font-size: 16px;
      letter-spacing: 0.01em;
      margin-bottom: 16px;
    }
    .purchased-book-container {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      justify-content: start;
      align-items: start;
      .purchased-book-wrapper {
        width: 100%;
        border-radius: 6px;
        //background-color: @white;
        //padding: 8px;
        //border: 1px solid lighten(@dark-gray,25%);
        .purchased-book-item {
          display: flex;
          margin-bottom: 15px;
          padding: 10px;
          background: @white;
          border-radius: 7px;
        }
        .purchased-book-img-wrapper {
          img {
            width: 75px;
            box-shadow: 0 0 14px @gray-light-shadow;
            border-radius: 4px;
          }
        }
        .purchased-book-info {
          vertical-align: top;
          padding: 0 15px 15px 15px;
          max-height: inherit;
          text-align: left;
          .purchased-book-name {
            font-style: normal;
            font-weight: @medium;
            line-height: 21px;
            font-size: 16px;
            letter-spacing: 0.01em;
            margin-bottom: 7px;
            @media (max-width: 767px) {
              font-size: 15px;
            }
          }
          .detail-book-author-name {
            text-align: left;
            margin-bottom: 7px;
            color: @gray;
          }
          .offer-price {
            display: inline-block;
            font-size: 20px;
            font-weight: @medium;
            color:@red;
            letter-spacing: 0.01em;
            margin-right: 4px;
            margin-bottom: 7px;
            i {
              font-size: 18px;
            }
          }
          .original-price {
            display: inline-block;
            font-size: 16px;
            font-weight:@light;
            color: @light-gray;
            letter-spacing: 0.01em;
            text-decoration: line-through;
            margin-bottom: 7px;
            span {
            }
          }
        }
      }
    }

    .purchase-details {
      float: left;
      //width: 40%;
      margin-left: 50px;
      @media screen and (max-width: 991px) {
        width: 100%;
        margin-left: 0;
      }
    }
    .browse-purchase-book {
      float: left;
      width: 60%;
      border-right: 1px solid lighten(@dark-gray,25%);
      @media screen and (max-width: 991px) {
        width: 100%;
        border: 0;
      }
      .browse-wrapper {
        padding: 24px 40px;
        margin: 0 auto;
        .continue-browse {
          display: block;
          font-style: normal;
          font-weight: @regular;
          line-height: normal;
          font-size: 14px;
          text-align: center;
          letter-spacing: 0.01em;
          color: @theme-primary-color;
          padding: 24px 0;
          margin-bottom: 24px;
          border-top: 1px solid lighten(@dark-gray,25%);
          border-bottom: 1px solid lighten(@dark-gray,25%);
        }
        .read-on-app {
          font-weight: @regular;
          line-height: normal;
          font-size: 12px;
          text-align: center;
          letter-spacing: -0.01em;
          color: lighten(@dark-gray,25%);
        }
        .download-app-btn {
          display: block;
          text-align: center;
          max-width: 122px;
          height: 40px;
          margin: 0 auto;
          .download-app-btn-img {
            width: 100%;
            height: auto;
            margin: 0 auto;
          }
        }
      }
    }

    .waves-effect {
      position: relative;
      cursor: pointer;
      display: inline-block;
      overflow: hidden;
      user-select: none;
      -webkit-tap-highlight-color: transparent;
      vertical-align: middle;
      z-index: 1;
      transition: .3s ease-out;
    }
    .learn-btn {
      font-size: 14px;
      display: block;
      text-align: center;
      font-weight: @medium;
      color: @white;
      background: @orange;
      letter-spacing: 0.01em;
      padding: 11px 25px;
      border-radius: 4px;
      margin-bottom: 24px;
      margin-top: -10px;
      &:hover {
        text-decoration: none;
        //color: @white;
        box-shadow: 0 2px 8px @gray-light-shadow;
      }
    }
    .instructions {
      h5 {
        font-weight: @semi-bold;
      }
      ol {
        li {
          padding-bottom: 7px;
          font-size: 15px;
          strong {
            font-weight:@semi-bold;
          }
        }
      }
    }
  }
}

#celebrationAnim{
  display:none;
  width: 100%;
  height:100%;
  position: absolute;
  top: -50px
}