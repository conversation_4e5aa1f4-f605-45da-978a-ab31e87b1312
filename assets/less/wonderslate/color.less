//header
@theme-gradient:radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
@yellow-gradient: rgba(255, 226, 162, 0.21);
@yellowtheme: rgba(255, 226, 162, 0.21);
@theme: #733EBA;
@white:#ffffff;
@modal-text:#A73397;
.text-gradient{
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
@theme1:#9A309B;
@theme-blue:#2F80ED;
@text-color:#949494;
@black:#212121;

//eBooks
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  //display: block !important;
}
@placeholder_color: #C13B87;
@icon_color: #98309C;
@border_color: #FFD602;
@dark-red: #CE0000;
@purple: #7F28A8;

//Book detail page
@price_bg: #27AE60;
@outline-btn: #9B51E0;

//Landing page
@text-darkcolor: #444444;
@btn-color: #2EBAC6;
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}

@cancel:rgb(220 53 69 / 0.8);

//To-do lists page
@filter_btn: #AE3691;
@priority_high: #B72319;
@priority_medium: #F79420;
@priority_low: #46B520;

//Dashboard page
@library-btn-bg: radial-gradient(99.37% 2306.89% at 2.52% 6.82%, #962F9D 0%, #C73D84 100%);
@todo-btn-bg: radial-gradient(165.91% 995.76% at 82.35% 156.82%, #219653 0%, #6FCF97 100%);
@tracker-btn-bg: radial-gradient(107.95% 1161.73% at 3.92% 7.95%, #FAC421 0%, #FC851B 100%);

//Nextexam page
@chapter-text-color: #AE3691;

@dark-yellow: #FFBF02;
