@import "../wstemplate/theme/responsive.less";

.listPriceText{
  text-decoration: line-through;
  color: #888;
}

.offerPriceText{
  font-weight: 500;
  margin-right: 6px;
  color: #444;
}
.offerPercentage{
  margin-left: 6px;
  color: red;
}
.popular_searches .content-wrapper p{
  font-size: 14px !important;
  -webkit-line-clamp:2 !important;
}
.price-sec span{
  font-size: 12px !important;
}
#relatedBooks > div{
  box-shadow: 0 3px 3px rgba(0,0,0,0.13);
  border: 1px solid rgba(0, 0, 0, 0.13);
  border-radius: 5px;
}
.popular_searches .img-wrapper{
  width: 100%;
  padding: 10px 5px 5px 5px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.price-sec{
  display: flex;
  flex-wrap: wrap;
}
#relatedBooks,
#bestSellerBooks{
  height:auto !important;
  gap: 18px;
  margin-left: 0!important;
  margin-right: 0!important;

  display: grid;
  grid-template-columns: repeat(7,1fr);
}
#bestSellerBooks{
  margin-left: 0!important;
  margin-right: 0!important;
}
.popular_searches .img-wrapper img{
  height: 190px !important;
  border-radius: 5px;
  object-fit: fill;
}
.popular_searches .uncover{
  height: 190px !important;
}
@media  @extraSmallDevices, @smallDevices{
  #relatedBooks,
  #bestSellerBooks{
    grid-template-columns: repeat(2,1fr);
    grid-gap: 10px !important;
  }
}
@media  @mediumDevices{
  #relatedBooks,
  #bestSellerBooks{
    grid-template-columns: repeat(4,1fr);
    grid-gap: 10px !important;
  }
}
@media (min-width: 991.98px) and (max-width:1440px ) {
  #relatedBooks,
  #bestSellerBooks{
    grid-template-columns: repeat(5,1fr);
    grid-gap: 10px !important;
  }
}
@media (min-width:980px)
{
  div#relatedBooks>div {
    padding:0px !important;
  }
}