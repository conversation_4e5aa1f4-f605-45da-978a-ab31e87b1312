@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";

.mobile-footer-resource{
  background:@theme-primary-color;
  box-shadow: 0 -4px 10px @gray-light-shadow;
  border-radius: 20px;
  height: 65px;
  position: fixed;
  width: 98%;
  bottom:5px;
  z-index: 9991;
  left: 0;
  right: 0;
  margin: 0 auto;
  button{
    background: none;
    border:none;
    &:focus {
      outline: 0;
    }
    i{
      color:@white;
    }
    span{
      display: block;
      color:@white;
      font-size: 10px;
      margin-top: 5px;
    }

  }
}
.footer-menu-popover{
  bottom:0;
  background-color: rgba(0, 0, 0, 0.85) !important;
  &.modal .modal-dialog {
    -webkit-transform: translate(0,0);
    transform: translate(0,0);
  }
  @media (min-width: 992px) {
    display: none !important;
  }
}
.top-drop-menu{
  >div{
    box-shadow: 0 0 10px @gray-light-shadow;
    border-radius: 20px;
    padding: 8px 15px !important;
    min-height: 40px;
    display: flex;
    align-items: center;
    min-width: 170px !important;
    i{
      font-size: 18px;
    }
  p{
    font-size: 12px;
  }
  }
}
.footer-menu-popover .modal-dialog{
  //bottom:0;
}
div#addVideo, div#addWeburl {
  z-index: 9992;
}
