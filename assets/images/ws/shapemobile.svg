<svg width="74" height="80" viewBox="0 0 74 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M58.5428 9.2625C53.6158 -1.40403 39.9556 -3.30383 28.7751 -8.16025C16.8154 -13.3551 5.36927 -24.2862 -6.99446 -19.2046C-19.8625 -13.9157 -22.7443 1.38084 -25.7876 14.2655C-28.9349 27.5911 -34.1746 43.5806 -24.0107 52.8402C-14.1478 61.8256 1.76591 52.8021 15.6828 52.0461C29.4827 51.2965 45.3244 58.1783 54.97 48.55C65.1648 38.3733 64.3123 21.7532 58.5428 9.2625Z" fill="url(#paint0_radial)"/>
</g>
<defs>
<filter id="filter0_d" x="-47.5842" y="-34.001" width="120.943" height="113.441" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(80.88 18.4781) rotate(173.234) scale(83.2114 73.8659)">
<stop stop-color="#8129A7"/>
<stop offset="1" stop-color="#CB3E82"/>
<stop offset="1" stop-color="#F5F5F5" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
