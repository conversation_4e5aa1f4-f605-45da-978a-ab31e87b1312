<svg width="1152" height="379" viewBox="0 0 1152 379" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0" mask-type="alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1152" height="379">
<rect width="1152" height="379" fill="#F79420"/>
</mask>
<g mask="url(#mask0)">
<rect x="-12" y="-59" width="1180" height="497" fill="#F79420"/>
<circle cx="289.5" cy="279.5" r="463.5" fill="url(#paint0_radial)" fill-opacity="0.21"/>
<circle r="51.5" transform="matrix(-1 0 0 1 1113.5 362.5)" fill="url(#paint1_radial)" fill-opacity="0.21"/>
<circle cx="1090" cy="24" r="98" fill="url(#paint2_radial)" fill-opacity="0.11"/>
</g>
<defs>
<radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1353.5 -184) rotate(154.9) scale(1766.85)">
<stop stop-color="#F05A2A"/>
<stop offset="1" stop-color="#F05A2A" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(169.722) rotate(154.9) scale(196.316)">
<stop stop-color="#F05A2A"/>
<stop offset="1" stop-color="#F05A2A" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1012.5 89) rotate(-29.6872) scale(184.75)">
<stop stop-color="#FFE4DB"/>
<stop offset="1" stop-color="#FFC3AF" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
