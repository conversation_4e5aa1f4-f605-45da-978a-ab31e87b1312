{"version": 3, "sources": ["./src/app/app.component.html", "./src/app/main/chat/chat.component.html", "./src/app/main/livevideo/livevideo.component.html", "./src/app/main/main.component.html", "./src/app/main/sidebar/sidebar.component.html", "./node_modules/tslib/tslib.es6.js", "./src/$_lazy_route_resource lazy namespace object", "./src/app/app-routing.module.ts", "./src/app/app.component.css", "./src/app/app.component.ts", "./src/app/app.module.ts", "./src/app/main/chat/chat.component.css", "./src/app/main/chat/chat.component.ts", "./src/app/main/livevideo/livevideo.component.css", "./src/app/main/livevideo/livevideo.component.ts", "./src/app/main/main.component.css", "./src/app/main/main.component.ts", "./src/app/main/sidebar/sidebar.component.css", "./src/app/main/sidebar/sidebar.component.ts", "./src/app/main/webSocketApi/WebSocketAPI.ts", "./src/environments/environment.ts", "./src/main.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAe,0F;;;;;;;;;;;;ACAf;AAAe,msB;;;;;;;;;;;;ACAf;AAAe,iJAAkF,+K;;;;;;;;;;;;ACAjG;AAAe,8U;;;;;;;;;;;;ACAf;AAAe,mI;;;;;;;;;;;;ACAf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,+DAA+D;AAC/D;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;;AAEO;AACP;AACA,mBAAmB,sBAAsB;AACzC;AACA;;AAEO;AACP;AACA,gDAAgD,OAAO;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA,4DAA4D,cAAc;AAC1E;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,4CAA4C,QAAQ;AACpD;AACA;;AAEO;AACP,mCAAmC,oCAAoC;AACvE;;AAEO;AACP;AACA;;AAEO;AACP;AACA,mCAAmC,MAAM,6BAA6B,EAAE,YAAY,WAAW,EAAE;AACjG,kCAAkC,MAAM,iCAAiC,EAAE,YAAY,WAAW,EAAE;AACpG,+BAA+B,iEAAiE,uBAAuB,EAAE,4BAA4B;AACrJ;AACA,KAAK;AACL;;AAEO;AACP,aAAa,6BAA6B,0BAA0B,aAAa,EAAE,qBAAqB;AACxG,gBAAgB,qDAAqD,oEAAoE,aAAa,EAAE;AACxJ,sBAAsB,sBAAsB,qBAAqB,GAAG;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,kCAAkC,SAAS;AAC3C,kCAAkC,WAAW,UAAU;AACvD,yCAAyC,cAAc;AACvD;AACA,6GAA6G,OAAO,UAAU;AAC9H,gFAAgF,iBAAiB,OAAO;AACxG,wDAAwD,gBAAgB,QAAQ,OAAO;AACvF,8CAA8C,gBAAgB,gBAAgB,OAAO;AACrF;AACA,iCAAiC;AACjC;AACA;AACA,SAAS,YAAY,aAAa,OAAO,EAAE,UAAU,WAAW;AAChE,mCAAmC,SAAS;AAC5C;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,MAAM,gBAAgB;AACzC;AACA;AACA;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;;AAEO;AACP,4BAA4B,sBAAsB;AAClD;AACA;AACA;;AAEO;AACP,iDAAiD,QAAQ;AACzD,wCAAwC,QAAQ;AAChD,wDAAwD,QAAQ;AAChE;AACA;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;AACA,iBAAiB,sFAAsF,aAAa,EAAE;AACtH,sBAAsB,gCAAgC,qCAAqC,0CAA0C,EAAE,EAAE,GAAG;AAC5I,2BAA2B,MAAM,eAAe,EAAE,YAAY,oBAAoB,EAAE;AACpF,sBAAsB,oGAAoG;AAC1H,6BAA6B,uBAAuB;AACpD,4BAA4B,wBAAwB;AACpD,2BAA2B,yDAAyD;AACpF;;AAEO;AACP;AACA,iBAAiB,4CAA4C,SAAS,EAAE,qDAAqD,aAAa,EAAE;AAC5I,yBAAyB,6BAA6B,oBAAoB,gDAAgD,gBAAgB,EAAE,KAAK;AACjJ;;AAEO;AACP;AACA;AACA,2GAA2G,sFAAsF,aAAa,EAAE;AAChN,sBAAsB,8BAA8B,gDAAgD,uDAAuD,EAAE,EAAE,GAAG;AAClK,4CAA4C,sCAAsC,UAAU,oBAAoB,EAAE,EAAE,UAAU;AAC9H;;AAEO;AACP,gCAAgC,uCAAuC,aAAa,EAAE,EAAE,OAAO,kBAAkB;AACjH;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP,4CAA4C;AAC5C;;;;;;;;;;;;ACnMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,4CAA4C,WAAW;AACvD;AACA;AACA,4E;;;;;;;;;;;;;;;;;;ACZyC;AACc;AAEvD,IAAM,MAAM,GAAW,EAAE,CAAC;AAM1B;IAAA;IAAgC,CAAC;IAApB,gBAAgB;QAJ5B,8DAAQ,CAAC;YACR,OAAO,EAAE,CAAC,4DAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvC,OAAO,EAAE,CAAC,4DAAY,CAAC;SACxB,CAAC;OACW,gBAAgB,CAAI;IAAD,uBAAC;CAAA;AAAJ;;;;;;;;;;;;;ACT7B;AAAe,6GAA8C,+H;;;;;;;;;;;;;;;;;ACAnB;AAO1C;IALA;QAME,UAAK,GAAG,QAAQ,CAAC;IACnB,CAAC;IAFY,YAAY;QALxB,+DAAS,CAAC;YACT,QAAQ,EAAE,UAAU;YACpB,yMAAmC;;SAEpC,CAAC;OACW,YAAY,CAExB;IAAD,mBAAC;CAAA;AAFwB;;;;;;;;;;;;;;;;;;;;;;;;;;;ACPiC;AACjB;AAEe;AACT;AAC2B;AACN;AACT;AACe;AACpB;AACH;AAqBnD;IAAA;IAAyB,CAAC;IAAb,SAAS;QAnBrB,8DAAQ,CAAC;YACR,YAAY,EAAE;gBACZ,2DAAY;gBACZ,sFAAkB;gBAClB,gFAAgB;gBAChB,uEAAa;gBACb,kEAAa;aACd;YACD,OAAO,EAAE;gBACP,uEAAa;gBACb,oEAAgB;gBAChB,4DAAc;gBACd,wDAAU;gBACV,6DAAe;gBACf,mEAAmB;aACpB;YACD,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,2DAAY,CAAC;SAC1B,CAAC;OACW,SAAS,CAAI;IAAD,gBAAC;CAAA;AAAJ;;;;;;;;;;;;;AC/BtB;AAAe,8EAAe,gBAAgB,qBAAqB,oBAAoB,qBAAqB,GAAG,WAAW,iBAAiB,wBAAwB,eAAe,sBAAsB,gBAAgB,GAAG,cAAc,iBAAiB,GAAG,QAAQ,iBAAiB,gBAAgB,iBAAiB,uBAAuB,8BAA8B,gCAAgC,gCAAgC,GAAG,cAAc,cAAc,GAAG,cAAc,0FAA0F,GAAG,eAAe,8BAA8B,0BAA0B,8BAA8B,GAAG,UAAU,eAAe,8BAA8B,GAAG,mBAAmB,iBAAiB,kBAAkB,kBAAkB,qBAAqB,GAAG,aAAa,gBAAgB,iBAAiB,uBAAuB,GAAG,cAAc,qCAAqC,yBAAyB,uBAAuB,GAAG,kBAAkB,iCAAiC,GAAG,YAAY,kBAAkB,sBAAsB,kBAAkB,GAAG,+CAA+C,m6E;;;;;;;;;;;;;;;;;;;;;ACAplC;AACD;AACvC;AACiC;AAO5D;IAGE;IAAgB,CAAC;sBAHN,aAAa;IAKxB,gCAAQ,GAAR;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,uEAAY,CAAC,IAAI,eAAa,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAC,IAAI,wDAAS,CAAC;YAC7B,OAAO,EAAC,IAAI,0DAAW,CAAC,EAAE,EAAC,yDAAU,CAAC,QAAQ,CAAC;SAChD,CAAC,CAAC;IACL,CAAC;IACD,gCAAQ,GAAR;QACE,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;YAC1B,IAAI,GAAG,GAAG,4BAA4B;gBACpC,6EAA6E;gBAC7E,0CAA0C;gBAC1C,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO;gBACrC,cAAc;gBACd,UAAU,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACjB,mCAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,mCAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACvC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;SAC9C;IACH,CAAC;IAED,qCAAa,GAAb,UAAc,MAAM;QAClB,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjB,yBAAyB;QACzB,IAAI,GAAG,GAAG,6BAA6B;YACrC,8CAA8C;YAC9C,IAAI,GAAE,GAAG,CAAC,SAAS,CAAC;YACpB,cAAc;YACd,0BAA0B;YAC1B,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC;YACpB,QAAQ;YACR,UAAU,CAAC;QACb,mCAAC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,mCAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IACzC,CAAC;;IAvCU,aAAa;QANzB,+DAAS,CAAC;YACT,QAAQ,EAAE,UAAU;YACpB,qNAAoC;YAEpC,aAAa,EAAE,+DAAiB,CAAC,IAAI;;SACtC,CAAC;;OACW,aAAa,CAwCzB;IAAD,oBAAC;CAAA;AAxCyB;;;;;;;;;;;;;ACV1B;AAAe,+EAAgB,wBAAwB,gBAAgB,GAAG,SAAS,oBAAoB,qBAAqB,GAAG,iBAAiB,kCAAkC,GAAG,+CAA+C,2kB;;;;;;;;;;;;;;;;;ACAlL;AAOlD;IAEE;IAAgB,CAAC;IAEjB,qCAAQ,GAAR;IACA,CAAC;IALU,kBAAkB;QAL9B,+DAAS,CAAC;YACT,QAAQ,EAAE,eAAe;YACzB,oOAAyC;;SAE1C,CAAC;;OACW,kBAAkB,CAO9B;IAAD,yBAAC;CAAA;AAP8B;;;;;;;;;;;;;ACP/B;AAAe,6GAA8C,uI;;;;;;;;;;;;;;;;;ACAX;AAOlD;IACE;IAAgB,CAAC;IAEjB,gCAAQ,GAAR;IACA,CAAC;IAJU,aAAa;QALzB,+DAAS,CAAC;YACT,QAAQ,EAAE,UAAU;YACpB,gNAAoC;;SAErC,CAAC;;OACW,aAAa,CAMzB;IAAD,oBAAC;CAAA;AANyB;;;;;;;;;;;;;ACP1B;AAAe,yEAAU,2BAA2B,iBAAiB,eAAe,qBAAqB,GAAG,+CAA+C,mb;;;;;;;;;;;;;;;;;ACAzG;AAOlD;IAEE;IAAgB,CAAC;IAEjB,mCAAQ,GAAR;IACA,CAAC;IALU,gBAAgB;QAL5B,+DAAS,CAAC;YACT,QAAQ,EAAE,aAAa;YACvB,8NAAuC;;SAExC,CAAC;;OACW,gBAAgB,CAO5B;IAAD,uBAAC;CAAA;AAP4B;;;;;;;;;;;;;;;;;;;;;ACPI;AACO;AAGxC;IAKI,sBAAY,aAA4B;QAJxC,sBAAiB,GAAW,6BAA6B,CAAC;QAC1D,UAAK,GAAW,EAAE,CAAC;QAIf,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACvC,CAAC;IACD,+BAAQ,GAAR,UAAS,MAAM;QACX,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,IAAI,EAAE,GAAG,IAAI,0CAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,4CAAU,CAAC,EAAE,CAAC,CAAC;QAClC,IAAM,KAAK,GAAG,IAAI,CAAC;QACnB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,EAAE,UAAU,KAAK;YACzC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,QAAQ;gBAClD,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YACH,2CAA2C;QAC/C,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC3B,CAAC;IAAA,CAAC;IAEF,kCAAW,GAAX;QACI,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE;YAC3B,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;SACjC;QACD,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAChC,CAAC;IAED,4CAA4C;IAC5C,oCAAa,GAAb,UAAc,KAAK;QAAnB,mBAKC;QAJG,OAAO,CAAC,GAAG,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACxC,UAAU,CAAC;YACP,OAAI,CAAC,QAAQ,CAAC,OAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAEJ;;;OAGG;IACA,4BAAK,GAAL,UAAM,OAAO;QACT,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACrE,CAAC;IAED,wCAAiB,GAAjB,UAAkB,OAAO;QACrB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACnE,CAAC;IACL,mBAAC;AAAD,CAAC;;;;;;;;;;;;;;ACtDD;AAAA;AAAA;AAAA,gFAAgF;AAChF,0EAA0E;AAC1E,gEAAgE;;AAEzD,IAAM,WAAW,GAAG;IACzB,UAAU,EAAE,KAAK;CAClB,CAAC;AAEF;;;;;;GAMG;AACH,mEAAmE;;;;;;;;;;;;;;;;;;;;ACfpB;AAC4B;AAE9B;AACY;AAEzD,IAAI,qEAAW,CAAC,UAAU,EAAE;IAC1B,oEAAc,EAAE,CAAC;CAClB;AAED,gGAAsB,EAAE,CAAC,eAAe,CAAC,yDAAS,CAAC;KAChD,KAAK,CAAC,aAAG,IAAI,cAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAlB,CAAkB,CAAC,CAAC", "file": "main.js", "sourcesContent": ["export default \"\\n<app-main></app-main>\\n\"", "export default \"\\n<div class=\\\"chat-wrapper shadow-sm bg-white rounded \\\">\\n<div class=\\\"message-wrapper\\\">\\n  <div class=\\\"media\\\">\\n  </div>\\n\\n</div>\\n\\n  <div class=\\\"chat-input\\\">\\n    <form [formGroup]=\\\"messageForm\\\" (ngSubmit)=\\\"onSubmit()\\\" >\\n        <div class=\\\"row\\\">\\n          <div class=\\\"col-11\\\">\\n             <textarea class=\\\"shadow-sm rounded\\\" placeholder=\\\"Type a Message\\\" formControlName=\\\"message\\\"></textarea>\\n          </div>\\n          <div class=\\\"col-1\\\">\\n              <button type=\\\"submit\\\" class=\\\"send\\\"><i class=\\\"material-icons\\\">send</i> </button>\\n          </div>\\n        </div>\\n    </form>\\n  </div>\\n</div>\\n\\n\"", "export default \"\\n  <div class=\\\"video-wrapper shadow-sm bg-white rounded\\\" style=\\\"height: 45vh;\\\">\\n\\n  </div>\\n  <div class=\\\"video-details\\\">\\n    <h1 class=\\\"title\\\">Lorem Ipsum | lorem | English grammar</h1>\\n    <p class=\\\"text-muted\\\">4000 views</p>\\n  </div>\\n\\n\"", "export default \"<div class=\\\"row\\\">\\n<!--  <div class=\\\"col-3\\\">-->\\n<!--     <app-sidebar></app-sidebar>-->\\n<!--  </div>-->\\n  <div class=\\\"col-12\\\">\\n<!--    <app-livevideo></app-livevideo>-->\\n    <div class=\\\"container\\\">\\n    <app-chat></app-chat>\\n    </div>\\n  </div>\\n</div>\\n\"", "export default \"\\n<div class=\\\"sidebar shadow-sm bg-white rounded\\\">\\n\\n</div>\\n\\n\"", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n", "function webpackEmptyAsyncContext(req) {\n\t// Here Promise.resolve().then() is used instead of new Promise() to prevent\n\t// uncaught exception popping up in devtools\n\treturn Promise.resolve().then(function() {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t});\n}\nwebpackEmptyAsyncContext.keys = function() { return []; };\nwebpackEmptyAsyncContext.resolve = webpackEmptyAsyncContext;\nmodule.exports = webpackEmptyAsyncContext;\nwebpackEmptyAsyncContext.id = \"./src/$$_lazy_route_resource lazy recursive\";", "import { NgModule } from '@angular/core';\nimport { Routes, RouterModule } from '@angular/router';\n\nconst routes: Routes = [];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n", "export default \"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzcmMvYXBwL2FwcC5jb21wb25lbnQuY3NzIn0= */\"", "import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'chatio';\n}\n", "import { BrowserModule } from '@angular/platform-browser';\nimport { NgModule } from '@angular/core';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LivevideoComponent } from './main/livevideo/livevideo.component';\nimport { SidebarComponent } from './main/sidebar/sidebar.component';\nimport { ChatComponent } from './main/chat/chat.component';\nimport {CollapseModule, TabsModule, TypeaheadModule} from \"ngx-bootstrap\";\nimport { MainComponent } from './main/main.component';\nimport {ReactiveFormsModule} from \"@angular/forms\";\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LivevideoComponent,\n    SidebarComponent,\n    ChatComponent,\n    MainComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    CollapseModule,\n    TabsModule,\n    TypeaheadModule,\n    ReactiveFormsModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n", "export default \".chat-wrapper{\\n  width: 100%;\\n  min-height: 90vh;\\n  margin-top:1rem;\\n  overflow-y: auto;\\n}\\ntextarea{\\n  border: none;\\n  background: #fafafa;\\n  outline: 0;\\n  padding: 0px 10px;\\n  width: 100%;\\n}\\n.chat-input{\\n  margin: 1rem;\\n}\\n.send{\\n  border: none;\\n  width: 42px;\\n  height: 42px;\\n  border-radius: 50%;\\n  background-color: #ff7675;\\n  background-position: center;\\n  transition: background 0.8s;\\n}\\n.send:focus{\\n  outline:0;\\n}\\n.send:hover{\\n  background: #47a7f5 radial-gradient(circle, transparent 1%, #47a7f5 1%) center/15000%;\\n}\\n.send:active{\\n  background-color: #6eb9f7;\\n  background-size: 100%;\\n  transition: background 0s;\\n}\\n.send i{\\n  color:#fff;\\n  transform: rotate(-35deg);\\n}\\n.message-wrapper{\\n  height: 80vh;\\n  margin:0 1rem;\\n  padding: 2rem;\\n  overflow-y: auto;\\n}\\n.media img{\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 0;\\n}\\n.media-body{\\n  background:rgba(255,118,117,0.3);\\n  padding: 0.5rem 1rem;\\n  border-radius: 4px;\\n}\\n.media-body.you{\\n  background: rgb(250,250,250);\\n}\\n.username{\\n  padding: 1rem;\\n  font-weight: bold;\\n  color:#4a4a4a;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\"", "import {Component, OnInit, ViewEncapsulation} from '@angular/core';\nimport {FormControl, FormGroup, Validators} from \"@angular/forms\";\nimport * as $ from 'jquery'\nimport { WebSocketAPI } from \"../webSocketApi/WebSocketAPI\";\n@Component({\n  selector: 'app-chat',\n  templateUrl: './chat.component.html',\n  styleUrls: ['./chat.component.css'],\n  encapsulation: ViewEncapsulation.None,\n})\nexport class ChatComponent implements OnInit {\n  messageForm:FormGroup;\n  webSocketAPI:WebSocketAPI;\n  constructor() { }\n\n  ngOnInit() {\n    this.webSocketAPI = new WebSocketAPI(new ChatComponent());\n    this.webSocketAPI._connect(\"/topic/public\");\n    this.messageForm=new FormGroup({\n      message:new FormControl('',Validators.required),\n    });\n  }\n  onSubmit() {\n    if (this.messageForm.valid) {\n      var str = '<div class=\"media mt-4\">\\n' +\n        '    <img src=\"../../../assets/images/profile.jpg\" class=\"mr-3\" alt=\"...\">\\n' +\n        '    <div class=\"media-body shadow-sm\">\\n' +\n        '\\n' + this.messageForm.value.message +\n        '    </div>\\n' +\n        '  </div>';\n      console.log(str);\n      $(str).insertAfter($('.media').last());\n      this.messageForm.get('message').setValue(\"\");\n    }\n  }\n\n  handleMessage(objStr){\n    var obj = JSON.parse(JSON.parse(objStr));\n    console.log(obj);\n    // obj = JSON.parse(obj);\n    var str = ' <div class=\"media mt-4\">\\n' +\n      '    <div class=\"media-body shadow-sm you\">\\n' +\n      '\\n'+ obj['message'] +\n      '    </div>\\n' +\n      '    <p class=\"username\">' +\n      '\\n' + obj['sender'] +\n      '</p>\\n' +\n      '  </div>';\n    $(str).insertAfter($('.media').last());\n  }\n}\n", "export default \".video-wrapper{\\n  background: #ffffff;\\n  width: 100%;\\n}\\n.title{\\n  font-size: 20px;\\n  margin-top: 1rem;\\n}\\n.video-details{\\n  border-bottom: 1px solid #ddd;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNyYy9hcHAvbWFpbi9saXZldmlkZW8vbGl2ZXZpZGVvLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxtQkFBbUI7RUFDbkIsV0FBVztBQUNiO0FBQ0E7RUFDRSxlQUFlO0VBQ2YsZ0JBQWdCO0FBQ2xCO0FBQ0E7RUFDRSw2QkFBNkI7QUFDL0IiLCJmaWxlIjoic3JjL2FwcC9tYWluL2xpdmV2aWRlby9saXZldmlkZW8uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi52aWRlby13cmFwcGVye1xuICBiYWNrZ3JvdW5kOiAjZmZmZmZmO1xuICB3aWR0aDogMTAwJTtcbn1cbi50aXRsZXtcbiAgZm9udC1zaXplOiAyMHB4O1xuICBtYXJnaW4tdG9wOiAxcmVtO1xufVxuLnZpZGVvLWRldGFpbHN7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZGRkO1xufVxuIl19 */\"", "import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-livevideo',\n  templateUrl: './livevideo.component.html',\n  styleUrls: ['./livevideo.component.css']\n})\nexport class LivevideoComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "export default \"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzcmMvYXBwL21haW4vbWFpbi5jb21wb25lbnQuY3NzIn0= */\"", "import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-main',\n  templateUrl: './main.component.html',\n  styleUrls: ['./main.component.css']\n})\nexport class MainComponent implements OnInit {\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "export default \".sidebar{\\n  background-color: #fff;\\n  height: 95vh;\\n  width:100%;\\n  overflow-y: auto;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNyYy9hcHAvbWFpbi9zaWRlYmFyL3NpZGViYXIuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLHNCQUFzQjtFQUN0QixZQUFZO0VBQ1osVUFBVTtFQUNWLGdCQUFnQjtBQUNsQiIsImZpbGUiOiJzcmMvYXBwL21haW4vc2lkZWJhci9zaWRlYmFyLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuc2lkZWJhcntcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcbiAgaGVpZ2h0OiA5NXZoO1xuICB3aWR0aDoxMDAlO1xuICBvdmVyZmxvdy15OiBhdXRvO1xufVxuIl19 */\"", "import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-sidebar',\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.css']\n})\nexport class SidebarComponent implements OnInit {\n\n  constructor() { }\n\n  ngOnInit() {\n  }\n\n}\n", "import * as Stomp from 'stompjs';\nimport * as Sock<PERSON><PERSON> from 'sockjs-client';\nimport { ChatComponent } from '../chat/chat.component';\n\nexport class WebSocketAPI {\n    webSocketEndPoint: string = 'http://192.168.1.26:8080/ws';\n    topic: string = \"\";\n    stompClient: any;\n    chatComponent: ChatComponent;\n    constructor(chatComponent: ChatComponent){\n        this.chatComponent = chatComponent;\n    }\n    _connect(_topic) {\n        this.topic = _topic;\n        console.log(\"Initialize WebSocket Connection\");\n        let ws = new SockJS(this.webSocketEndPoint);\n        this.stompClient = Stomp.over(ws);\n        const _this = this;\n        _this.stompClient.connect({}, function (frame) {\n            _this.stompClient.subscribe(_topic, function (sdkEvent) {\n                _this.onMessageReceived(sdkEvent);\n            });\n            //_this.stompClient.reconnect_delay = 2000;\n        }, this.errorCallBack);\n    };\n\n    _disconnect() {\n        if (this.stompClient !== null) {\n            this.stompClient.disconnect();\n        }\n        console.log(\"Disconnected\");\n    }\n\n    // on error, schedule a reconnection attempt\n    errorCallBack(error) {\n        console.log(\"errorCallBack -> \" + error)\n        setTimeout(() => {\n            this._connect(this.topic);\n        }, 5000);\n    }\n\n /**\n  * Send message to sever via web socket\n  * @param {*} message \n  */\n    _send(message) {\n        console.log(\"calling logout api via web socket\");\n        this.stompClient.send(\"/app/hello\", {}, JSON.stringify(message));\n    }\n\n    onMessageReceived(message) {\n        console.log(message);\n        this.chatComponent.handleMessage(JSON.stringify(message.body));\n    }\n}\n", "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\n\nexport const environment = {\n  production: false\n};\n\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/dist/zone-error';  // Included with Angular CLI.\n", "import { enableProdMode } from '@angular/core';\nimport { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\n\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\n\nif (environment.production) {\n  enableProdMode();\n}\n\nplatformBrowserDynamic().bootstrapModule(AppModule)\n  .catch(err => console.error(err));\n"], "sourceRoot": "webpack:///"}