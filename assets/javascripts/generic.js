/**
 * Created by achy<PERSON>nan<PERSON> on 17/02/16.
 */

function genericValidate(flds){
    var allFilled=true
    $('.alert').hide();

    for (i=0; i<flds.length; i++) {
        if( !$("#"+flds[i]).val() ) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#"+flds[i]).addClass('has-error');
            $("#"+flds[i]).closest('.input-group').addClass('has-error');
            $("#"+flds[i]).closest('.form-group').addClass('has-error');
            allFilled = false;
        }
        else{
            $("#"+flds[i]).removeClass('has-error');
            $("#"+flds[i]).closest('.input-group').removeClass('has-error');
            $("#"+flds[i]).closest('.form-group').removeClass('has-error');
        }

    }
    if(!allFilled){
        $('.alert').show();

    }

    return allFilled;

}

function isNumeric(n) {
    return !isNaN(parseFloat(n)) && isFinite(n);
}
