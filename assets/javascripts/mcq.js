

function showNextQuestionMCQ(index){
    qa = quiz.questions;
    var questionDiv = document.createElement('div');
    var questionNumberDiv = document.createElement('div');
    var question=qa[index].ps;
    if(question.indexOf("<p>")==0) question = question.substr(3);
    if(quiz.description){
        document.getElementById("quizdescription").innerHTML="<div class='col-md-12 text-center'>"+quiz.description+"</div><br>";
        $("#quizdescription").show();
    } else{
        document.getElementById("quizdescription").innerHTML="";
        $("#quizdescription").hide();
    }

    if(quiz.isPassage){
        document.getElementById("passage").innerHTML="<div class='col-md-12'>"+quiz.passage+"</div><br>";
        $("#passage").show();
    } else{
        document.getElementById("passage").innerHTML="";
        $("#passage").hide();
    }

    if(qa[index].directions){
        document.getElementById("directions").innerHTML="<div class='col-md-12'>"+qa[index].directions+"</div><br>";
        $("#directions").show();
    } else{
        document.getElementById("directions").innerHTML="";
        $("#directions").hide();
    }

    quiz.currentIndex = index;
    questionNumberDiv.innerHTML ="<b>"+ (index+1)+" / "+(qa.length)+"</b>";
    var questionImage = qa[index].fileName;
    qa[index].ps = qa[index].ps.replace('<p>','');
    qa[index].ps = qa[index].ps.replace('</p>','');
    questionDiv.innerHTML= "<p class='question-string'>"+""+(index+1)+". "+""+qa[index].ps;+"</p>";

    $("#quizModal").find('.question').text(""); // to remove old ones
    $("#quizModal").find('#question')[0].appendChild(questionDiv);
    var questionStr = "";
    if ("Fill in the blanks" == quiz.type){
        console.log("3");
        resetTextInput(index);
    }
    else {


        var option;
        var optionLabel = ["A", "B", "C", "D", "E"];

        for (j = 1; j < 6; j++) {
            option = eval("qa[" + index + "].op" + j);

            if (option) {
                if (option.indexOf("<p") == 0) {
                    option = option.replace('<p>', '');
                    option = option.replace('</p>', '');
                }

                if ("checkbox" == qa[index].optionType) {
                    questionStr += "<div class='questionrow question-row-bordered' id='answerOption" + j + "'> " +
                        " <div class='question-div' style='padding-left: 0;'>" +
                        " <div class='checkbox'>" +
                        "<label class='full-wdth-label'>" +
                        "<input type='checkbox' name='quiz.cbx.ans" + j + "' id='ans" + j + "'>" +
                        "<span class='checkmark checkmark-radio checkmark-checkbox' style='border:0; padding: 1px 0 0 1px;'>" + " " + optionLabel[j - 1] + "</span>" +
                        "<p class='option1 greytext' style='margin: 0 0 0 16px;'>" + option + "</p>" +
                        " </label>" +
                        " </div>" +
                        " </div></div>" +
                        "</div>";
                } else {
                    questionStr += "<div class='questionrow question-row-bordered' id='answerOption" + j + "'> " +
                        " <div class='question-div' style='padding-left: 0;'>" +
                        " <div class='radio'>" +
                        "<label class='full-wdth-label'>" +
                        "<input type='radio' name='quiz.rad.ans1' value='ans" + j + "' id='ans" + j + "'" +
                        " onclick='radioAnswerChecked(" + index + "," + j + ")'>" +
                        "<span class='checkmark checkmark-radio' style='border:0; padding: 1px 0 0 1px;'>" + " " + optionLabel[j - 1] + "</span>" +
                        "<p class='option1 greytext' style='margin: 0 0 0 16px;'>" + option + "</p>" +
                        " </label>" +
                        " </div>" +
                        " </div></div>" +
                        "</div>";
                }
            }
        }
    }

        questionStr =questionStr+ "<div class='row'>" +
                " <div class=''>" +
                " <p  class='text-danger unanswered'></p>" +
                "</div>" +
                "</div>";



    var questionSectionDiv = document.createElement('div');
    questionSectionDiv.innerHTML=questionStr;
    $("#quizModal").find('.mcqquestion').text(""); // to remove old ones
    $("#quizModal").find("#mcqquestion")[0].appendChild(questionSectionDiv);
    resetCheckBoxes(index);
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);


    $("#mcqquestionsection").show();
    $("#answersection").show();


    $("#questionumber-containter").show();

    $('html, body').animate({
        scrollTop: 0
    }, 500);
}

function scoreAndShowAnswersMCQ(data){
    var readMode=false;
    var msgStr;
    quiz.answers=data.results;
    qa = data.results;
    chaptersList = quiz.chaptersList;
    quiz.score = 0;
    var questionNumberButtons = document.getElementById('questionumber-containter');
    var questionNumberString = "";
    var displayQuestions;
	
    if(quiz.mode == "read"){
        readMode = true;
        qa = data.results;

        if ("Fill in the blanks" == quiz.type) {
            quiz.originalQuestions = data.results;
            quiz.questions = formatQuestions(data.results);
            quiz.userAnswers = qa;
        }else{
            quiz.questions = data.results;
            removeNullFromQuestions();
            quiz.userAnswers = qa;
        }


            $("#mainbookcontent").fadeOut(1000);
            $("#quizModal").show();

        modalName = "quizModal";

            $("#mainbookcontent").fadeOut(1000);
            $("#"+modalName).show();

        quiz.description=data.description;
    }else {
        subjects=[];
        subjectDetails=[];

        //check if this test generator thingy
        if(chaptersList){
            for(var index = 0; index < qa.length; ++index) {
                for (l = 0; l < chaptersList.length; l++) {
                    if(qa[index].quizId==chaptersList[l].quizId){
                        qa[index].subject = chaptersList[l].subject;
                        qa[index].chapterId = chaptersList[l].id;
                        break;

                    }
                }
            }
        }

        for(var index = 0; index < qa.length; ++index) {
            if(qa[index].subject) {
                subjectKey = qa[index].subject.replace(/\W+/g, '');
                if (subjects.indexOf(qa[index].subject) < 0) {
                    subjects.push(qa[index].subject);
                    var subjectDetail = {};
                    subjectDetail.noOfQuestions = 1;
                    subjectDetail.correctAnswers = 0;
                    subjectDetail.chapters={};
                    //add the chapter information
                    var chapter = {};
                    chapter.id=qa[index].chapterId;
                    chapter.noOfQuestions = 1;
                    chapter.correctAnswers = 0;
                    chapter.wrongAnswers = 0;
                    chapter.skipped=0;
					
                    if(chaptersList){
                        chapter.name = getChapterName(chaptersList,qa[index].chapterId);
                        subjectDetail.chapters[''+qa[index].chapterId] = chapter;
                    }


                    subjectDetails[subjectKey] = subjectDetail;
                } else {
                    //already added
                    subjectDetails[subjectKey].noOfQuestions++;
                    if(subjectDetails[subjectKey].chapters[''+qa[index].chapterId]==undefined) {

                        //if the chapter doesnt exist
                        var chapter = {};
                        chapter.id = qa[index].chapterId;
                        chapter.noOfQuestions = 1;
                        chapter.correctAnswers = 0;
                        chapter.wrongAnswers = 0;
                        chapter.skipped=0;
                        
                        if (chaptersList) {
                            chapter.name = getChapterName(chaptersList, qa[index].chapterId);
                            subjectDetails[subjectKey].chapters[''+qa[index].chapterId] = chapter;
                        }

                    }else{
                        //if the chapter already exists
                        subjectDetails[subjectKey].chapters[''+qa[index].chapterId].noOfQuestions++;
                    }

                }
            }
        }
		
        var score;
        if ("Fill in the blanks" == quiz.type) {
            score =  getScoreFIB(quiz.answers);

        }else{
            score = getScoreMCQ(qa);
        }
        quiz.score = score.correctAnswers;

        msgStr = scoreMessage(score,qa.length);
        quiz.userAnswers = userAnswers;
        questionNumberButtons.innerHTML = "";
        updateWithQuizAnswers(userAnswers,score,quiz.generated);
        quiz.generated="false";

    }

    if(quiz.isPassage){
        document.getElementById("answerpassage").innerHTML="<div class='col-md-12 smallText greyText'>"+quiz.passage+"</div><br><br>";
        $("#answerpassage").show();
    } else{
        document.getElementById("answerpassage").innerHTML="";
        $("#answerpassage").hide();
    }
	
    if(quiz.description){
        document.getElementById("quizanswerdescription").innerHTML="<div class='col-md-12 text-center'>"+quiz.description+"</div><br>";
        $("#quizanswerdescription").show();
    } else{
        document.getElementById("quizanswerdescription").innerHTML="";
        $("#quizanswerdescription").hide();
    }

    var scoreDiv = document.createElement('div');
    var answerDiv = document.createElement('div');
        answerDiv.className = 'answer-holder-inner';


    var answerClass1="",answerClass2="",answerClass3="",answerClass4="";

    $("#quizModal").find('.questionsection').css("display","none");
    $("#quizModal").find('.answersection').css("display","block");
    $("#quizModal").find('.score-container').text(""); // to remove old ones
	
    if(!readMode) {
        scoreDiv.innerHTML = msgStr;
        $("#quizModal").find('#score-container')[0].appendChild(scoreDiv);

    }

    var answerStr="";
    var correctAnswer="";
    var option5present=false;
    var directions="";
    var questionString="";
    var labelClass="";
    var answerClass="";
    var answer="";
    if ("Fill in the blanks" == quiz.type) {
        displayQuestions = getDisplayFormat(quiz.originalQuestions, quiz.answers);

    }

  for(var i = 0; i < qa.length; ++i) {
      var questionClassName = 'skipped-question';
      if ("Fill in the blanks" == quiz.type) {

          answerStr += "<div class='row'><div style='margin-top: 30px;>"+
              "<div class='row'>"+
              "<div class='col-md-9'>"+
              "<a href='#' id='question" + (i + 1) + "'></a><span>"+(i+1)+".&nbsp;"+displayQuestions[i].ps+"</span>"+
              "</div>";
          if(!readMode) {
              if ((userAnswers[i]['correctAnswer'] == "false" || userAnswers[i]['correctAnswer'] == "true") && userAnswers[i].skipped != "true") {
                  if (userAnswers[i]['correctAnswer'] == "false") {
                      questionClassName = "wrong-question-answer";
                  } else {
                      questionClassName = "correct-question-answer";
                  }
              }
              answerStr +="<div class='col-md-3 left-border-vr  text-center correct-answers'>" +
                      "<p style='font-weight: 700;'>Correct Answer</p>" +
                      "<p>" +
                      "<span style='text-transform: capitalize'>" + displayQuestions[i].as + "</span>" +
                      "</p>" +
                      "</div>";

          }

          answerStr += "</div>"+"</div></div>";
          if(!readMode) answerStr+="<hr>";
          questionNumberString = questionNumberString + "<a href='#question" + (i + 1) + "' class='" + questionClassName + "'>" + (i + 1) + "</a>";

      } else {


          labelClass = "correct-answer-by-user";
          answerClass = "correct-answer-by-user";
          answer = "";



          if (!readMode) {
              if ((userAnswers[i]['correctAnswer'] == "false" || userAnswers[i]['correctAnswer'] == "true") && userAnswers[i].skipped != "true") {
                  if (userAnswers[i]['correctAnswer'] == "false") {
                      labelClass = "wrong-answer-label";
                      answerClass = "wrong-answer-by-user";
                      questionClassName = "wrong-question-answer";
                  } else {
                      questionClassName = "correct-question-answer";
                  }

                  //get the answers
                  for (var key in userAnswers[i]) {
                      if (("" + userAnswers[i][key]) == 'Yes') {
                          var answerString = eval("qa[" + i + "]." + eval("ansOptKeys." + key));
                          if (answerString) {
                              if (answerString.indexOf("<p") == 0) {
                                  answerString = answerString.replace('<p>', '');
                                  answerString = answerString.replace('</p>', '');
                              }
                          }

                          answer += answerString + "<br>";
                      }
                  }

              } else if (userAnswers[i].skipped == "true") {
                  labelClass = "wrong-answer-label";
                  answerClass = "wrong-answer-by-user";
                  answer = "Skipped";
              }
          }

          questionString = qa[i].ps;
          questionString = questionString.replace("<p>", "");
          questionString = questionString.replace("</p>", "");

          if (qa[i].directions) {
              directions = "<div class='row'><div class='col-md-12 smallText greyText'>" + qa[i].directions + "</div></div><br>";
          } else {
              directions = "";
          }

          resetCheckBoxes(0); //removing the state of checkboxes.. so that if same quiz is loaded after the results.. the answers are removed

          if (correctAnswer.indexOf("<p>") == 0) correctAnswer = correctAnswer.substr(3);

          if (qa[i].answerDescription) {
              qa[i].answerDescription = qa[i].answerDescription.replace('<p>', '');
              qa[i].answerDescription = qa[i].answerDescription.replace('</p>', '');
          }

          if (qa[i].answerDescription) {
              var descriptionOfAnswer = "";
              descriptionOfAnswer += "<div class='answer-description col-md-12'>" +
                  "<p class='show-explanation'>" +
                  "<a href='#' class='show-explanation-btn'>" + "Show Explanation" + "</a>" +
                  "</p>" +
                  "<div class='correct-answer-explanation'>" + qa[i].answerDescription + "</div>" +
                  "</div>";
          } else {
              descriptionOfAnswer = "";
          }

          var option;
          correctAnswer = "";
          for (j = 1; j < 6; j++) {
              option = eval("qa[" + i + "].op" + j);
              if (option) {
                  if (option.indexOf("<p") == 0) {
                      option = option.replace('<p>', '');
                      option = option.replace('</p>', '');
                  }

                  if (eval("qa[" + i + "].ans" + j) == 'Yes') correctAnswer += option + '<br>';
              }
          }

          if (quiz.mode != 'read') {
              answerStr += directions + "<div class='col-md-12' id='sum-question' style='padding-left: 0;'>" + "<b>" + (i + 1) + ") " + "</b>" +
                  "<a href='#' id='question" + (i + 1) + "'></a>" + questionString + "</div>";
              var option;

              for (j = 1; j < 6; j++) {
                  option = eval("qa[" + i + "].op" + j);

                  if (option) {
                      if (option.indexOf("<p") == 0) {
                          option = option.replace('<p>', '');
                          option = option.replace('</p>', '');
                      }

                      answerStr += "<div class='col-md-12'>" +
                          "<div class='col-md-1' style='width: 3%; padding-left:0;'> " + optionsArray[j - 1] + ". </div>" +
                          "<div class='col-md-11 question-div-mcq' style='padding-left:0;'>" + option + "</div>" +
                          "</div>";
                  }
              }

              answerStr += "<div class='correct-answer-learn col-md-12' style='border-bottom: 0; margin-bottom:0;'>" +
                  "<p class='correct-answer-label " + answerClass + "'>" + "Your Answer:" + "</p>" +
                  "<p class='correct-answer " + answerClass + "'>" + answer + "</p>" +
                  "</div>" +
                  "<div class='correct-answer-learn col-md-12'>" +
                  "<p class='correct-answer-label correct-answer-by-user'>" + "Correct Answer:" + "</p>" +
                  "<p class='correct-answer correct-answer-by-user'>" + correctAnswer + "</p>" +
                  descriptionOfAnswer +
                  "</div>";
          } else {
              answerStr += directions + "<div class='col-md-12' id='sum-question' style='padding-left: 0;'>" + "<b>" + (i + 1) + ") " + "</b>" + questionString + "</div>" +
                  "<div class='correct-answer-learn col-md-12'>" +
                  "<p class='correct-answer-label'>" + "Answer:" + "</p>" +
                  "<p class='correct-answer'>" + correctAnswer + "</p>" +
                  descriptionOfAnswer +
                  "</div>";
          }


          questionNumberString = questionNumberString + "<a href='#question" + (i + 1) + "' class='" + questionClassName + "'>" + (i + 1) + "</a>";
      }
  }
	
    document.getElementById('questionumber-containter').innerHTML = questionNumberString;
    answerDiv.innerHTML=answerStr;
    $("#quizModal").find('.answer-holder').text(""); // to remove old ones
    $("#quizModal").find('#answer-holder')[0].appendChild(answerDiv);

    //initializing stuff..if the same quiz is loaded again
    quiz.currentIndex=0;
    quiz.mode="play";
    resetUserAnswers();
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
}


function getChapterName(chaptersList,chapterId){
    var chapterName;
    for(j=0;j<chaptersList.length;j++){
        if(chaptersList[j].id==chapterId){
            chapterName = chaptersList[j].name;
            break;
        }
    }

    return chapterName;
}

var checkedIndex;
var checkedOptionNo;

function check(){
    var index = checkedIndex;
    var optionNo = checkedOptionNo;
    var option;
    var answerOption;
    var answerOptionIndex;

    for (j = 1; j < 6; j++) {
        option = eval("qa[" + index + "].op" + j);
        answerOption = eval("qa[" + index + "].ans" + j);
        if('Yes'==answerOption) answerOptionIndex = j;
        if (option) {
            if (j != optionNo) document.getElementById("ans" + j).disabled = true;
        }
    }
    if(answerOptionIndex==optionNo) {
        document.getElementById("answerOption"+optionNo).classList.add("question-row-bordered-right");
    } else {
        document.getElementById("answerOption"+optionNo).classList.add("question-row-bordered-wrong");
        document.getElementById("answerOption"+answerOptionIndex).classList.add("question-row-bordered-right");
    }
    if (index == (quiz.questions.length - 1)) {
        $('.next-btn').html('Submit');
        $('.next-btn').attr('href', 'javascript:done();');
    }else {
        $('.next-btn').html('Next');
        $('.next-btn').attr('href', 'javascript:nextQ();');
    }

}

function radioAnswerChecked(index,optionNo){

    checkedIndex =  index;
    checkedOptionNo = optionNo;
    $('.next-btn').removeClass('disabled');
}

function saveAnswerMCQ(index){

    if($('#ans1').is(":checked") || $('#ans2').is(":checked") || $('#ans3').is(":checked") || $('#ans4').is(":checked")|| $('#ans5').is(":checked")){
        userAnswers[index] = {'skipped':'false','ans1':$('#ans1').is(":checked") ? 'Yes' : null, 'ans2':$('#ans2').is(":checked") ? 'Yes': null, 'ans3':$('#ans3').is(":checked") ? 'Yes' : null, 'ans4': $('#ans4').is(":checked") ? 'Yes' : null, 'ans5': $('#ans5').is(":checked") ? 'Yes' : null};
        return true;
    }else {
        userAnswers[index]={'skipped':'true','ans1' : null,'ans2' : null,'ans3' : null,'ans4' : null,'ans5' : null}
        return false;
    }
}
function getScoreMCQ(answers){
    var length = userAnswers.length;
    var score={}, isIncorrect = false;
    score.correctAnswers = 0;
    score.wrongAnswers=0;
    score.skipped=0;
    score.totalQuesions=answers.length;

    for(var i = 0; i < length; ++i){
        userAnswers[i]['correctAnswer']="false";
        userAnswers[i]['id']=answers[i]['id'];
        
        for(var key in userAnswers[i]){
            //removed the simple check and added a more complicated one, as in some browsers it was evaluating correctly
            // this current if condition is checking if both or yes or if both are not yes... if one of them of them is true.. then its not error.
            if(userAnswers[i].skipped=="true"){
                isIncorrect = true;
                score.skipped++;
                if(answers[i].subject) {
                    var subjectDetail = subjectDetails[answers[i].subject.replace(/\W+/g, '')];
                    var chapterKeys = Object.keys(subjectDetail.chapters);

                    if(answers[i].chapterId){
                        for(k=0;k<chapterKeys.length;k++){
                            var chapter = subjectDetail.chapters[''+chapterKeys[k]];
                            if(chapter.id==answers[i].chapterId){
                                chapter.skipped++;
                                break;
                            }
                        }
                    }
                }
                
                break;
            } else if(!((((""+userAnswers[i][key]) == 'Yes') &&((""+answers[i][key])=='Yes')) || 
                    (((""+userAnswers[i][key]) != 'Yes') &&((""+answers[i][key])!='Yes')))){                
                isIncorrect = true;
                score.wrongAnswers++;
                if(answers[i].subject) {
                    var subjectDetail = subjectDetails[answers[i].subject.replace(/\W+/g, '')];
                    var chapterKeys = Object.keys(subjectDetail.chapters);

                    if(answers[i].chapterId){
                        for(k=0;k<chapterKeys.length;k++){
                            var chapter = subjectDetail.chapters[''+chapterKeys[k]];
                            
                            if(chapter.id==answers[i].chapterId){
                                chapter.wrongAnswers++;
                                break;
                            }
                        }
                    }
                }
                
                break;
            }
        }

        if(!isIncorrect){
            score.correctAnswers++;
            userAnswers[i].correctAnswer="true";
            
            if(answers[i].subject) {
                var subjectDetail = subjectDetails[answers[i].subject.replace(/\W+/g, '')];
                subjectDetail.correctAnswers++;
                var chapterKeys = Object.keys(subjectDetail.chapters);

                if(answers[i].chapterId){
                    for(k=0;k<chapterKeys.length;k++){
                        var chapter = subjectDetail.chapters[''+chapterKeys[k]];
                        
                        if(chapter.id==answers[i].chapterId){
                            chapter.correctAnswers++;
                            break;
                        }
                    }
                }
            }
        }

        isIncorrect = false;
    }
	
    return score;
}

function resetCheckBoxes(index){
    var ua = retrieveUserAnswerCheckBox(index)

    if(ua==undefined) {
        $('#ans1').prop('checked', false);
        $('#ans2').prop('checked', false);
        $('#ans3').prop('checked', false);
        $('#ans4').prop('checked', false);
        $('#ans5').prop('checked', false);
    } else {
        $('#ans1').prop('checked', ua.ans1);
        $('#ans2').prop('checked', ua.ans2);
        $('#ans3').prop('checked', ua.ans3);
        $('#ans4').prop('checked', ua.ans4);
        $('#ans5').prop('checked', ua.ans5);
        if("checkbox" != qa[index].optionType&&immediateFeedback){
            for(i=1;i<6;i++){
                var key;
                
                switch (i) {
                    case 1:
                        key = ua.ans1;
                        break;
                    case 2:
                        key = ua.ans2;
                        break;
                    case 3:
                        key = ua.ans3;
                        break;
                    case 4:
                        key = ua.ans4;
                        break;
                    case 5:
                        key = ua.ans5;
                }                
                
                if(key) radioAnswerChecked(index,i);
            }
        }
    }
}

function closeMCQWithAlert(){
    if(confirm("Do you want to close the practise session?")) {
        $("#quizModal").fadeOut(1000);
        $("#mainbookcontent").fadeIn(1000);
    }
}
function closeMCQ(){
    $("#quizModal").fadeOut(1000);
    $("#mainbookcontent").fadeIn(1000);

}