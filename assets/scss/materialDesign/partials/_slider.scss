.smart-books-slider {
  background-color: $white-color;
  position: relative;
  z-index: 100;
}
.slider-hash-tags {
  list-style: none;
  padding: 0;
  padding-left: 10px;
  margin: 0;
}
.slider-hash-tag-item {
  display: inline-block;
  margin-right: 38px;
}
.slider-hash-tag-link {
  display: block;
  font-weight: 300;
  font-size: $font-size-xl;
  letter-spacing: 0.01em;
  &:hover {
    text-decoration: none;
    color: $orange-color;
  }
}
.slider-books-heading {
  font-family: $font-family-abril;
  font-size: $font-size-xxxl;
  color: $font-color-more-light-grey;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  margin-bottom: 0;
}
.slider-books-wrapper {
  min-height: 380px;
  position: relative;
  top: -24px;
}
.slider-books-holder-down {
  position: absolute;
  top: 80px;
  left: 38px;
}
.slider-book {
  display: inline-block;
  max-width: 160px;
  max-height: 230px;
  margin-right: 40px;
  transform-style: preserve-3d;
  img {
    box-shadow: $slider-book-shadow;
    border-radius: $border-radius;
  }
}
.slider-book:last-child {
  margin-right: 0;
}
.slider-book-link {
  display: block;
  transform: translateZ(50px);
}

.slider-control {
  max-height: 400px;
  img {
    position: absolute;
    top: 50%;
    margin: 0 auto;
    border-radius: 50px;
    -webkit-transform: translateY(-50%);
       -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
         -o-transform: translateY(-50%);
            transform: translateY(-50%);
  }
} 
// @media screen and (max-width: 992px){
//   .slider-control {
//     img {
//       margin: 0 auto;
//     }
//   } 
// }

.carousel-showmanymoveone .carousel-control {
  width: 4%;
  background-image: none;
}
.carousel-inner .active.left  { left: -33%;             }
.carousel-inner .active.right { left: 33%;              }
.carousel-inner .next         { left: 33%               }
.carousel-inner .prev         { left: -33%              }
.carousel-control.left        { background-image: none; }
.carousel-control.right       { background-image: none; }
.carousel-inner .item         { background: white;      }