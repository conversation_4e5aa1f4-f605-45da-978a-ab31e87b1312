/*! Generated by <PERSON>ont Squirrel (https://www.fontsquirrel.com) on April 2, 2018 */



@font-face {
    font-family: 'nexa_blackregular';
    src: url('fontfabric_-_nexablack-webfont.woff2') format('woff2'),
         url('fontfabric_-_nexablack-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'nexa_boldregular';
    src: url('fontfabric_-_nexa-bold-webfont.woff2') format('woff2'),
         url('fontfabric_-_nexa-bold-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'nexa_bookregular';
    src: url('fontfabric_-_nexa-book-webfont.woff2') format('woff2'),
         url('fontfabric_-_nexa-book-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'nexa_book_italicregular';
    src: url('fontfabric_-_nexa-book-italic-webfont.woff2') format('woff2'),
         url('fontfabric_-_nexa-book-italic-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'nexa_heavyregular';
    src: url('fontfabric_-_nexaheavy-webfont.woff2') format('woff2'),
         url('fontfabric_-_nexaheavy-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'nexa_regular_italicitalic';
    src: url('fontfabric_-_nexa-regular-italic-webfont.woff2') format('woff2'),
         url('fontfabric_-_nexa-regular-italic-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'nexa_lightregular';
    src: url('nexa_light-webfont.woff2') format('woff2'),
         url('nexa_light-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}