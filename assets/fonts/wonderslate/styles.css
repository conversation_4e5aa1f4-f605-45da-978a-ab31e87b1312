@charset "UTF-8";

@font-face {
  font-family: "wonderslate";
  src:url("fonts/wonderslate.eot");
  src:url("fonts/wonderslate.eot?#iefix") format("embedded-opentype"),
    url("fonts/wonderslate.woff") format("woff"),
    url("fonts/wonderslate.ttf") format("truetype"),
    url("fonts/wonderslate.svg#wonderslate") format("svg");
  font-weight: normal;
  font-style: normal;

}

[data-icon]:before {
  font-family: "wonderslate" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "wonderslate" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
  content: "\61";
}
.icon-checkbox-deselected:before {
  content: "\62";
}
.icon-checkbox-selected:before {
  content: "\63";
}
.icon-search-light:before {
  content: "\64";
}
.icon-search-dark:before {
  content: "\65";
}
.icon-close:before {
  content: "\66";
}
.icon-comment:before {
  content: "\67";
}
.icon-done:before {
  content: "\68";
}
.icon-error-dark:before {
  content: "\69";
}
.icon-error-light:before {
  content: "\6a";
}
.icon-filter:before {
  content: "\6b";
}
.icon-help:before {
  content: "\6c";
}
.icon-text-format:before {
  content: "\6d";
}
.icon-list:before {
  content: "\6f";
}
.icon-sort:before {
  content: "\70";
}
.icon-settings:before {
  content: "\71";
}
.icon-radio-selected:before {
  content: "\72";
}
.icon-radio-deselected:before {
  content: "\73";
}
.icon-add:before {
  content: "\6e";
}
.icon-bookmark:before {
  content: "\74";
}
.icon-chevron:before {
  content: "\75";
}
.icon-dropdown:before {
  content: "\76";
}
.icon-favorite:before {
  content: "\77";
}
.icon-fullscreen:before {
  content: "\78";
}
.icon-grid:before {
  content: "\79";
}
.icon-hamburger:before {
  content: "\7a";
}
.icon-reload:before {
  content: "\41";
}
.icon-star-filled:before {
  content: "\43";
}
.icon-star:before {
  content: "\42";
}
.icon-info-outline-black:before {
  content: "\44";
}
.icon-fontsize-less:before {
  content: "\45";
}
.icon-fontsize-zoom:before {
  content: "\46";
}
.icon-letter-spacing-decrease:before {
  content: "\47";
}
.icon-letter-spacing-increase:before {
  content: "\48";
}
.icon-lineheight:before {
  content: "\49";
}
.icon-lineheight-1:before {
  content: "\4a";
}
