<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<script>
    var loggedIn = false;
</script>

<sec:ifLoggedIn>
    <script>
        loggedIn = true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="container-fluid publish-management" style="min-height: calc(100vh - 160px);">
    <div class="row justify-content-center p-4">
        <div class="col-md-9 align-left main p-4">
            <h4 for="publisherId">${pub.id ? "Update" : "Add"} Publisher</h4>

            <form method="post" enctype="multipart/form-data" action="/publisherManagement/updatePublisher"
                  class="text-left mt-3"
                  id="publisherDetailsForm">
                <input type="text" class="form-control hidden" name="id" id="id" value="${pub.id}"/>
                <input type="text" class="form-control hidden" id="urls" value="${puburls.join('~')}"/>

                <label for="pubName">Name</label>

                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="name" id="pubName" value="${pub.name}"
                           onchange="onPublisherNameChanged()"
                           placeholder="Publisher Name"/>
                </div>

                <label for="website">Website</label>

                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="website" id="website" value="${pub.website}"
                           placeholder="Publisher's Website URL"/>
                </div>

                <label for="contactPerson">Contact Person</label>

                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="contactPerson" id="contactPerson"
                           value="${pub.contactPerson}"
                           placeholder="Contact Person Name"/>
                </div>

                <label for="pubemail">Email</label>

                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="email" id="pubemail" value="${pub.email}"
                           placeholder="Contact Person Email"/>
                </div>

                <label for="pubmobile">Mobile</label>

                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="mobile" id="pubmobile" value="${pub.mobile}"
                           placeholder="Contact Person Mobile"/>
                </div>

                <label for="urlName">Url Name</label>

                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text">https://www.wonderslate.com/publisher/</span>
                    </div>
                    <input type="text" class="form-control" name="urlname" id="urlName" value="${pub.urlname}"
                           onchange="onUrlNameChanged()"
                           placeholder="URL Name"/>
                </div>

                <label for="pubcity">City</label>

                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="city" id="pubcity" value="${pub.city}"
                           placeholder="City"/>
                </div>

                <label for="pubstate">State</label>

                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="state" id="pubstate" value="${pub.state}"
                           placeholder="State"/>
                </div>

                <label for="pubcountry">Name</label>

                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="country" id="pubcountry" value="${pub.country}"
                           placeholder="Country"/>
                </div>

                <label for="pubtagline">Tagline</label>

                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="tagline" id="pubtagline" value="${pub.tagline}"
                           placeholder="Tagline"/>
                </div>

                <div class="row justify-content-center">
                    <div class="col h-100 w-100">
                        <label for="publogo">Logo</label>
                        <% if (pub.logo != "" && pub.logo != null) { %>
                        <img class="h-100 w-100 border rounded"
                             src="/publisherManagement/showPublisherImage?id=${pub.id}">
                        <% } else { %>
                        <div class="col text-center border rounded p-4">
                            <i class="material-icons">description</i>

                            <p>Logo Not Uploaded</p>
                        </div>
                        <% } %>

                        <div class="input-group mb-3">
                            <div class="custom-file mt-1">
                                <input class="custom-file-input" id="selectFile" name="file" type="file"
                                       accept="image/png, image/jpeg, image/gif ,image/svg"
                                       onchange="updatePublisherImage()">
                                <label class="custom-file-label" id="logoFileName">Choose file</label>
                            </div>
                        </div>

                        <p id="file-error" style="font-size: 12px;
                        margin-top: 0.5rem;
                        text-align: center;">Please Upload Image below 2mb</p>

                    </div>

                    <div class="col ">
                        <label for="pubBackgroundColor">Background Color</label>

                        <div class="input-group mb-3">
                            <input type="text" class="form-control" name="backgroundColor" id="pubBackgroundColor"
                                   value="${pub.backgroundColor}">

                            <div class="input-group-append">
                                <span class="input-group-text">
                                    <input type="color" id="colorPicker" onchange="selectColor()"
                                           value="${pub.backgroundColor ? pub.backgroundColor : "#ffffff"}"/>
                                </span>

                            </div>

                        </div>

                    </div>
                </div>
            </form>

            <button id="updatePub" class="btn btn-primary"
                    onclick="updatePublisherr()">${pub.id ? "Update" : "Add"}</button>

            <% if (pub.id) { %>
            <div>
                <h3 class="mt-4">Banner Management</h3>

                <form class="form-horizontal mt-4" enctype="multipart/form-data" role="form" name="uploadbanner1"
                      id="uploadbanner1" action="/pubdesk/addBanners" onsubmit="return addBanner(1)"
                      method="post">
                    <div class="form-group form-inline miguser">

                        <input type="hidden" name="bannerId" value="${bannersMstList[0] ? bannersMstList[0].id : ""}">
                        <input type="hidden" name="pubId" value="${pub.id}">
                        <input type="text" class="form-control col-md-3 m-2 border" name="name" id="name1"
                               value="${bannersMstList[0] ? bannersMstList[0].imageName : ""}" placeholder="Name"
                               autocomplete="off">
                        <input type="file" name="file" class="form-control col-md-3 m-2" name="image" id="image1"
                               value="${bannersMstList[0] ? bannersMstList[0].imagePath : ""}" accept="image/*">
                        <input type="text" class="form-control col-md-3 m-2" name="namename" id="namename1"
                               value="${bannersMstList[0] ? bannersMstList[0].imagePath : ""}" readonly="true"
                               autocomplete="off">
                        <% if (bannersMstList[0] != null) { %>
                        <input class="btn btn-sm btn-primary my-0 mr-2" type="submit" value="Update"><input
                            class="btn btn-sm btn-primary m-0" type="button" value="Delete"
                            onclick="deleteId(${bannersMstList[0].id});"><a href="showImage?fileName=${
                            bannersMstList [ 0 ] ? bannersMstList [ 0 ]. imagePath: ""}">view</a></button>
                        <% } else { %>
                        <input class="btn btn-sm btn-primary col-1 m-0" type="submit" value="Add">
                        <% } %>
                    </div>
                </form>

                <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadbanner2"
                      id="uploadbanner2"
                      action="/pubdesk/addBanners" onsubmit="return addBanner(2)" method="post">
                    <div class="form-group form-inline miguser">
                        <input type="hidden" name="bannerId" value="${bannersMstList[1] ? bannersMstList[1].id : ""}">
                        <input type="hidden" name="pubId" value="${pub.id}">
                        <input type="text" class="form-control col-md-3 m-2 border" name="name" id="name2"
                               value="${bannersMstList[1] ? bannersMstList[1].imageName : ""}" placeholder="Name"
                               autocomplete="off">
                        <input type="file" name="file" class="form-control col-md-3 m-2" name="image" id="image2"
                               value="${bannersMstList[1] ? bannersMstList[1].imagePath : ""}" accept="image/*">
                        <input type="text" class="form-control col-md-3 m-2" name="namename" id="namename2"
                               value="${bannersMstList[1] ? bannersMstList[1].imagePath : ""}" readonly="true"
                               autocomplete="off">
                        <% if (bannersMstList[1] != null) { %>
                        <input class="btn btn-sm btn-primary my-0 mr-2" type="submit" value="Update"><input
                            class="btn btn-sm btn-primary m-0" type="button" value="Delete"
                            onclick="deleteId(${bannersMstList[1].id});"><a
                            href="showImage?fileName=${
                                    bannersMstList [ 1 ] ? bannersMstList [ 1 ]. imagePath: ""}">view</a></button>
                        <% } else { %>
                        <input class="btn btn-sm btn-primary col-1 m-0" type="submit" value="Add">
                        <% } %>
                    </div>
                </form>

                <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadbanner3"
                      id="uploadbanner3"
                      action="/pubdesk/addBanners" onsubmit="return addBanner(3)" method="post">
                    <div class="form-group form-inline miguser">
                        <input type="hidden" name="bannerId" value="${bannersMstList[2] ? bannersMstList[2].id : ""}">
                        <input type="hidden" name="pubId" value="${pub.id}">
                        <input type="text" class="form-control col-md-3 m-2 border" name="name" id="name3"
                               value="${bannersMstList[2] ? bannersMstList[2].imageName : ""}" placeholder="Name"
                               autocomplete="off">
                        <input type="file" name="file" class="form-control col-md-3 m-2" name="image" id="image3"
                               value="${bannersMstList[2] ? bannersMstList[2].imagePath : ""}" accept="image/*">
                        <input type="text" class="form-control col-md-3 m-2" name="namename" id="namename3"
                               value="${bannersMstList[2] ? bannersMstList[2].imagePath : ""}" readonly="true"
                               autocomplete="off">
                        <% if (bannersMstList[2] != null) { %>
                        <input class="btn btn-sm btn-primary my-0 mr-2" type="submit" value="Update"><input
                            class="btn btn-sm btn-primary m-0" type="button" value="Delete"
                            onclick="deleteId(${bannersMstList[2].id});"><a
                            href="showImage?fileName=${
                                    bannersMstList [ 2 ] ? bannersMstList [ 2 ]. imagePath: ""}">view</a></button>
                        <% } else { %>
                        <input class="btn btn-sm btn-primary col-1 m-0" type="submit" value="Add">
                        <% } %>
                    </div>
                </form>

                <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadbanner4"
                      id="uploadbanner4"
                      action="/pubdesk/addBanners" onsubmit="return addBanner(4)" method="post">
                    <div class="form-group form-inline miguser">
                        <input type="hidden" name="bannerId" value="${bannersMstList[3] ? bannersMstList[3].id : ""}">
                        <input type="hidden" name="pubId" value="${pub.id}">
                        <input type="text" class="form-control col-md-3 m-2 border" name="name" id="name4"
                               value="${bannersMstList[3] ? bannersMstList[3].imageName : ""}" placeholder="Name"
                               autocomplete="off">
                        <input type="file" name="file" class="form-control col-md-3 m-2" name="image" id="image4"
                               value="${bannersMstList[3] ? bannersMstList[3].imagePath : ""}" accept="image/*">
                        <input type="text" class="form-control col-md-3 m-2" name="namename" id="namename4"
                               value="${bannersMstList[3] ? bannersMstList[3].imagePath : ""}" readonly="true"
                               autocomplete="off">
                        <% if (bannersMstList[3] != null) { %>
                        <input class="btn btn-sm btn-primary my-0 mr-2" type="submit" value="Update"><input
                            class="btn btn-sm btn-primary m-0" type="button" value="Delete"
                            onclick="deleteId(${bannersMstList[3].id});"><a
                            href="showImage?fileName=${
                                    bannersMstList [ 3 ] ? bannersMstList [ 3 ]. imagePath: ""}">view</a></button>
                        <% } else { %>
                        <input class="btn btn-sm btn-primary col-1 m-0" type="submit" value="Add">
                        <% } %>
                    </div>
                </form>

                <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadbanner5"
                      id="uploadbanner5"
                      action="/pubdesk/addBanners" onsubmit="return addBanner(5)" method="post">
                    <div class="form-group form-inline miguser">
                        <input type="hidden" name="bannerId" value="${bannersMstList[4] ? bannersMstList[4].id : ""}">
                        <input type="hidden" name="pubId" value="${pub.id}">
                        <input type="text" class="form-control col-md-3 m-2 border" name="name" id="name5"
                               value="${bannersMstList[4] ? bannersMstList[4].imageName : ""}" placeholder="Name"
                               autocomplete="off">
                        <input type="file" name="file" class="form-control col-md-3 m-2" name="image" id="image5"
                               value="${bannersMstList[4] ? bannersMstList[4].imagePath : ""}" accept="image/*">
                        <input type="text" class="form-control col-md-3 m-2" name="namename" id="namename5"
                               value="${bannersMstList[4] ? bannersMstList[4].imagePath : ""}" readonly="true"
                               autocomplete="off">
                        <% if (bannersMstList[4] != null) { %>
                        <input class="btn btn-sm btn-primary my-0 mr-2" type="submit" value="Update"><input
                            class="btn btn-sm btn-primary m-0" type="button" value="Delete"
                            onclick="deleteId(${bannersMstList[4].id});"><a
                            href="showImage?fileName=${
                                    bannersMstList [ 4 ] ? bannersMstList [ 4 ]. imagePath: ""}">view</a></button>
                        <% } else { %>
                        <input class="btn btn-sm btn-primary col-1 m-0" type="submit" value="Add">
                        <% } %>
                    </div>
                </form>

                <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadbanner6"
                      id="uploadbanner6"
                      action="/pubdesk/addBanners" onsubmit="return addBanner(6)" method="post">
                    <div class="form-group form-inline miguser">
                        <input type="hidden" name="bannerId" value="${bannersMstList[5] ? bannersMstList[5].id : ""}">
                        <input type="hidden" name="pubId" value="${pub.id}">
                        <input type="text" class="form-control col-md-3 m-2 border" name="name" id="name6"
                               value="${bannersMstList[5] ? bannersMstList[5].imageName : ""}" placeholder="Name"
                               autocomplete="off">
                        <input type="file" name="file" class="form-control col-md-3 m-2" name="image" id="image6"
                               value="${bannersMstList[5] ? bannersMstList[5].imagePath : ""}" accept="image/*">
                        <input type="text" class="form-control col-md-3 m-2" name="namename" id="namename6"
                               value="${bannersMstList[5] ? bannersMstList[5].imagePath : ""}" readonly="true"
                               autocomplete="off">
                        <% if (bannersMstList[5] != null) { %>
                        <input class="btn btn-sm btn-primary my-0 mr-2" type="submit" value="Update"><input
                            class="btn btn-sm btn-primary m-0" type="button" value="Delete"
                            onclick="deleteId(${bannersMstList[5].id});"><a
                            href="showImage?fileName=${
                                    bannersMstList [ 5 ] ? bannersMstList [ 5 ]. imagePath: ""}">view</a></button>
                        <% } else { %>
                        <input class="btn btn-sm btn-primary col-1 m-0" type="submit" value="Add">
                        <% } %>
                    </div>
                </form>

            </div>
            <% } %>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    var urls = document.getElementById('urls').value.split('~')
    if (document.getElementById("id").value) {
        var index = urls.indexOf(document.getElementById("urlName").value)
        urls.splice(index, 1);
        document.getElementById("urls").value = urls.join('~')

    }
    if (!document.getElementById('urlName').value) {
        document.getElementById("urlName").value =
            document
                .getElementById("pubName").value
                .toString()
                .toLowerCase()
                .replace(/ /g, "-")
                .replace(/\./g, "-")
    }

    function updatePublisherr() {
        onUrlNameChanged()
        var id = document.getElementById("id").value.toString().trim()
        var name = document.getElementById("pubName").value.toString().trim()
        var website = document.getElementById("website").value.toString().trim()
        var contactPerson = document.getElementById("contactPerson").value.toString().trim()
        var email = document.getElementById("pubemail").value.toString().trim()
        var mobile = document.getElementById("pubmobile").value.toString().trim()
        var urlname = document.getElementById("urlName").value.toString().trim()
        var city = document.getElementById("pubcity").value.toString().trim()
        var state = document.getElementById("pubstate").value.toString().trim()
        var country = document.getElementById("pubcountry").value.toString().trim()
        var tagline = document.getElementById("pubtagline").value.toString().trim()
        var backgroundColor = document.getElementById("pubBackgroundColor").value.toString().trim()
        if (name == "") {
            alert("Publisher Name cannot be empty.")
            return
        }
        var urlreg = /[A-Z]+/
        if (urlname == "" || urlname.match(urlreg) || urlname.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) > -1) {
            alert("Publisher Url must be all small characters and must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
            return
        }
        if (email.length > 0 && !validateEmail(email)) {
            alert("Enter Valid Email")
            document.getElementById("pubemail").value = ""
            return
        }
        if (mobile.length > 0 && mobile.length < 10 && !parseInt(mobile)) {
            alert('Enter Valid mobile')
            document.getElementById("pubmobile").value = ""
            return
        }
        var webreg = /[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/ig
        if (website.length > 0 && !webreg.test(website)) {
            alert('Enter Valid Website')
            document.getElementById("website").value = ""
            return
        }
        $('.loading-icon').removeClass('hidden');
        document.getElementById('publisherDetailsForm').submit();
    }

    const onPublisherNameChanged = () => {
        changed = true
        // document.getElementById("urlName").value==""
        if (true) {
            document.getElementById("urlName").value =
                document
                    .getElementById("pubName").value
                    .toString()
                    .toLowerCase()
                    .replace(/ /g, "-")
                    .replace(/\./g, "-")
            onUrlNameChanged()
        }
    }
    const onUrlNameChanged = () => {
        document.getElementById('urlName').value = document.getElementById('urlName').value.toLowerCase().replace(/ /g, "-").replace(/\./g, "-")
        document.getElementById('urls').value.split("~").forEach((u, i) => {
            if (u == document.getElementById('urlName').value) {
                alert("Url Not Available")
                document.getElementById('urlName').value = ""
            }
        })
    }

    function validateEmail(field) {
        var regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,5}$/;
        return (regex.test(field)) ? true : false;
    }

    function updatePublisherImage() {
        var oFile = document.getElementById("selectFile").files[0]; // <input type="file" id="fileUpload" accept=".jpg,.png,.gif,.jpeg"/>

        if (oFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g) > -1) {
            alert("File Name must not contain any space and special characters \n < > { } # / \\ ? : ! & $ ^ * % ` | + ")
            document.getElementById("selectFile").value = ""
            return
        }

        if (oFile.size > 2097152) // 2 mb for bytes.
        {
            $('#file-error').css('color', 'red');
            return;
        }
        document.getElementById("logoFileName").innerText = oFile.name
    }

    function addBanner(index) {
        if ((document.getElementById("name" + index).value == "") || (document.getElementById("image" + index).value == "" && document.getElementById("namename" + index).value == "")) {
            alert("Please enter the name and image");
            return false;
        } else {
            alert("Saved successfully");
        }
    }

    function deleteId(id) {
        <g:remoteFunction controller="pubdesk" action="deleteBannerById" params="'id='+id" onSuccess = "bannerDeleted(data);"/>
    }

    function bannerDeleted(data) {
        if (data.status == "OK") {
            alert("Deleted successfully");
            location.reload();
        }
    }

    function openColorPicker() {
        // document.getElementById('colorPicker').removeClass('hidden')
        $('#colorPicker').trigger('click')
    }

    function selectColor() {
        document.getElementById('pubBackgroundColor').value = document.getElementById('colorPicker').value
    }
</script>
