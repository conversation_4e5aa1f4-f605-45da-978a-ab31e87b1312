<!DOCTYPE html>
<html>
<head>
    <title>Arivupro</title>
    <asset:stylesheet src="forms/dashboard.css"/>
    <asset:stylesheet src="forms/main.css"/>
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.1.0/css/all.css" integrity="sha384-lKuwvrZot6UHsBSfcMvOkWwlCMgc0TaWr+30HWe3a4ltaBwTZhyTEggF5tJv8tbt" crossorigin="anonymous">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

</head>
<body>
<div data-ng-controller="headerCtrl">
    <header class="main-header">
        <div class="skip">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <ul class="skip list-inline">
                            <li><a tabindex="-1" class="accessible" href="#">Skip to Main Content</a></li>
                            <li class="high-low"><i class="fa fa-adjust"></i></li>
                            <li class="fresize f-up">A<sup>+</sup></li>
                            <li class="fresize f-down">A<sup>-</sup></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="row branding">
                <div class="col-xs-12">
                    <a href="#" title="Goods and Services Tax Home">
                        <img class="logo" src="${assetPath(src: 'emblem.png')}" alt="Goods and Services Tax Home">
                    </a>
                    <h1 class="site-title"><a href="#">Goods and Services Tax</a></h1>
                    <ul class="list-inline mlinks">
                        <!----><li ng-if="!udata">
                        <a target="_self" href="#"><i class="fa fa-sign-in"></i> Login</a>
                    </li><!---->
                    <!---->
                    <!---->
                    </ul>
                    <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#main">
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="sr-only">Toggle navigation</span>
                    </button>
                </div>
            </div>
        </div>
    </header>
    <nav class="navbar navbar-default collapsed">
        <div class="container">
            <div id="main" class="navbar-collapse collapse">
                <ul class="nav navbar-nav">
                    <li class="active">
                        <a class="nav_home" href="#">Dashboard</a>
                    </li>
                    <li class="dropdown drpdwn">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false" target="_self">Services <span class="caret"></span></a>
                        <ul class="dropdown-menu smenu" role="menu">
                            <li class="has-sub">
                                <a href="#" target="_self" class="ng-hide">Registration</a>
                                <ul class="isubmenu serv">
                                    <li>
                                        <a href="#" target="_self">New Registration</a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav><!---->
<!----></ng-include>
</div>
<div class="content-wrapper">
    <div class="container">
        <div class="mypage">
            <div class="row">
                <div class="col-xs-10">
                    <div>
                        <ol class="breadcrumb">
                            <li>
                                <a target="" href="#">Dashboard</a>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="col-xs-2">
                    <div class="lang dropdown">
                        <span class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">English</span>
                        <ul class="dropdown-menu lang-dpdwn">
                            <li>
                                <a>English</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="content-pane" style="min-height: 568.2px;">
                <div class="mysavedapp">
                    <span>My Saved Application</span>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="table-custom">
                            <table class="table table-hover table-bordered">
                                <tr>
                                    <th class="th">Creation Date</th>
                                    <th class="th">Form</th>
                                    <th class="th">Form Description</th>
                                    <th class="th">Expiry Date</th>
                                    <th class="th">Status</th>
                                    <th class="th">Action</th>
                                </tr>
                                <tr>
                                    <td class="th">${dateCreated}</td>
                                    <td class="th">GST REG-01</td>
                                    <td class="th">Application for New Registration</td>
                                    <td class="th">${dateExpiry}</td>
                                    <td class="th">Draft <i class="fas fa-info-circle" style="color:#2c4e86"></i></td>
                                  <!--  <td class="th"><button type="edit" class="btnstyle">   <span style="color: white;">&#128393;</span></button></td> -->
                                    <td class="th"><a href="detailedForm">Edit</a> </td>
                                </tr>
                            </table>
                        </div>
                        <div class="trackappstatus">
                            <p>Track Application Status</p>
                        </div>
                        <div>
                            <p class="p">You do not have any submitted applications</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<footer ng-controller="footerCtrl">
    <div class="expbtn">
        <i ng-click="fexpand()" ng-class="{'fa-angle-up': expanded, 'fa-angle-down': !expanded}" class="fa fa-angle-down" title="Expand/Collapse Footer"></i>
    </div>
    <div class="ifooter " id="demo">
        <!----><ng-include src="'/pages/common/footer.html'"><!----><div class="f1" data-ng-if="!expanded">
        <div class="container">
            <div class="row">
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/about/gst/history" href="//www.gst.gov.in/about/gst/history">About GST</a>
                    <ul>
                        <!--<li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="{{servers.GST_CONTENT_R1_URL}}/about/vision">Vision and Mission</a></li>
          <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="{{servers.GST_CONTENT_R1_URL}}/about/citizencharter">Citizen Charter</a></li>-->
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/about/gst/council" href="//www.gst.gov.in/about/gst/council">GST Council Structure</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/about/gst/history" href="//www.gst.gov.in/about/gst/history">GST History</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/website" href="//www.gst.gov.in/policies/website">Website Policies</a>
                    <ul>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/website" href="//www.gst.gov.in/policies/website">Website Policy</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/hyperlink" href="//www.gst.gov.in/policies/hyperlink">Hyperlink Policy</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/disclaimer" href="//www.gst.gov.in/policies/disclaimer">Disclaimer</a></li>
                    </ul>

                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help/relatedsites" href="//www.gst.gov.in/help/relatedsites">Related Sites</a>
                    <ul>
                        <li><a data-popup="true" data-ng-href="http://www.cbec.gov.in/" href="http://www.cbec.gov.in/">Central
                        Board of Excise and Customs</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help/statevat" href="//www.gst.gov.in/help/statevat">State
                        Tax Websites</a></li>
                        <li><a data-popup="true" data-ng-href="//india.gov.in/" href="//india.gov.in/">National
                        Portal</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-2 col-xs-12 help no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help" href="//www.gst.gov.in/help">Help</a>
                    <ul>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/system/" href="//www.gst.gov.in/system/">System
                        Requirements</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help/helpmodules/" href="//www.gst.gov.in/help/helpmodules/">User Manuals, Videos and FAQs</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/docadvisor/" href="//www.gst.gov.in/docadvisor/">Documents Required for Registration</a></li>
                        <li><a data-popup="true" data-ng-href="https://www.youtube.com/c/GoodsandServicesTaxNetwork" href="https://www.youtube.com/c/GoodsandServicesTaxNetwork">GST Media</a></li>
                        <li><a class="disabled" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/sitemap" href="//www.gst.gov.in/sitemap">Site Map</a></li>
                        <!--<li><a target="_blank" data-ng-href="{{servers.GST_SERVICES_R1_URL}}/services/track-provisional-id-status">Track Provisional ID</a></li>-->
                    </ul>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 cont no-mobile scl">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/contact" href="//www.gst.gov.in/contact">Contact Us</a>
                    <ul>
                        <li>
                            <span class="contact">Help Desk Number: <br>0120-4888999</span>
                        </li>
                        <!-- <li>
            <a data-popup="true" data-ng-if="!udata" data-ng-href="//www.entrust.com/ssl-certificates/" target="_blank" data-ng-click="seal()"><img src="{{servers.GST_CONTENT_R1_URL}}\uiassets\images\entrustSeal.png" alt="Loading..." ></a>
          </li> -->
                        <li>
                            <span class="contact">Log/Track Your Issue:<br><a href="https://selfservice.gstsystem.in/" title="Grievance Redressal Portal for GST" target="_blank">Grievance Redressal Portal for GST</a></span>
                        </li>
                        <li class="social">
                            <a data-popup="true" href="//www.facebook.com/Goods-and-Services-Tax-1674179706229522/?fref=ts" title="Facebook"><i class="fa fa-facebook-square"></i>.</a>
                            <a data-popup="true" href="//www.youtube.com/channel/UCFYpOk92qurlO5t-Z_y-bOQ" title="Youtube"><i class="fa fa-youtube-play"></i>.</a>
                            <a data-popup="true" href="//twitter.com/askGSTech"><i class="fa fa-twitter" title="Twitter"></i>.</a>
                            <a data-popup="true" href="//www.linkedin.com/company/13204281?trk=tyah&amp;trkInfo=clickedVertical%3Acompany%2CclickedEntityId%3A13204281%2Cidx%3A4-2-9%2CtarId%3A1478268606810%2Ctas%3AGoods%20and%20Services%20" title="Linkedin"><i class="fa fa-linkedin"></i>.</a>
                        </li>
                        <!---->
                    </ul>
                </div>
            </div>
        </div>
    </div><!---->
        <div class="f2">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <p>© 2016-17 Goods and Services Tax Network</p>
                        <p>Site Last Updated on 23-05-2018</p>
                        <p>Designed &amp; Developed by GSTN</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="f3">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <p class="site">Site best viewed at 1024 x 768 resolution in Internet Explorer 10+, Google Chrome 49+, Firefox 45+ and Safari 6+</p>
                    </div>
                </div>
            </div>
        </div>
        <style>
        .disabled{
            cursor: not-allowed !important;
            opacity: 0.6;
        }
        </style>
        <script>
            $(document).on('click', '.disabled', function(){
                return false;
            });
        </script></ng-include>
    </div>
</footer>
<asset:javascript src="jquery-1.11.2.min.js"/>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
</body>
</html>