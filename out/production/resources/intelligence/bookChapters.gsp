<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/bookgpt/externalgpt.css">
    <title>${bookTitle}</title>
</head>

<body>
<div id="loader" class="loader-container">
    <div class="app-loader"></div>
</div>
<div class="container">
    <button id="show-chapters-btn" class="show-chapters-btn">Show Chapters</button>
    <div class="book-detail-page">
        <div class="chapters-list">
            <h4 class="book-title">${bookTitle}</h4>
            <div id="chapters-list"></div>
        </div>
        <div class="chapter-details" >
            <h4 id="sec-title">Select a chapter to view details</h4>
            <div id="spinner" class="spinner" style="display: none;"></div>
            <div id="chapter-details"></div>
        </div>
    </div>
</div>
<script>
    const bookId = "${params.bookId}";
    const secretKey = "${secretKey}";
    const chaptersListElement = document.getElementById("chapters-list");
    const chapterDetailsElement = document.getElementById("chapter-details");
    const spinnerElement = document.getElementById("spinner");
    const loader = document.getElementById("loader");
    let chapterName = "";
    let chapterId = null;
    let sitename = "${session['entryController']}"
    <%if("true".equals(session["commonWhiteLabel"])){%>
        sitename = "${session["siteName"]}"
    <%}%>
    async function fetchChapters() {
        try {
            const response = await fetch("/wonderpublish/getChaptersList?bookId="+bookId);
            const data = await response.json();

            if (data && data.chapters) {
                loader.style.display = "none";
                renderChaptersList(data.chapters);
                chapterId = data.chapters[0].chapterId;
                await loadChapterDetails(data.chapters[0].chapterId); // Load the first chapter by default
            } else {
                chaptersListElement.innerHTML = "<p>No chapters found.</p>";
            }
        } catch (error) {
            console.error("Error fetching chapters:", error);
            chaptersListElement.innerHTML = "<p>Failed to load chapters.</p>";
        }
    }

    function renderChaptersList(chapters) {
        chaptersListElement.innerHTML = "";
        chapters.forEach(function (chapter, index) {
            const chapterItem = document.createElement("div");
            chapterItem.classList.add("chapter-item");
            chapterItem.textContent = chapter.name;

            if (index === 0) {
                chapterItem.classList.add("active");
                chapterName = chapter.name;
            }

            chapterItem.addEventListener("click", function () {
                document.querySelectorAll(".chapter-item").forEach(item => item.classList.remove("active"));
                chapterItem.classList.add("active");
                chapterName = chapter.name;
                chapterId = chapter.chapterId;
                loadChapterDetails(chapter.chapterId);
            });

            chaptersListElement.appendChild(chapterItem);
        });
    }

    async function loadChapterDetails(chapterId) {
        try {
            spinnerElement.style.display = "block";
            chapterDetailsElement.innerHTML = "";

            const response = await fetch("/intelligence/getGptChapterDetails?chapterId="+chapterId);
            const result = await response.json();

            if (result) {
                await renderChapterDetails(result.data[0].gptContents);
            } else {
                chapterDetailsElement.innerHTML = "<p>No details available for this chapter.</p>";
            }
        } catch (error) {
            console.error("Error fetching chapter details:", error);
            chapterDetailsElement.innerHTML = "<p>Failed to load chapter details.</p>";
        } finally {
            spinnerElement.style.display = "none";
        }
    }

    async function renderChapterDetails(contents) {
        document.getElementById("sec-title").textContent = chapterName;
        if (!contents || contents.length === 0) {
            chapterDetailsElement.innerHTML = "<p>No GPT content available for this chapter.</p>";
            return;
        }

        const table = document.createElement("table");
        const thead = document.createElement("thead");
        const headerRow = document.createElement("tr");

        ["Resource Type", "Resource Link", "Copy Link"].forEach(headerText => {
            const th = document.createElement("th");
            th.textContent = headerText;
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        const tbody = document.createElement("tbody");

        contents = enrichContents(contents)
        for (const content of contents) {
            const row = document.createElement("tr");

            const td1 = document.createElement("td");
            td1.textContent = content.promptLabel;
            row.appendChild(td1);

            const td2 = document.createElement("td");
            const link = document.createElement("a");
            try {
                const resourceUrl = await constructResourceLink(content);
                link.href = resourceUrl;
                link.textContent = "View";
                link.target = "_blank";
            } catch (error) {
                console.error("Error constructing resource link:", error);
                link.textContent = "Error";
            }
            td2.appendChild(link);
            row.appendChild(td2);

            const td3 = document.createElement("td");
            const copyButton = document.createElement("button");
            copyButton.textContent = "Copy";
            copyButton.className = "copy-button";
            copyButton.addEventListener("click", function () {
                copyToClipboard(link.href);
                showCopyAnimation(copyButton);
            });
            td3.appendChild(copyButton);
            row.appendChild(td3);

            tbody.appendChild(row);
        }

        table.appendChild(tbody);
        chapterDetailsElement.appendChild(table);
    }
    function enrichContents(contents) {
        const filteredContents = contents.filter(
            item => !["flashcards", "flashcard"].includes(item.promptType)
        );

        const baseContent = [...filteredContents];

        if (filteredContents.some(item => ["qna", "mcq"].includes(item.promptType))) {
            baseContent.push({
                promptType: "giveTest",
                promptLabel: "Create Question Paper",
                readingMaterialResId: filteredContents[0].readingMaterialResId
            });
        }

        baseContent.push({
            promptType: "gpt_chat",
            promptLabel: "Chat",
            readingMaterialResId: filteredContents[0].readingMaterialResId
        });

        return baseContent;
    }

    async function constructResourceLink(content) {
        let resourceUrl = "/intelligence/gptContent?promptType="+content.promptType+"&readingMaterialResId="+content.readingMaterialResId+"&key="+secretKey+"&siteId=${params.siteId}";
        if (content.promptType.toLowerCase() === "mcq" || content.promptType.toLowerCase().includes("mcq")) {
            const { quizId, resId } = await getMCQDetails(content.readingMaterialResId);
            resourceUrl = "/prepjoy/prepJoyGame?quizId="+quizId+"&resId="+resId+"&quizType=testSeries&source=web&siteName="+sitename+"&learn=false&fromExternal=true&pubDesk=false&dailyTest=false&fromgpt=true";
        }
        if (content.promptType.toLowerCase() === "gpt_chat") {
            resourceUrl = "/intelligence/chat?resId="+content.readingMaterialResId+"&chapterId="+chapterId+"&bookId="+bookId+"&key="+secretKey+"&siteId=${params.siteId}";
        }
        return resourceUrl;
    }

    async function getMCQDetails(readingMaterialResId) {
        const response = await fetch("/prompt/getGPTsForResource?resId="+readingMaterialResId+"&promptType=mcq");
        if (!response.ok) throw new Error("Something went wrong");
        const result = await response.json();
        return { quizId: result.quizId, resId: result.resId };
    }

    function copyToClipboard(text) {
        const textarea = document.createElement("textarea");
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand("copy");
        document.body.removeChild(textarea);
    }

    function showCopyAnimation(button) {
        button.textContent = "Copied!";
        button.classList.add("copied");
        setTimeout(() => {
            button.textContent = "Copy";
            button.classList.remove("copied");
        }, 2000);
    }

    document.addEventListener("DOMContentLoaded", fetchChapters);

    document.getElementById('show-chapters-btn').addEventListener('click', function () {
        const chaptersList = document.querySelector('.chapters-list');
        const button = document.getElementById('show-chapters-btn');
        if (chaptersList.style.left === '0px') {
            chaptersList.style.left = '-100%';
            button.textContent = 'Show Chapters';
        } else {
            chaptersList.style.left = '0';
            button.textContent = 'Close Chapters';
        }
    });

    loader.style.display = "flex";
</script>
</body>
</html>