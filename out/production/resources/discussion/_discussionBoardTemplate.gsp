<asset:javascript src="sharer.min.js"/>
%{--<asset:stylesheet href="wonderslate/doubts.css" async="true"/>--}%
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.3/css/bootstrap-select.css" />

<script src="/assets/katex.min.js"></script>
<asset:stylesheet href="katex.min.css"/>
<script src="/assets/auto-render.min.js"></script>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<script type="text/x-mathjax-config">
MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>
<style>
.cke_button__source_label {
    display: none !important;
}
.doubts-section .filter-wrapper .row{
    justify-content: flex-start !important;
}
.clearFilter{
    background: transparent !important;
    color: #949494;
    font-weight: 500;
}
</style>
<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="modal modal-modifier fade postmodals" id="answer-successModal">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <div class="circle_around">
                    <i class="material-icons">check</i>
                </div>
                <h5 class="mt-2 mb-0">Your Answer has been sent to review.</h5>
                <p>Once approved you’ll be notified.</p>

                <div class="d-flex justify-content-end justify-content-center py-3">
                    <button type="button" class="btn btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-3" data-dismiss="modal">Okay</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal modal-modifier fade postmodals" id="question-successModal">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <div class="circle_around">
                    <i class="material-icons">check</i>
                </div>
                <h5 class="mt-2 mb-0">Your Question has been sent to review. </h5>
                <p>Once approved you’ll be notified.</p>

                <div class="d-flex justify-content-end justify-content-center py-3">
                    <button type="button" class="btn btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-3" data-dismiss="modal">Okay</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal modal-modifier fade postmodals" id="question-deleteSuccessModal">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <div class="circle_around">
                    <i class="material-icons">check</i>
                </div>
                <h5 class="mt-2 mb-0">Question deleted successfully. </h5>

                <div class="d-flex justify-content-end justify-content-center py-3">
                    <button type="button" class="btn btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-3" onclick="closeDeleteModal()">Okay</button>
                </div>
            </div>

        </div>
    </div>
</div>

<section class="page-main-wrapper mdl-js pb-5 pt-0 sectionHeight doubts-section">
    <div  class="container">
        <div class="doubt-menu pt-4 px-2">

            <div class="d-lg-flex align-items-center">

                                <div class="d-flex align-items-center">

                                    <button type="button" id="backToDoubts" onclick="javascript:showTab('nav-child-one');" style="display:none;" class="btn btn-outline-primary btn-outline-primary-modifier border-0"><i class="material-icons">backspace</i> </button>
                                    <div class="mdl-tooltip" data-mdl-for="backToDoubts">Back</div>
                                    <div class="page_title">
                                        <h3 id="doubt-text">
                                            <span class="d-flex align-items-center">
                                                <button id="goBack" class="material-icons border-0 mr-1 go-back-btn app-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
                                                <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
                                                <strong>Doubts</strong>
                                            </span>
                                        </h3>
                                    </div>
                                    <a href="javascript:askDoubt();" class="btn btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect ml-2 ask-doubt-btn d-lg-none">
                                        <span>Ask a Doubt</span>
                                    </a>
                                    <button class="search-ws btn btn-outline-primary btn-outline-primary-modifier rounded-circle ml-2">
                                        <i class="material-icons">search</i>
                                    </button>
                                    <button class="btn clearFilter" disabled onclick="clearFilters()">Clear filter</button>
                                </div>


            </div>

            <div class="input-group mt-2 col-12 col-lg-9 px-0" id="doubt-search">
                <input autocomplete="off" id="search-book" type="text" placeholder="Search questions" class="search form-control form-control-modifier bg-light border border-right-0 typeahead">
                <div class="input-group-append">
                    <button onclick="submitSearch()" class="search-btn btn border border-left-0 text-primary text-primary-modifier bg-light">
                        <i class="material-icons">search</i>
                    </button>
                </div>
            </div>

            <ul class="nav nav-tabs mt-2 mt-lg-4">
                <button class="askdoubt d-none d-lg-block  btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect" onclick="javascript:askDoubt();">
                    Ask a Doubt
                </button>
                <li id="alldoubts-tab" class="nav-item">
                    <a class="nav-link active" id="nav-child-one" data-toggle="tab" href="#alldoubts">All Doubts</a>
                </li>
                <li id="mydoubts-tab" class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#mydoubts">My Doubts</a>
                </li>
                <li id="myAnswer-tab" class="nav-item">
                    <a class="nav-link" data-toggle="tab" href="#myanswers">My Answers</a>
                </li>
                <li id="mobile-filter" class="nav-item d-lg-none" data-toggle="collapse" data-target="#collapseFilter" aria-expanded="false" aria-controls="collapseFilter">
                    <i class="material-icons">filter_list</i>
                </li>

            </ul>
            <div class="collapse d-lg-block" id="collapseFilter">
                <div class="row pl-4 pr-4 pt-0 pb-0">
                    <div class="col-12 col-lg-12 mt-0 mt-lg-2">
                        <div class="filter-wrapper d-lg-block" id="main-filters" >
                            <div class="row flex-wrap pb-3" id="rowFilters">
                                <select id="class" data-show-subtext="true" data-show-subtext="true" data-live-search="true" onchange="classChanged(this)" class="selectpicker d-none form-control form-control-modifier selected mt-2 form-select col-4 mr-3">
                                        <option class="dropdown-item" value="" selected="selected">Select Class</option>
                                    <g:each in="${institutes}" var="institute" status="i">
                                        <option class="dropdown-item" class="inst-${institute.id}" data-value="${institute.batchId}">${institute.name}</option>
                                    </g:each>
                                </select>
                                <g:select data-show-subtext="true" data-live-search="true" name="subject" id="subject" from="${subjects}" optionKey="name" optionValue="name"
                                          class='selectpicker form-control selected col-4 mt-2 mr-3' onchange="javascript:subjectChanged(this);" noSelection="['':'Choose Subject']" data-none-results-text="Did not find any match"/>

                                <select id="answerFilter" onchange="answerFilterChanged(this)" class="form-control form-control-modifier selected mt-2 form-select col-4">
                                    <option value="all" selected="selected">All</option>
                                    <option value="answered" >Answered</option>
                                    <option value="unanswered" >Unanswered</option>
                                </select>
                            </div>
%{--                                <button class="filter d-lg-none " type="button"  style="visibility: hidden">--}%
%{--                                    <span class="badge badge-light filter-count" ></span>--}%
%{--                                    <i class="material-icons">filter_alt</i>--}%
%{--                                </button>--}%
                            </div>
                        </div>
                        <div style="display: none" class="card" id="image-preview">
                            <img id="q-image-preview" style="width: 100%;" src="#" alt="your image" />
                            <button type="button" class="close-img" onclick="removeSelectedQuestionImg()" > <i class="material-icons">cancel</i> </button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <!-- Tab panes -->

        <div class="mobile-drop row m-0 justify-content-center">
            <div class="col-12 col-lg-9 tabs">
                <div class="tab-content">
                    <div id="alldoubts" class="container tab-pane active">
                        <div class="row justify-content-center">
                            <div class="col-12 col-lg-9 col-md-9 col-sm-12 reset-padding">
                                <div id="allTab">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="mydoubts" class="container tab-pane fade">
                        <div class="row justify-content-center">
                            <div class="col-12 col-lg-9 col-md-9 col-sm-12 reset-padding">
                                <div id="myDoubtsTab">

                                </div>
                            </div>
                        </div>

                    </div>
                    <div id="myanswers" class="container tab-pane fade">
                        <div id="myAnswerTab" class='row' style="margin-left: 0px; margin-right: 0px;">

                        </div>
                    </div>
                    <input id="pagination-currentpage-count" value="0" hidden />
                    <div id="pagination-div" class="text-center mt-3"> </div>
                </div>
                <div class="row posting-question p-0" style="display: none;">
                    <div class="row col-12 px-0 no-gutters">
                        <div class="card answer-card" id="queSave-modal">
                        </div>
                    </div>
                </div>

            </div>
            <div class="col-12 col-lg-9">
                <p id="searchError" class="text-center" style="display:none;">No doubts found.</p>
            </div>
        </div>


    </div>
    <div id="mobileque-modal" class="d-lg-none">

    </div>
</section>

<div class="modal fade" id="image-modal">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body">
                <div id="image-modal-body" ></div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" id="image-modal-close" class="btn btn-danger btn-danger-modifier mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-3" data-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>


<div class="modal" id="tags-modal" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
            </div>
            <!-- Modal body -->
            <div class="modal-body pt-0">
                <div id="tags-modal-body">
                    <h4 class="tag-text">Please select the subject of this Question.</h4>
                    <p class="tag-text">This will help others to find your question easily.</p>

                    <div id="filters-tags">

                        <div class="d-flex flex-nowrap" id="rowFilters">

                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal footer -->

            <div class="modal-footer justify-content-end" id="tags-modal-footer">
                <button type="button" id="tags-modal-close" onclick="closeTagsModal()" class="btn btn-flashcard d-none">Skip</button>
            </div>

        </div>
    </div>
</div>

<div id="relatedDoubts" class="books-list">

<g:render template="/resources/relatedBooks"></g:render>
</div>

<g:render template="/books/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>

<script>
    var currentUser = "${user}";
    var subjects = [];
    var siteId = "${session['siteId']}";
    var appType = "${session['appType']}";
    var tagsModalOpened = 0;
    var fileObj = {};
    var allQuestionsList = [];
    var myDoubtsList = [];
    var myAnswerList = {};
    var recordsPerPage = 20;
    var recordsPerBatch = 100;
    var batchIndex = 0;
    var currentTab = "all";
    var count = 0;
    var currentPage = "";
    var parentPageFIlter = "${parentPageFilter}";
    var tags = new Array(45);
    var paramFilter =  "${params.filter}";
    var instituteId = "${params.instituteId}";
    var batchId="";
    var instituteDoubt=false;
    var isInstituteAdmin=false;
    //to differentiate normal doubts and institute doubts
    if (instituteId!=""&& instituteId!=null && instituteId!=undefined){
        instituteDoubt=true;
    }

    if(appType == "android" || appType == "ios"){
      $('#relatedDoubts').hide();
    }
    if(paramFilter == "" || paramFilter == undefined || paramFilter == null){
        if(parentPageFIlter != "" || parentPageFIlter != undefined || parentPageFIlter != null) paramFilter = parentPageFIlter;
    }
    var openParams =  "${params.open}";
    var qId= "${params.qId}";
    if((openParams != "" && openParams != undefined && openParams == 'questionExplorer' ) || (qId.split("__")[1] != undefined && qId.split("__")[1] == "questionExplorer")) {
       if(qId.split("__") > 4){
           var userName = $('<textarea/>').html(qId.split("__")[2]).text();
        <g:remoteFunction controller="usermanagement" action="addPoints"  params="'reason=questionShared&points=2&scoreTypeId=1&userName='+userName" />
       }
        showQuestionExplorer('allTab',0,'',parseInt(qId.split("__")[0]));
    }if(paramFilter != "" && paramFilter != undefined) {
        tags[0] = paramFilter;
        getFilteredData();
    }else getDoubts("all");
    var schoolCategories = [];
    var collegeCategories = [];
    var examCategories = [];
    if(paramFilter == "" || paramFilter == undefined || paramFilter == null){
        schoolCategories = JSON.parse("${School}".replace(/&quot;/g,'"'));
        collegeCategories = JSON.parse("${College}".replace(/&quot;/g,'"'));
        examCategories =JSON.parse("${CompetitiveExams}".replace(/&quot;/g,'"'));
    }else {
        $('#main-filters').attr("style", "display: none !important");
    }

    navigator.getUserMedia = ( navigator.getUserMedia ||
        navigator.webkitGetUserMedia ||
        navigator.mozGetUserMedia ||
        navigator.msGetUserMedia);

    var video;
    var webcamStream;

    //modal window for post answer

    function submitPost() {
        $('#postmodal').show();
        $('.modalBackdrop').show();
    }
    function dismissModal() {
        $('#postmodal').hide();
        $('.modalBackdrop').hide();
        $('#done').hide();
    }

    function askDoubt() {
        if(appType == "android"){
            callAndroidAddQuestion();
        }else if(appType == "ios"){
            callIOSAddQuestion();
        }else if(currentUser != null && currentUser != "" && currentUser != undefined){
            $('.sectionHeight .nav-tabs,#doubt-search,.askdoubt,.tab-content, .search-ws, .new-folder').hide();
            $('.posting-question').show().addClass('mt-4');
            $('.mobile-footer').hide();
            $('#backToDoubts').css('display', 'flex');
            $('#doubt-search').hide();
            $('#doubt-text').html('<strong>Ask Doubts</strong>');
            $('#main-filters').attr("style", "display: none !important");
            $('.doubt-menu').addClass('pb-3');
            $("#searchError").hide();
            $(".clearFilter").hide();
            if($(window).width()<767){
                $('.mobile-drop').css('margin-top','65px');
            }
            if($(window).width()<991){
                $('.ask-doubt-btn').attr("style", "display: none !important");
            }
            var htmlStr=
                "<textarea id=\"questionText\" class=\"answer-textarea\" name=\"questionText\"></textarea>\n" +
                "" + "                        <div class=\"answer-actions\">\n" +
                "                            <input onchange='questionFileChanged()' style=\"display: none\" id=\"questionFile\" name=\"questionFile\" type=\"file\" accept=\"image/png, image/jpeg, image/gif ,image/svg\" >\n" +
                "                            <button onclick=\"questionAttachFileClicked()\" >" ;
            if($(window).width() < 767) htmlStr = htmlStr + " <i class=\"material-icons\">attach_file</i>\n" ;
            else htmlStr = htmlStr + " <i class=\"material-icons\">attach_file</i>";
            htmlStr = htmlStr + " </button>\n" +
                "                            <button type='button' class=\"btn btn-default cancels\" onclick=\"javascript:showTab('nav-child-one');\">Cancel</button>\n" +
                "                            <button type='button' class=\"btn btn-default post\" onclick=\"javascript:submitQuestion();\">Post</button>\n" +
                "                        </div>";
            document.getElementById('queSave-modal').innerHTML=htmlStr;
            var classDr = document.getElementById("class"),
                cloneClass = classDr.cloneNode(true);
            cloneClass.id = "addQuestionClass";
            var subjectDr = document.getElementById('subject'),
                clone = subjectDr.cloneNode(true); // true means clone all childNodes and all event handlers
            clone.id = "addQuestionSubject";
            if(paramFilter == "" || paramFilter == undefined || paramFilter == null) {
                $(clone).insertAfter(document.getElementById("questionText"));
                document.getElementById("addQuestionSubject").removeAttribute("onchange");
                $(cloneClass).insertAfter(document.getElementById("addQuestionSubject"));
            }
            jQuery.ajax({
                url: serverPath+"/assets/bootstrap-select.js?compile=false",
                dataType: 'script',
                async: true
            });
            $('.selectpicker').selectpicker('refresh');
            document.getElementById("questionText").focus();
            CKEDITOR.config.width = '100%'
            CKEDITOR.replace('questionText', {
                extraPlugins: 'mathjax,font,colorbutton,colordialog,autolink',
                mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
            })
            CKEDITOR.config.removePlugins = 'maximize,scayt,wsc,blockquote,save,flash,iframe,smiley,templates,tabletools,pagebreak,tableselection,templates,about,showblocks,newpage,language,print,div,find,image,SpecialCharacters';
            CKEDITOR.config.removeButtons = 'Source,Copy,Cut,Paste,Undo,Redo,Print,Form,TextField,select,SelectAll,Table,Textarea,Button,tableselection,CreateDiv,PasteText,PasteFromWord,Select,HiddenField,Radio,Checkbox,ImageButton,Anchor,BidiLtr,BidiRtl,Font,Format,Styles,Preview,Indent,Outdent';
        }else{
           //loginOpenWithFunction('askDoubt','Please login to ask doubt');
            alert('Please login to ask doubt');
            loginOpen();
        }
    }
    function submitQuestion() {
       document.getElementById('questionText').value = CKEDITOR.instances.questionText.getData().replaceAll('~~');
        var subject = $("#addQuestionSubject").val(),searchInputValue = document.getElementsByClassName("_bstrpSelectSearchInputId")[document.getElementsByClassName("_bstrpSelectSearchInputId").length -1].value;
        if(subject == "" || subject == undefined || subject == null){
            subject = searchInputValue.trim();
        }
        if (instituteDoubt){
            var classes = $("#addQuestionClass").val(),searchInputValue1 = document.getElementsByClassName("_bstrpSelectSearchInputId")[document.getElementsByClassName("_bstrpSelectSearchInputId").length -1].value;
            if(classes == "" || classes == undefined || classes == null){
                classes = searchInputValue1.trim();
            }
        }
        var sendTags = [];
        if((paramFilter == "" || paramFilter == undefined || paramFilter == null) && (subject != "") && (classes != "")) sendTags.push(subject,classes);
        else {
            sendTags.push(paramFilter);
            subject = paramFilter;
            if (instituteDoubt){
                classes=paramFilter;
            }

        }
       if($("#questionText").val() != "" && $("#questionText").val() != undefined && sendTags != undefined && sendTags.length > 0 && subject != ""){
           var qImg = document.getElementById("questionFile").files[0];
        $('.loading-icon').removeClass('hidden');
        var formData = new FormData();
        if(qImg != undefined && qImg != null && qImg != ""){
            formData.append('file', qImg);
            formData.append('image','true');
        }
        formData.append('title',"");
        formData.append('question',$("#questionText").val());
        formData.append('free','true');
        formData.append('siteId',siteId);
        formData.append('resId',siteId);
        formData.append('tags',sendTags[0]);

        if (instituteDoubt){
            formData.append("instituteId",instituteId);
            formData.append("batchId",batchId);
        }
        $.ajax({
            type:'POST',
            url: '/discussion/addDiscussionQuestion',
            data:formData,
            cache:false,
            contentType: false,
            processData: false,
            success:function(data){
                showTab('nav-child-one');
                $('.loading-icon').addClass('hidden');
                if (instituteDoubt){
                    $("#question-successModal h5").text("Your question has been submitted.");
                    $("#question-successModal p").hide();
                }
                $('#question-successModal').modal('show');
            },
            error: function(data){
                console.log("error");
            }
        });
       }else{

           var htmlStr = "<p> All the fields are mandatory </p>";
           $('#image-modal-body').html(htmlStr);
           if(document.getElementById("attached-ans-img-cancel-btn") != null || document.getElementById("attached-ans-img-cancel-btn") != undefined) $('#attached-ans-img-cancel-btn').remove();
           $('#image-modal').modal('show');

       }

    }
    function skip() {
        $('#category-modal').hide();
        if($(window).width() < 767){
            $('#done').css('display','flex');
        }
        else {
            $('#done').show();
        }
    }

    function getDoubts(tab) {
        $('.loading-icon').removeClass('hidden');
        if(tab == "all"){
            if($(window).width()<768) {
                $('#mobile-filter').removeClass('d-none');
                //$('.doubt-menu .nav-tabs li#myAnswer-tab a').attr('style', 'border-right:1px solid #ededed');
            }
            <g:remoteFunction controller="discussion" action="getMainPageQuestions"  onSuccess="showQuestions(data,'all')" params="'siteId='+siteId+'&batchIndex='+batchIndex+'&filter=all'+'&instituteId='+instituteId" />
        }else if(tab == "myDoubtsTab"){
            if($(window).width()<768) {
                $('#mobile-filter').addClass('d-none');
                //$('.doubt-menu .nav-tabs li#myAnswer-tab a').attr('style', 'border-right:0');
            }
            if(currentUser != null && currentUser != "" && currentUser != undefined){
                <g:remoteFunction controller="discussion" action="getFallowingDoubts"  onSuccess="showQuestions(data,'myDoubtsTab')" params="'siteId='+siteId+'&batchIndex='+batchIndex+'&instituteId='+instituteId" />
            }else if(currentUser == null || currentUser == "" || currentUser == undefined) {
                $("#pagination-div").html("");
                $('.loading-icon').addClass('hidden');
                document.getElementById("myDoubtsTab").innerHTML = "<h6 class='text-left w-100 pt-4'> Please login to see the My Doubts </h6>";
                loginOpen();
            }

        }else if(tab == "myAnswers"){
            $('#mobile-filter').addClass('d-none');
            if(currentUser != null && currentUser != "" && currentUser != undefined){
                <g:remoteFunction controller="discussion" action="getMyAnswers"  onSuccess="showQuestions(data,'myAnswers')" params="'siteId='+siteId+'&batchIndex='+batchIndex+'&instituteId='+instituteId" />
            }else if(currentUser == null || currentUser == "" || currentUser == undefined) {
                $("#pagination-div").html("");
                $('.loading-icon').addClass('hidden');
                document.getElementById("myAnswerTab").innerHTML = "<h6 class='text-left w-100 pt-4'> Please login to see My Answers </h6>";
                loginOpen();
            }

        }
    }


    function displayAllQuestions(discussionDoubts,initialLoopValue,loopLimiter) {
        var htmlStr = "";
        if(discussionDoubts != undefined) for(var i=initialLoopValue; i<loopLimiter; i++) {
            htmlStr = htmlStr + "<div id=\"questionCard_" + i + "\" class=\"card discussion-card mt-4\">\n" +
                "                                    <nav>\n" +
                "                                        <ol class=\"breadcrumb\">\n";
            if (discussionDoubts[i].tags != undefined) { //&& discussionDoubts[i].tags.length >0
                var tag = discussionDoubts[i].tags
                // for(var j=tag.length-1;j >= tag.length-2  && j>=0;j--){
                var tagStr = tag;
                // if(tag[j].split("_")[1] != undefined && tag[j].split("_")[1] != null && tag[j].split("_")[1] != "") tagStr = tag[j].split("_")[1];
                // else tagStr = tag[j];
                if (tagStr == "nextExams") tagStr = "Next Exam";
                htmlStr = htmlStr + "<li class=\"breadcrumb-item\"><a >" + tagStr + "</a></li>\n";
                // }
            }
            htmlStr = htmlStr + " <button id='fallow" + i + "' type=\"button\" class=\"confirm\" onclick=\"javascript:confirm()\" style=\"display: none;\">Confirm</button>\n";
            if (discussionDoubts[i].fallowing == true && $("<textarea/>").html(currentUser).text() != discussionDoubts[i].userId) {htmlStr = htmlStr + "<div class='addtodoubts'><button id='allFallow_" + i + "' type=\"button\" class=\"addDoubts\" onclick=\"actionClicked('all'," + i + ",'unFallowQuestion'," + discussionDoubts[i]._id + ")\">Remove from My Doubts</button>\n" +
                " <div class=\"dropdown dropleft\">\n" +
                "                                            <button class=\"drop-menu dropdown-toggle\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons\">more_horiz</i></button>\n" +
                "                                            <ul class=\"dropdown-menu \">\n" +
                "\n" +
                "                                                <li" +
                " onclick=\"actionClicked('all'," + i + ",'reportAbuse'," + discussionDoubts[i]._id + ")\"" +
                "><a ><i class=\"material-icons\">\n" +
                "                                                    visibility\n" +
                "                                                </i>Report Abuse</a></li>\n" +
                "                                                <li" +
                " onclick=\"actionClicked('all'," + i + ",'reportIncorrect'," + discussionDoubts[i]._id + ")\"" +
                "><a><i class=\"material-icons\">\n" +
                "                                                    visibility\n" +
                "                                                </i>Report Incorrect</a></li>\n" +
                "\n" +
                "                                            </ul>\n" +
                "                                        </div></div>\n";
        }
            else if ($("<textarea/>").html(currentUser).text() != discussionDoubts[i].userId){ htmlStr = htmlStr + "<div class='addtodoubts'><button id='allFallow_" + i + "' type=\"button\" class=\"addDoubts\" onclick=\"actionClicked('all'," + i + ",'fallowQuestion'," + discussionDoubts[i]._id + ")\">Add to My Doubts</button>\n";
                if (!instituteDoubt) {
                    htmlStr = htmlStr + " <div class=\"dropdown dropleft\">\n" +
                "                                            <button class=\"drop-menu dropdown-toggle\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons\">more_horiz</i></button>\n" +
                "                                            <ul class=\"dropdown-menu \">\n" +
                "\n";
                htmlStr = htmlStr + "                                                <li" +
                    " onclick=\"actionClicked('all'," + i + ",'reportAbuse'," + discussionDoubts[i]._id + ")\"" +
                    "><a ><i class=\"material-icons\">\n" +
                    "                                                    visibility\n" +
                    "                                                </i>Report Abuse</a></li>\n" +
                    "                                                <li" +
                    " onclick=\"actionClicked('all'," + i + ",'reportIncorrect'," + discussionDoubts[i]._id + ")\"" +
                    "><a><i class=\"material-icons\">\n" +
                    "                                                    visibility\n" +
                    "                                                </i>Report Incorrect</a></li>\n" +
                    "</ul>\n";
                    htmlStr = htmlStr + "                                        </div></div>\n";
            }
            if (instituteDoubt && isInstituteAdmin) {
                htmlStr = htmlStr + " <div class=\"dropdown dropleft\">\n" +
                    "                                            <button class=\"drop-menu dropdown-toggle\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons\">more_horiz</i></button>\n" +
                    "                                            <ul class=\"dropdown-menu \">\n" +
                    "\n";
                htmlStr = htmlStr + "                                                <li" +
                    " onclick=\"deleteDoubt(" + i + "," + discussionDoubts[i]._id + ")\"" +
                    "><a ><i class=\"material-icons\">\n" +
                    "                                                    delete\n" +
                    "                                                </i>Delete</a></li>\n" +
                    "</ul>\n";
                htmlStr = htmlStr + "                                        </div></div>\n";
            }
        }else if($("<textarea/>").html(currentUser).text() == discussionDoubts[i].userId){ htmlStr = htmlStr + "<div class='addtodoubts'><button id='allFallow_" + i + "' type=\"button\" class=\"addDoubts d-none\" onclick=\"actionClicked('all'," + i + ",'fallowQuestion'," + discussionDoubts[i]._id + ")\">Add to My Doubts</button>\n";
                if (instituteDoubt && isInstituteAdmin) {
                    htmlStr = htmlStr + " <div class=\"dropdown dropleft\">\n" +
                        "                                            <button class=\"drop-menu dropdown-toggle\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons\">more_horiz</i></button>\n" +
                        "                                            <ul class=\"dropdown-menu \">\n" +
                        "\n";
                    htmlStr = htmlStr + "                                                <li" +
                        " onclick=\"deleteDoubt(" + i + "," + discussionDoubts[i]._id + ")\"" +
                        "><a ><i class=\"material-icons\">\n" +
                        "                                                    delete\n" +
                        "                                                </i>Delete</a></li>\n" +
                        "</ul>\n";
                    htmlStr = htmlStr + "                                        </div></div>\n";
                }
            }
            htmlStr = htmlStr + " </ol>\n" +
                "                                    </nav>\n" +
                "                                    <div onclick='openUserProfile(\""+discussionDoubts[i].userId+"\")' class=\"profile\">\n";
            if(discussionDoubts[i].uImgName == undefined || discussionDoubts[i].uImgName == "" || discussionDoubts[i].uImgName == null || discussionDoubts[i].uImgName == 'null') htmlStr = htmlStr + " <img src=\"${assetPath(src: 'landingpageImages/profile.jpg')}\" alt=\"\" class=\"rounded-circle\">\n";
            else htmlStr = htmlStr + "<img src=\"/funlearn/showProfileImage?id="+discussionDoubts[i].uImgId+"&amp;fileName="+discussionDoubts[i].uImgName+"&amp;type=user&amp;imgType=passport\" class=\"rounded-circle\">";
            htmlStr = htmlStr +" <h4 id=\"username\">"+discussionDoubts[i].userName+"</h4>\n" +
                "\n" ;
            if(discussionDoubts[i].hours != undefined && discussionDoubts[i].hours != null) {
                htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+discussionDoubts[i].hours+" hrs</p>\n";
            }
            else {
                var d = new Date(discussionDoubts[i].dateCreated);
                var mins = d.getMinutes();
                var displayDate = "";
                if(mins < 10) displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":0" +d.getMinutes();
                else displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":" +d.getMinutes();
                htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+displayDate+"</p>\n";
            }
            htmlStr = htmlStr + " </div>\n" +
                "                                    <div onclick=\"showQuestionExplorer('allTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" class=\"content\">\n" +
                "<p class='mt-1'>"+discussionDoubts[i].question+"</p>\n";
            if(discussionDoubts[i].qImgName != undefined && discussionDoubts[i].qImgName != "" && discussionDoubts[i].qImgName != null) htmlStr = htmlStr + "<img style='width: 100%;' onclick='openImageModal(\""+discussionDoubts[i].qImgId+"\",\""+discussionDoubts[i].qImgName+"\")' src=\"/discussion/showDoubtImage?id="+discussionDoubts[i].qImgId+"&amp;fileName="+discussionDoubts[i].qImgName+"&amp;type=discussionDoubt&amp;imgType=passport\" class=\"mt-3\">";
            if(discussionDoubts[i].qaList != undefined && discussionDoubts[i].qaList.length > 0 && discussionDoubts[i].qaList[discussionDoubts[i].qaList.length - 1].answer != undefined) htmlStr = htmlStr + " <div style='font-size: 12px; margin-top: 15px' >"+"<p class='answeredby mb-2'><span>Answered by</span>"+discussionDoubts[i].qaList[discussionDoubts[i].qaList.length - 1].userName+"</p></div>"+"<p style='font-size: 12px' class='textOverflow color-grey'>"+discussionDoubts[i].qaList[discussionDoubts[i].qaList.length - 1].answer+"</p>";
            htmlStr = htmlStr + " </div>\n" +
                "                                    <div class=\"card-actions x\">\n";
            if(discussionDoubts[i].upvote_count == undefined) discussionDoubts[i].upvote_count = 0
            htmlStr = htmlStr + "  <input hidden value=\""+discussionDoubts[i].upvote_count+"\" id=\"allVoteCount_"+i+"\" >";
            if(discussionDoubts[i].voted == true) htmlStr = htmlStr + " <div class='flex-action'><button id='allVote_"+i+"' style='color: #2F80ED' onclick=\"actionClicked('all',"+i+",'downvoteQuestion'," + discussionDoubts[i]._id+")\"><span class='upvote-int'>"+discussionDoubts[i].upvote_count+"</span><i style='color: #2F80ED' class=\"material-icons circle bord\">thumb_up</i> Thanked</button>\n";
            else {
                if(discussionDoubts[i].upvote_count == 1) htmlStr = htmlStr + " <div class='flex-action'><button id='allVote_"+i+"' onclick=\"actionClicked('all',"+i+",'upvoteQuestion'," + discussionDoubts[i]._id+")\"><span class='upvote-int'>"+ discussionDoubts[i].upvote_count +"</span><i class=\"material-icons circle\">thumb_up</i> Thanks</button>\n";
                else htmlStr = htmlStr + "<div class='flex-action'> <button id='allVote_"+i+"' onclick=\"actionClicked('all',"+i+",'upvoteQuestion'," + discussionDoubts[i]._id+")\"><span class='upvote-int'>"+ discussionDoubts[i].upvote_count +"</span><i class=\"material-icons circle\">thumb_up</i>Thanks!</button>\n";
                // htmlStr = htmlStr + " <button id='allVote_"+i+"' onclick=\"actionClicked('all',"+i+",'upvoteQuestion'," + discussionDoubts[i]._id+")\"><i class=\"material-icons\">thumb_up</i> Upvote</button>\n";
            }
            if(!(appType == "android" || appType == "ios")) {
                htmlStr = htmlStr + "<button onclick='shareBtnClicked(" + i + "," + discussionDoubts[i]._id + ")' id='share-button'  class=\" dropdown-toggle rounded-0\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons circle\">share</i></button>\n" +
                    "                                   <ul id='share-dropdown_" + i + "' class=\"dropdown-menu d-none\">\n" +
                    "</ul>" ;
            }
            htmlStr +="</div>" +
                "\n" ;
            if(discussionDoubts[i].answerCount == 1 || discussionDoubts[i].answerCount == "1" || (discussionDoubts[i].qaList != undefined && discussionDoubts[i].qaList.length == 1)) htmlStr = htmlStr + " <div class='flex-action'> <button onclick=\"showQuestionExplorer('allTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" ><i class=\"material-icons\">message</i>"+discussionDoubts[i].answerCount+"</button>\n";
            else if(discussionDoubts[i].answerCount != undefined) htmlStr = htmlStr + " <div class='flex-action'><button onclick=\"showQuestionExplorer('allTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" > "+discussionDoubts[i].answerCount+"</button>\n";
            else htmlStr = htmlStr + " <div class='flex-action'><button onclick=\"showQuestionExplorer('allTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" > "+discussionDoubts[i].qaList.length+"</button>\n";

            htmlStr = htmlStr + " <button onclick='showPostAnswer(\"all\","+i+"," + discussionDoubts[i]._id+")' id=\"answerBtn_"+i+"\" class=\"answer-post answer\">\n" +
                "                                        <i class=\"material-icons\">message</i>\n" +
                "                                        Answer\n" +
                "                                    </button>\n"+
                "                                    </div>\n" +
                "                                </div></div>";
        }
        if(discussionDoubts === undefined || discussionDoubts.length == 0){
            document.getElementById("allTab").innerHTML = "<p> There are no doubts. Post a Question </p>";
        }else{
            document.getElementById("allTab").innerHTML = document.getElementById("allTab").innerHTML + htmlStr;
        }
        renderMathInElement(document.body);

    }

    function displayMyDoubts(discussionDoubts,initialLoopValue,loopLimiter) {
        var htmlStr = "";
        if(currentUser != null && currentUser != "" && currentUser != undefined && discussionDoubts != undefined) for(var i=initialLoopValue; i<loopLimiter; i++){
            htmlStr = htmlStr + "<div id=\"myDoubtsTabQuestionCard_"+i+"\" class=\"card discussion-card mt-4\">\n" +
                "                                    <nav>\n" +
                "                                        <ol class=\"breadcrumb\">\n" ;
            if(discussionDoubts[i].tags != undefined ){ //&& discussionDoubts[i].tags.length >0
                var tag = discussionDoubts[i].tags;
                // for(var j=tag.length-1;j >= tag.length-2 && j>=0;j--){
                    var tagStr = discussionDoubts[i].tags;
                    // if(tag[j].split("_")[1] != undefined && tag[j].split("_")[1] != null && tag[j].split("_")[1] != "") tagStr = tag[j].split("_")[1];
                    // else tagStr = tag[j];
                    if(tagStr == "nextExams") tagStr = "Next Exam";
                    htmlStr = htmlStr + "<li class=\"breadcrumb-item\"><a >" + tagStr + "</a></li>\n";
                // }
            }
            htmlStr = htmlStr + " <button type=\"button\" class=\"confirm\" onclick=\"javascript:confirm()\" style=\"display: none;\">Confirm</button>\n";
            if($("<textarea/>").html(currentUser).text() != discussionDoubts[i].userId) htmlStr = htmlStr +"<div class='addtodoubts'><button id='fallow_"+i+"' type=\"button\" class=\"addDoubts\" onclick=\"actionClicked('myDoubtsTab',"+i+",'removeFallowedQuestion'," + discussionDoubts[i]._id+")\">Remove from My Doubts</button>\n" +
            " <div class=\"dropdown dropleft\">\n" +
            "                                            <button class=\"drop-menu dropdown-toggle\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons\">more_horiz</i></button>\n" +
            "                                            <ul class=\"dropdown-menu \">\n" +
            "\n" +
            "                                                <li" +
            " onclick=\"actionClicked('all',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\""+
            "><a ><i class=\"material-icons\">\n" +
            "                                                    visibility\n" +
            "                                                </i>Report Abuse</a></li>\n" +
            "                                                <li" +
            " onclick=\"actionClicked('all',"+i+",'reportIncorrect'," + discussionDoubts[i]._id+")\""+
            "><a><i class=\"material-icons\">\n" +
            "                                                    visibility\n" +
            "                                                </i>Report Incorrect</a></li>\n" +
            "\n" +
            "                                            </ul>\n" +
            "                                        </div></div>\n" ;


            htmlStr = htmlStr + "                                        </ol>\n" +
                "                                    </nav>\n" +
                "                                    <div onclick='openUserProfile(\""+discussionDoubts[i].userId+"\")' class=\"profile\">\n";
            if(discussionDoubts[i].uImgName == undefined || discussionDoubts[i].uImgName == "" || discussionDoubts[i].uImgName == null || discussionDoubts[i].uImgName == 'null') htmlStr = htmlStr + " <img src=\"${assetPath(src: 'landingpageImages/profile.jpg')}\" alt=\"\" class=\"rounded-circle\">\n";
            else htmlStr = htmlStr + "<img src=\"/funlearn/showProfileImage?id="+discussionDoubts[i].uImgId+"&amp;fileName="+discussionDoubts[i].uImgName+"&amp;type=user&amp;imgType=passport\" class=\"rounded-circle\">";
            htmlStr = htmlStr +" <h4 id=\"username\">"+discussionDoubts[i].userName+"</h4>\n" +
                "\n" ;
            if(discussionDoubts[i].hours != undefined && discussionDoubts[i].hours != null) {
                htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+discussionDoubts[i].hours+" hrs</p>\n";
            }
            else {
                var d = new Date(discussionDoubts[i].dateCreated);
                var mins = d.getMinutes();
                var displayDate = "";
                if(mins < 10) displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":0" +d.getMinutes();
                else displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":" +d.getMinutes();
                htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+displayDate+"</p>\n";
            }
            htmlStr = htmlStr + " </div>\n" +
                "                                    <div onclick=\"showQuestionExplorer('myDoubtsTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" class=\"content\">\n" +
                "                                        <p class=\"mt-1\">"+discussionDoubts[i].question+"</p>\n";
            if(discussionDoubts[i].qImgName != undefined && discussionDoubts[i].qImgName != "" && discussionDoubts[i].qImgName != null) htmlStr = htmlStr + "<img style='width: 100%' onclick='openImageModal(\""+discussionDoubts[i].qImgId+"\",\""+discussionDoubts[i].qImgName+"\")' src=\"/discussion/showDoubtImage?id="+discussionDoubts[i].qImgId+"&amp;fileName="+discussionDoubts[i].qImgName+"&amp;type=discussionDoubt&amp;imgType=passport\" class=\"mt-3\">";
            htmlStr = htmlStr + " </div>\n" +
                "                                    <div class=\"card-actions y\">\n";
            if(discussionDoubts[i].upvote_count == undefined) discussionDoubts[i].upvote_count = 0;
            htmlStr = htmlStr + "  <input hidden value=\""+discussionDoubts[i].upvote_count+"\" id=\"myDoubtsTabVoteCount_"+i+"\" >";
            if(discussionDoubts[i].voted == true) htmlStr = htmlStr + "<div class='flex-action'> <button id='myDoubtsTabVote_"+i+"' style='color: #2F80ED' onclick=\"actionClicked('myDoubtsTab',"+i+",'downvoteQuestion'," + discussionDoubts[i]._id+")\">"+discussionDoubts[i].upvote_count+"<i style='color:#2F80ED' class=\"material-icons circle bord\">thumb_up</i> Thanked </button>\n";
            else {
                if(discussionDoubts[i].upvote_count == 1) htmlStr = htmlStr + "<div class='flex-action'> <button id='myDoubtsTabVote_"+i+"' onclick=\"actionClicked('myDoubtsTab',"+i+",'upvoteQuestion'," + discussionDoubts[i]._id+")\"><i class=\"material-icons circle\">thumb_up</i>"+ discussionDoubts[i].upvote_count +" Thanks</button>\n";
                else htmlStr = htmlStr + "<div class='flex-action'> <button id='myDoubtsTabVote_"+i+"' onclick=\"actionClicked('myDoubtsTab',"+i+",'upvoteQuestion'," + discussionDoubts[i]._id+")\"><i class=\"material-icons circle\">thumb_up</i>"+ discussionDoubts[i].upvote_count +" Thanks</button>\n";
            }
            if(!(appType == "android" || appType == "ios")) {
                htmlStr = htmlStr + " <button onclick='shareBtnClicked(\"myDoubt" + i + "\"," + discussionDoubts[i]._id + ")' id='share-button'  class=\" dropdown-toggle rounded-0\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons circle\">share</i></button>\n" +
                    "                                   <ul id='share-dropdown_myDoubt" + i + "' class=\"dropdown-menu \">\n" +
                    "" +
                    " </ul>";
            }
            htmlStr += " </div>"+
                "\n" ;
            if(discussionDoubts[i].qaList != undefined && discussionDoubts[i].qaList != null ) {
                if(discussionDoubts[i].qaList.length == 1) htmlStr = htmlStr + "<div class='flex-action'><button onclick=\"showQuestionExplorer('myDoubtsTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" > "+discussionDoubts[i].qaList.length+" </button>\n" ;
                else htmlStr = htmlStr + " <div class='flex-action'> <button onclick=\"showQuestionExplorer('myDoubtsTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" >"+discussionDoubts[i].qaList.length+" </button>\n" ;
            } else  htmlStr = htmlStr + " <div class='flex-action'><button onclick=\"showQuestionExplorer('myDoubtsTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" > 0 </button>\n";

            htmlStr = htmlStr +
                "                                    <button onclick='showPostAnswer(\"myDoubtsTab\","+i+"," + discussionDoubts[i]._id+")' id=\"myDoubtsTabAnswerBtn_"+i+"\" class=\"answer-post answer\">\n" +

                "                                        <i class=\"material-icons\">message</i>\n" +
                "                                        Answer\n" +
                "                                    </button>\n" +
                "                                    </div>\n" +
                "                                </div></div>";
        }
        document.getElementById("myDoubtsTab").innerHTML = document.getElementById("myDoubtsTab").innerHTML + htmlStr;
        if(discussionDoubts === undefined || discussionDoubts.length == 0){
            document.getElementById("myDoubtsTab").innerHTML = "<p> Currently you have not added any doubts. The doubts you ask and the ones you mark for adding to your list, will be visible here </p>";

        }
    }

    function displayMyAnswers(data,initialLoopValue,loopLimiter) {
        var htmlStr = "";
        var discussionDoubts = data["discussionQuestion"];
        if(currentUser != null && currentUser != "" && currentUser != undefined && discussionDoubts != undefined) for(var i=initialLoopValue; i<loopLimiter; i++){
            var user = data["user"]
            htmlStr = htmlStr + "<div class=\"card discussion-card mt-4\">\n" +
                "                            <nav>\n" +
                "                                <ol class=\"breadcrumb\">\n" ;
            if(discussionDoubts[i].tags != undefined ){ //&& discussionDoubts[i].answersList[0].tags.length >0
                var tag = discussionDoubts[i].tags
                // for(var j= tag.length -1 ;j >= tag.length  && j>=0;j--){
                    var tagStr = tag;//"";
                    // if(tag[j].split("_")[1] != undefined && tag[j].split("_")[1] != null && tag[j].split("_")[1] != "") tagStr = tag[j].split("_")[1];
                    // else tagStr = tag[j];
                    if(tagStr == "nextExams") tagStr = "Next Exam";
                    htmlStr = htmlStr + "<li class=\"breadcrumb-item\"><a >" + tagStr + "</a></li>\n";
                // }
            }
            htmlStr = htmlStr + " <button type=\"button\" class=\"confirm\" onclick=\"javascript:confirm()\" style=\"display: none;\">Confirm</button>\n";
            if(discussionDoubts[i].fallowing == true && $("<textarea/>").html(currentUser).text() != discussionDoubts[i].userId) {
                htmlStr = htmlStr + "<div class='addtodoubts'><button id='myAnswersFallow_" + i + "' type=\"button\" class=\"addDoubts\" onclick=\"actionClicked('myAnswers'," + i + ",'unFallowQuestion'," + discussionDoubts[i]._id + ")\">Remove from My Doubts</button>\n" +
                    " <div class=\"dropdown dropleft\">\n" +
                    "                                            <button class=\"drop-menu dropdown-toggle\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons\">more_horiz</i></button>\n" +
                    "                                            <ul class=\"dropdown-menu \">\n" +
                    "\n" +
                    "                                                <li" +
                    " onclick=\"actionClicked('all'," + i + ",'reportAbuse'," + discussionDoubts[i]._id + ")\"" +
                    "><a ><i class=\"material-icons\">\n" +
                    "                                                    visibility\n" +
                    "                                                </i>Report Abuse</a></li>\n" +
                    "                                                <li" +
                    " onclick=\"actionClicked('all'," + i + ",'reportIncorrect'," + discussionDoubts[i]._id + ")\"" +
                    "><a><i class=\"material-icons\">\n" +
                    "                                                    visibility\n" +
                    "                                                </i>Report Incorrect</a></li>\n" +
                    "\n" +
                    "                                            </ul>\n" +
                    "                                        </div></div>\n";
            }
            else if($("<textarea/>").html(currentUser).text() != discussionDoubts[i].userId) {
                htmlStr = htmlStr + " <div class='addtodoubts'><button id='myAnswersFallow_" + i + "' type=\"button\" class=\"addDoubts\" onclick=\"actionClicked('myAnswers'," + i + ",'fallowQuestion'," + discussionDoubts[i]._id + ")\">Add to My Doubts</button>\n";

                if (!instituteDoubt) {
                    htmlStr = htmlStr + " <div class=\"dropdown dropleft\">\n" +
                        "                                            <button class=\"drop-menu dropdown-toggle\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons\">more_horiz</i></button>\n" +
                        "                                            <ul class=\"dropdown-menu \">\n" +
                        "\n" +
                        "                                                <li" +
                        " onclick=\"actionClicked('all'," + i + ",'reportAbuse'," + discussionDoubts[i]._id + ")\"" +
                        "><a ><i class=\"material-icons\">\n" +
                        "                                                    visibility\n" +
                        "                                                </i>Report Abuse</a></li>\n" +
                        "                                                <li" +
                        " onclick=\"actionClicked('all'," + i + ",'reportIncorrect'," + discussionDoubts[i]._id + ")\"" +
                        "><a><i class=\"material-icons\">\n" +
                        "                                                    visibility\n" +
                        "                                                </i>Report Incorrect</a></li>\n" +
                        "\n" +
                        "                                            </ul>\n" +
                        "                                     </div></div>";
                }else if (instituteDoubt && isInstituteAdmin){
                    htmlStr = htmlStr + " <div class=\"dropdown dropleft\">\n" +
                        "                                            <button class=\"drop-menu dropdown-toggle\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons\">more_horiz</i></button>\n" +
                        "                                            <ul class=\"dropdown-menu \">\n" +
                        "\n";
                    htmlStr = htmlStr + "                                                <li" +
                        " onclick=\"deleteDoubt(" + i + "," + discussionDoubts[i]._id + ")\"" +
                        "><a ><i class=\"material-icons\">\n" +
                        "                                                    delete\n" +
                        "                                                </i>Delete</a></li>\n" +
                        "</ul>\n";
                    htmlStr = htmlStr + "                                        </div></div>\n";
                }
            }
            htmlStr = htmlStr + " </ol>\n" +
                "                            </nav>\n" +
                "                            <div onclick='openUserProfile(\""+discussionDoubts[i].userId+"\")' class=\"profile\">\n";
            if(discussionDoubts[i].uImgName == undefined || discussionDoubts[i].uImgName == null || discussionDoubts[i].uImgName == "" ||discussionDoubts[i].uImgName == 'null') htmlStr = htmlStr + " <img src=\"${assetPath(src: 'landingpageImages/profile.jpg')}\" alt=\"\" class=\"rounded-circle\">\n";
            else htmlStr = htmlStr + "<img src=\"/funlearn/showProfileImage?id="+discussionDoubts[i].uImgId+"&amp;fileName="+discussionDoubts[i].uImgName+"&amp;type=user&amp;imgType=passport\" class=\"rounded-circle\">";
            htmlStr = htmlStr + " <h4 id=\"username\">"+discussionDoubts[i].userName +"</h4>\n" +
                "\n" ;
            if(discussionDoubts[i].hours != undefined && discussionDoubts[i].hours != null) {
                htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+discussionDoubts[i].hours+" hrs</p>\n";
            }
            else {
                var d = new Date(discussionDoubts[i].dateCreated);
                var mins = d.getMinutes();
                var displayDate = "";
                if(mins < 10) displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":0" +d.getMinutes();
                else displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":" +d.getMinutes();
                htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+displayDate+"</p>\n";
            }
            htmlStr = htmlStr + "                            </div>\n" +
                "                                    <div onclick=\"showQuestionExplorer('myAnswerTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" class=\"content\">\n" +
                "                                        <p class=\"mt-1\">"+discussionDoubts[i].question+"</p>\n";
            if(discussionDoubts[i].qImgName != undefined && discussionDoubts[i].qImgName != null && discussionDoubts[i].qImgName != "") htmlStr = htmlStr + "<img onclick='openImageModal(\""+discussionDoubts[i].qImgId+"\",\""+discussionDoubts[i].qImgName+"\")' src=\"/discussion/showDoubtImage?id="+discussionDoubts[i].qImgId+"&amp;fileName="+discussionDoubts[i].qImgName+"&amp;type=discussionDoubt&amp;imgType=passport\" style='width: 100%' class=\"mt-3\">";
            htmlStr = htmlStr + " </div>\n" +
                "                            <div class=\"card-actions z\">\n";
            if(discussionDoubts[i].upvote_count == undefined) discussionDoubts[i].upvote_count = 0;
            htmlStr = htmlStr + "  <input hidden value=\""+discussionDoubts[i].upvote_count+"\" id=\"myDoubtsTabVoteCount_"+i+"\" >";
            if(discussionDoubts[i].voted == true) htmlStr = htmlStr + "<div class='flex-action'><button id='myAnswersVote_"+i+"' style='color: #2F80ED' onclick=\"actionClicked('myAnswers',"+i+",'downvoteQuestion'," + discussionDoubts[i]._id+")\">"+discussionDoubts[i].upvote_count+"<i style='color: #2F80ED' class=\"material-icons circle bord\">thumb_up</i>  Thanked</button>\n";
            else {
                if(discussionDoubts[i].upvote_count == 1) htmlStr = htmlStr + "<div class='flex-action'><button id='myAnswersVote_"+i+"' onclick=\"actionClicked('myAnswers',"+i+",'upvoteQuestion'," + discussionDoubts[i]._id+")\">"+discussionDoubts[i].upvote_count+"<i class=\"material-icons circle\">thumb_up</i> Thanks</button>\n";
                else htmlStr = htmlStr + "<div class='flex-action'> <button id='myAnswersVote_"+i+"' onclick=\"actionClicked('myAnswers',"+i+",'upvoteQuestion'," + discussionDoubts[i]._id+")\">"+discussionDoubts[i].upvote_count+"<i class=\"material-icons circle\">thumb_up</i> Thanks</button>\n";
            }
            if(!(appType == "android" || appType == "ios")) {
                htmlStr = htmlStr + "<button onclick='shareBtnClicked(\"myAnswer" + i + "\"," + discussionDoubts[i]._id + ")' id='share-button'  class=\" dropdown-toggle rounded-0\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons circle\">share</i></button>\n" +
                    "                                   <ul id='share-dropdown_myAnswer" + i + "' class=\"dropdown-menu \">\n" +
                    "" +
                    " </ul>";
            }
            htmlStr = htmlStr + " </div>";
            if(discussionDoubts[i].qaList.length == 1) htmlStr = htmlStr + " <button onclick=\"showQuestionExplorer('myAnswerTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" > "+discussionDoubts[i].qaList.length+" Answer</button>\n";
            else htmlStr = htmlStr + " <button onclick=\"showQuestionExplorer('myAnswerTab',"+i+",'reportAbuse'," + discussionDoubts[i]._id+")\" > "+discussionDoubts[i].qaList.length+" Answers</button>\n";

            htmlStr = htmlStr +
                "                            </div>\n" +
                "\n";
            for(var j=0; j< discussionDoubts[i].qaList.length; j++){
                if(discussionDoubts[i].qaList.answer != undefined && discussionDoubts[i].qaList.answer != "" && discussionDoubts[i].qaList.answer != null){
                htmlStr = htmlStr + " <div onclick='openUserProfile(\""+data.user.userId+"\")' class=\"profile mt-3\">\n";
                if(user.profilepic == undefined || user.profilepic == null || user.profilepic == "" || user.profilepic == 'null') htmlStr = htmlStr + " <img src=\"${assetPath(src: 'landingpageImages/profile.jpg')}\" alt=\"\" class=\"rounded-circle\">\n";
                else htmlStr = htmlStr + "<img src=\"/funlearn/showProfileImage?id="+user.id+"&amp;fileName="+user.profilepic+"&amp;type=user&amp;imgType=passport\" class=\"rounded-circle\">";
                htmlStr = htmlStr + " <h4 >"+user.name+"</h4>\n" ;
                if(discussionDoubts[i].qaList.hours != undefined && discussionDoubts[i].qaList.hours != null) {
                    htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+discussionDoubts[i].qaList.hours+" hrs</p>\n";
                }
                else {
                    var d = new Date(discussionDoubts[i].qaList.createdOn);
                    var mins = d.getMinutes();
                    var displayDate = "";
                    if(mins < 10) displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":0" +d.getMinutes();
                    else displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":" +d.getMinutes();
                    htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+displayDate+"</p>\n";
                }
                htmlStr = htmlStr +"                            </div>\n" +
                    "                            <div class=\"answer-wrappers mt-2\">\n" +
                    "                                <div class=\" userAnswer\">\n " +
                    " <p>"+discussionDoubts[i].qaList.answer + " </p> \n" ;
                if(discussionDoubts[i].qaList.imgName != undefined && discussionDoubts[i].qaList.imgName != "" && discussionDoubts[i].qaList.imgName != null) htmlStr = htmlStr + "<img onclick='openAnswerImagel("+discussionDoubts[i]._id.timestamp+","+discussionDoubts[i]._id.machineIdentifier+","+discussionDoubts[i]._id.processIdentifier+","+discussionDoubts[i]._id.counter+",\""+discussionDoubts[i].qaList.imgName+"\")' src=\"/discussion/showAnswerImage?timestamp="+discussionDoubts[i]._id.timestamp+"&amp;machineIdentifier="+discussionDoubts[i]._id.machineIdentifier+"&amp;processIdentifier="+discussionDoubts[i]._id.processIdentifier+"&amp;counter="+discussionDoubts[i]._id.counter+"&amp;fileName="+discussionDoubts[i].qaList.imgName+"&amp;type=discussionDoubt&amp;imgType=passport\" class=\"mt-3\">";
                htmlStr = htmlStr +   "                                </div>\n" +
                    "                            </div>\n";
            }
            }

            htmlStr = htmlStr +     " </div>";
        }
        document.getElementById("myAnswerTab").innerHTML = document.getElementById("myAnswerTab").innerHTML + htmlStr;
        if(discussionDoubts === undefined || discussionDoubts.length == 0){
            document.getElementById("myAnswerTab").innerHTML = "<p style='padding: 0 15px'>Currently you have not answered any doubts. Answer some doubts and help others.</p>";
        }
    }

    function showQuestions(data,tab){
        var discussionDoubts = [];
        count = data['count'];
        isInstituteAdmin=data['isInstituteAdmin'];
        if(batchIndex == 0){
            document.getElementById("allTab").innerHTML = "";
            document.getElementById("myAnswerTab").innerHTML = "";
            document.getElementById("myDoubtsTab").innerHTML = "";
            $("#pagination-currentpage-count").val(0);
            allQuestionsList = [];
            myDoubtsList = [];
            myAnswerList = [];
        }
        if(tab == "all" || tab == "myDoubtsTab"){
            discussionDoubts = data["discussionQuestion"];
        }else if(tab == "myAnswers"){
            discussionDoubts = data["discussionQuestion"]
        }

        if (discussionDoubts.length==0 && tab != "myDoubtsTab" && tab != "myAnswers"){
            $("#searchError").show();
        }else{
            $("#searchError").hide();
        }

        if(discussionDoubts != undefined) {
            if(tab == "all"){
                $('.nav-tabs a[href="#alldoubts"]').tab('show');
                    allQuestionsList = allQuestionsList.concat(discussionDoubts);
                showPagination('all');
            }else  if(tab == "myDoubtsTab"){
                myDoubtsList = myDoubtsList.concat(discussionDoubts);
                showPagination('myDoubtsTab');
            } else if(tab == "myAnswers"){
                if(myAnswerList['discussionQuestion'] == undefined || myAnswerList['discussionQuestion'].length == 0) myAnswerList = data;
                else myAnswerList['discussionQuestion'] = myAnswerList['discussionQuestion'].concat(discussionDoubts);
                showPagination('myAnswers');
            }
        }

    }

    function actionClicked(tab,index,action,q_a_id) {
        if (action == "reportAbuse") {
            if(currentUser != null && currentUser != "" && currentUser != undefined){
                var obj = {
                "id": q_a_id,
                "siteId": siteId
            };
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                url: '/discussion/addAbuse',
                data: JSON.stringify(obj),
                success: function (response) {
                    getDoubts(tab);
                    $('.loading-icon').addClass('hidden');
                    var htmlStr = "<p> Reported as Abusive </p>";
                    $('#image-modal-body').html(htmlStr);
                    if(document.getElementById("attached-ans-img-cancel-btn") != null || document.getElementById("attached-ans-img-cancel-btn") != undefined) $('#attached-ans-img-cancel-btn').remove();
                    $('#image-modal').modal('show');
                }
            });
            }else {
                alert("Please login to report the question");
                loginOpen();
            }

        }else if (action == "reportIncorrect") {
            if(currentUser != null && currentUser != "" && currentUser != undefined){
                var obj = {
                    "id": q_a_id,
                "siteId": siteId
            };
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                url: '/discussion/addIncorrect',
                data: JSON.stringify(obj),
                success: function (response) {
                    getDoubts(tab);
                    $('.loading-icon').addClass('hidden');
                    var htmlStr = "<p> Reported as Incorrect </p>";
                    $('#image-modal-body').html(htmlStr);
                    if(document.getElementById("attached-ans-img-cancel-btn") != null || document.getElementById("attached-ans-img-cancel-btn") != undefined) $('#attached-ans-img-cancel-btn').remove();
                    $('#image-modal').modal('show');
                }
            });
            }else {
                alert("Please login to report the question");
                loginOpen();
            }
        }
        else if(action == "upvoteQuestion"){
            if(currentUser != null && currentUser != "" && currentUser != undefined){
                var obj = {
                    "id": q_a_id,
                    "siteId": siteId
                };
                $('.loading-icon').removeClass('hidden');
                $.ajax({
                    type: "POST",
                    contentType: "application/json",
                    dataType: "json",
                    url: '/discussion/upvoteQuestion',
                    data: JSON.stringify(obj),
                    success: function (response) {
                        $('.loading-icon').addClass('hidden');
                    }
                });
                var upvoteCount = parseInt($("#"+tab+"VoteCount_"+index).val());
                upvoteCount = upvoteCount +1;
                $("#"+tab+"VoteCount_"+index).val(upvoteCount);
                $('button#'+tab+'Vote_'+index).replaceWith(" <button id='"+tab+"Vote_"+index+"' style='color: #2F80ED' onclick=\"actionClicked('"+tab+"',"+index+",'downvoteQuestion'," + q_a_id+")\">"+upvoteCount+"<i style='color: #2F80ED' class=\"material-icons circle bord\">thumb_up</i> Thanked </button>\n")
            }else {
                alert("Please login to upvote a question");
                loginOpen();
            }
        }else if(action == "fallowQuestion"){

            if(currentUser != null && currentUser != "" && currentUser != undefined){
                var obj = {
                    "id": q_a_id,
                    "siteId": siteId
                };
                $('.loading-icon').removeClass('hidden');
                $.ajax({
                    type: "POST",
                    contentType: "application/json",
                    dataType: "json",
                    url: '/discussion/fallowQuestion',
                    data: JSON.stringify(obj),
                    success: function (response) {
                        $('.loading-icon').addClass('hidden');
                    }
                });
                $('button#'+tab+'Fallow_'+index).replaceWith("<button id='"+tab+"Fallow_"+index+"' type=\"button\" class=\"addDoubts\" onclick=\"actionClicked('"+tab+"',"+index+",'unFallowQuestion'," + q_a_id+")\">Remove from My Doubts</button>\n")
            }else {
                alert("Please login to follow a question");
                loginOpen();
            }
        }else if(action == "unFallowQuestion"){

            var obj = {
                "id": q_a_id,
                "siteId": siteId
            };
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                url: '/discussion/unFallowQuestion',
                data: JSON.stringify(obj),
                success: function (response) {
                    $('.loading-icon').addClass('hidden');
                }
            });
            $('button#'+tab+'Fallow_'+index).replaceWith(" <button id='"+tab+"Fallow_"+index+"' type=\"button\" class=\"addDoubts\" onclick=\"actionClicked('"+tab+"',"+index+",'fallowQuestion'," + q_a_id+")\">Add to My Doubts</button>\n")
        }else if(action == "downvoteQuestion"){

            var obj = {
                "id": q_a_id,
                "siteId": siteId
            };
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                url: '/discussion/downVoteQuestion',
                data: JSON.stringify(obj),
                success: function (response) {
                    $('.loading-icon').addClass('hidden');
                }
            });
            var upvoteCount = parseInt($("#"+tab+"VoteCount_"+index).val());
            upvoteCount = upvoteCount - 1;
            $("#"+tab+"VoteCount_"+index).val(upvoteCount);
            if(upvoteCount == 1) $('button#'+tab+'Vote_'+index).replaceWith(" <button id='"+tab+"Vote_"+index+"' onclick=\"actionClicked('"+tab+"',"+index+",'upvoteQuestion'," + q_a_id+")\">"+upvoteCount+"<i class=\"material-icons circle\">thumb_up</i> Thanks</button>\n");
            else $('button#'+tab+'Vote_'+index).replaceWith(" <button id='"+tab+"Vote_"+index+"' onclick=\"actionClicked('"+tab+"',"+index+",'upvoteQuestion'," + q_a_id+")\">"+upvoteCount+"<i class=\"material-icons circle\">thumb_up</i>  Thanks</button>\n")
        }
        else if(action == "removeFallowedQuestion"){

            var obj = {
                "id": q_a_id,
                "siteId": siteId
            };
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                url: '/discussion/unFallowQuestion',
                data: JSON.stringify(obj),
                success: function (response) {
                    $('.loading-icon').addClass('hidden');
                    var htmlStr = "<p> Question removed from My Doubts </p>";
                    $('#image-modal-body').html(htmlStr);
                    if(document.getElementById("attached-ans-img-cancel-btn") != null || document.getElementById("attached-ans-img-cancel-btn") != undefined) $('#attached-ans-img-cancel-btn').remove();
                    $('#image-modal').modal('show');
                }
            });
            $("div#myDoubtsTabQuestionCard_"+index).remove();
            myDoubtsList.splice(index,1);
        }
    }

    function answerActionClicked(tab,index,action,q_a_id) {
        if(currentUser != null && currentUser != "" && currentUser != undefined){
        if(action == "downvoteAnswer"){

            var obj = {
                "id":q_a_id,
                "siteId": siteId,
                "answerId":q_a_id
            };
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                url: '/discussion/downVoteAnswer',
                data: JSON.stringify(obj),
                success: function (response) {
                    $('.loading-icon').addClass('hidden');
                }
            });
            $('button#'+tab+'Vote_'+index).replaceWith(" <button id='"+tab+"Vote_"+index+"' onclick=\"answerActionClicked('"+tab+"',"+index+",'upvoteAnswer'," + q_a_id+")\">Thanks</button>\n")
        }else if(action == "upvoteAnswer"){
            if(currentUser != null && currentUser != "" && currentUser != undefined){
                var obj = {
                    "id":q_a_id,
                    "siteId": siteId,
                    "answerId":q_a_id
                };
                $('.loading-icon').removeClass('hidden');
                $.ajax({
                    type: "POST",
                    contentType: "application/json",
                    dataType: "json",
                    url: '/discussion/upvoteAnswer',
                    data: JSON.stringify(obj),
                    success: function (response) {
                        $('.loading-icon').addClass('hidden');
                    }
                });
                $('button#'+tab+'Vote_'+index).replaceWith(" <button id='"+tab+"Vote_"+index+"' style='color: #2F80ED' onclick=\"answerActionClicked('"+tab+"',"+index+",'downvoteAnswer'," + q_a_id+")\"> Thanked </button>\n")
            }else {
                alert("Please login to upvote an Answer");
                loginOpen();
            }
        }else if(action == "addAnswerAbuse"){

            var obj = {
                "id":q_a_id,
                "siteId": siteId,
                "answerId":q_a_id
            };
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                url: '/discussion/addAnswerAbuse',
                data: JSON.stringify(obj),
                success: function (response) {
                    getDoubts(tab);
                    $('.loading-icon').addClass('hidden');
                    var htmlStr = "<p> Reported as Abusive </p>";
                    $('#image-modal-body').html(htmlStr);
                    if(document.getElementById("attached-ans-img-cancel-btn") != null || document.getElementById("attached-ans-img-cancel-btn") != undefined) $('#attached-ans-img-cancel-btn').remove();
                    $('#image-modal').modal('show');
                }
            });
        }else if(action == "addAnswerIncorrect"){

            var obj = {
                "id":q_a_id,
                "siteId": siteId,
                "answerId":q_a_id
            };
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                type: "POST",
                contentType: "application/json",
                dataType: "json",
                url: '/discussion/addAnswerIncorrect',
                data: JSON.stringify(obj),
                success: function (response) {
                    getDoubts(tab);
                    $('.loading-icon').addClass('hidden');
                    var htmlStr = "<p> Reported as Incorrect </p>";
                    $('#image-modal-body').html(htmlStr);
                    if(document.getElementById("attached-ans-img-cancel-btn") != null || document.getElementById("attached-ans-img-cancel-btn") != undefined) $('#attached-ans-img-cancel-btn').remove();
                    $('#image-modal').modal('show');
                }
            });
        }
    }else {
            if(action == "upvoteAnswer") alert("Please login to upvote the Answer");
            else if(action == "addAnswerAbuse") alert("Please login to report the Answer");
            else if(action == "addAnswerIncorrect") alert("Please login to report the Answer");
            loginOpen();
        }
    }

    function showPostAnswer(tab,index,qId){
        if(appType == "android"){
            callAndroidAddAnswer(qId);
        }else if(appType == "ios"){
            callIOSAddAnswer(qId);
        }else if(currentUser != null && currentUser != "" && currentUser != undefined){
            // if($('.close-answer')[0] != undefined) console.log($('.close-answer')[0].id)
            if($('.close-answer')[0] != undefined) $('.close-answer').remove();
            if($('.answer-cancel')[0] != undefined) $('.answer-cancel').css("display",'none');
            if($('.answer-post')[0] != undefined) $('.answer-post').show();
            var htmlStr = " " +
                " <div id='postAnswerForm_"+index+"' class=\" close-answer card answer-card mt-4\">\n" +
                "                                    <div class=\"modalBackdrop\">\n" +
                "                                    </div>\n" +
                "                                    <textarea id='answerText_"+index+"' class=\"answer-textarea\" name='answerQuestion'></textarea>\n" +
                " <div class='position-relative'>" +
                " <img id=\"ans-image-preview_"+index+"\" onclick='openImageModal(undefined,undefined)'  src=\"#\" style='display:none;width: 100%' />" +
                " <button style='display:none;' type=\"button\" class='ans-close-img close-preview answer-cancel' onclick=\"removeSelectedAnsImg("+index+")\" > <i class=\"material-icons\">cancel</i></button> "+

                " </div>"+
                " <button   type=\"button\" class=\" close-img\" onclick='hidePostAnswer(\""+tab+"\","+index+")' > <i class=\"material-icons\">cancel</i> </button>" +
                "                                    <div class=\"answer-actions\">\n" +
                "                                        <input style='display: none' id=\"fileoption_"+index+"\" name=\"file_"+index+"\" type=\"file\"  accept=\"image/png, image/jpeg, image/gif ,image/svg\" onchange=\"updateFileObj("+index+");\" />" +
                " <button onclick='attachFileClicked("+index+")' type='button'>" ;
            if($(window).width() < 767) htmlStr = htmlStr + " <i class=\"material-icons\">photo_camera</i>\n" ;
            else htmlStr = htmlStr + " <i class=\"material-icons\">attach_file</i>";
            htmlStr = htmlStr +  "</button>\n" +
                " <button class=\"btn btn-default post\" onclick='submitAnswer("+index+","+qId+")' >Post</button>\n" +
                "                                    </div>\n" ;
            htmlStr += "                                    <div class=\"modal fade\" id=\"postmodal\">\n" +
                "                                        <div class=\"modal-dialog\">\n" +
                "                                            <div class=\"modal-content\">\n" +
                "                                                <div class=\"modal-body\">\n" +
                "                                                    <div class=\"circle_around\">\n" +
                "                                                        <i class=\"material-icons\">check</i>\n" +
                "                                                    </div>\n" +
                "                                                        <h4>Your Answer has been sent to review. </h4>\n" +
                "                                                        <p>Once approved you’ll be notified.</p>\n" +
                "                                                <button class=\"btn btn-dismiss\" onclick=\"javascript:dismissModal();\">Okay!</button>\n" +
                "                                                </div>\n" +
                "                                            </div>\n" +
                "                                        </div>\n" +
                "                                    </div>\n"+
                "                                </div>";

            if(tab == "all") {
                $(htmlStr).insertAfter(document.getElementById("questionCard_"+index));
                $("#answerBtn_"+index).hide();
                $("#cancelPostBtn_"+index).css("display",'flex');
            }
            else if(tab == "myDoubtsTab") {
                $(htmlStr).insertAfter(document.getElementById("myDoubtsTabQuestionCard_"+index));
                $("#myDoubtsTabAnswerBtn_"+index).hide();
                $("#myDoubtsTabCancelPostBtn_"+index).css("display",'block');
            }else if(tab == "questionExplorer"){
                $(htmlStr).insertAfter(document.getElementById("questionExplorerQuestionCard_"+index));
                $("#questionExplorerAnswerBtn_"+index).hide();
                $("#questionExplorerCancelPostBtn_"+index).css("display",'block');
            }
            document.getElementById("answerText_"+index).focus();
            var userAnswers= document.getElementById("answerText_"+index);
            CKEDITOR.config.width = '100%'

            CKEDITOR.replace(userAnswers, {
                extraPlugins: 'mathjax,font,colorbutton,colordialog,autolink',
                mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
            });
            CKEDITOR.config.removePlugins = 'maximize,scayt,wsc,blockquote,save,flash,iframe,smiley,templates,tabletools,pagebreak,tableselection,templates,about,showblocks,newpage,language,print,div,find,image,SpecialCharacters';
            CKEDITOR.config.removeButtons = 'Source,Copy,Cut,Paste,Undo,Redo,Print,Form,TextField,select,SelectAll,Table,Textarea,Button,tableselection,CreateDiv,PasteText,PasteFromWord,Select,HiddenField,Radio,Checkbox,ImageButton,Anchor,BidiLtr,BidiRtl,Font,Format,Styles,Preview,Indent,Outdent';

        }else{
            alert("Please login to Answer a Question");
            loginOpen();
        }
    }

    function hidePostAnswer(tab,index){
        /* if(tab == "all"){
             $("#cancelPostBtn_"+index).css("display",'none');
             $("#answerBtn_"+index).show()
             $("#postAnswerForm_"+index).remove();
         }else if(tab == "myDoubtsTab"){
             $("#myDoubtsTabCancelPostBtn_"+index).css("display",'none');
             $("#myDoubtsTabAnswerBtn_"+index).show()
             $("#postAnswerForm_"+index).remove();
         } */
        if($('.close-answer')[0] != undefined) $('.close-answer').remove();
        if($('.answer-cancel')[0] != undefined) $('.answer-cancel').css("display",'none');
        if($('.answer-post')[0] != undefined) $('.answer-post').show();
    }

    function submitAnswer(index,qId){
        document.getElementById("answerText_"+index).value = CKEDITOR.instances["answerText_"+index].getData().replaceAll('~~');
       if($("#answerText_"+index).val() != "" && $("#answerText_"+index).val() != undefined){
           var formData = new FormData();
        $('.loading-icon').removeClass('hidden');
        if(fileObj['file_'+index] != undefined && fileObj['file_'+index] != null && fileObj['file_'+index] != ""){
            formData.append('file', fileObj['file_'+index]);
            formData.append('image','true');
        }
        formData.append('answerText',$("#answerText_"+index).val())
        // formData.append('timestamp',timestamp)
        // formData.append('machineIdentifier',machineIdentifier)
        // formData.append('processIdentifier',processIdentifier)
        // formData.append('counter',counter)
        formData.append('siteId',siteId)
        formData.append('qId',qId)
           if(instituteDoubt) {
               formData.append('instituteId', instituteId)
           }
        $.ajax({
            type:'POST',
            url: '/discussion/addAnswer',
            data:formData,
            cache:false,
            contentType: false,
            processData: false,
            success:function(data){
                <g:remoteFunction controller="discussion" action="updateRecentAnsweredDiscussionQuestionCache"   params="'siteId='+siteId" />
                $("#answerText_"+index).val("")
                hidePostAnswer('','');
                $('.loading-icon').addClass('hidden');
                batchIndex = 0;
                getDoubts("all");
                if (instituteDoubt){
                    $("#answer-successModal h5").text("Your answer has been submitted.");
                    $("#answer-successModal p").hide();
                }
                $('#answer-successModal').modal('show');
            },
            error: function(data){
                $('.loading-icon').addClass('hidden');
                console.log("error");
            }
        });
       }else {
           var htmlStr = "<p> Cannot submit an empty Answer </p>";
           $('#image-modal-body').html(htmlStr);
           if(document.getElementById("attached-ans-img-cancel-btn") != null || document.getElementById("attached-ans-img-cancel-btn") != undefined) $('#attached-ans-img-cancel-btn').remove();
           $('#image-modal').modal('show');
       }
    }

    function showQuestionExplorer(tab,index,action,qId) {
        var obj = {
            "qId": qId,
            "siteId": siteId
        };
        $('.loading-icon').removeClass('hidden');
        $.ajax({
            type: "POST",
            contentType: "application/json",
            dataType: "json",
            url: '/discussion/getQuestionById',
            data: JSON.stringify(obj),
            success: function (response) {
                displayQuestionExplorer(response,tab);
            }
        });
    }

    function displayQuestionExplorer(response,tab) {
        $('.loading-icon').addClass('hidden');
        $("#pagination-div").html("");
        $('#main-filters').attr("style", "display: none !important");
        // $('#nav-child-one').trigger('click');
        var discussionQuestions = response['discussionQuestion'];
        discussionQuestions['_id'] = discussionQuestions['id'];
        $('#backToDoubts').css('display', 'flex');
        $('.search-ws').hide();
        $('.app-back-btn').hide();
        var htmlStr = "<div class=\"w-100\">\n" +
            "    <div class=\"col-12 px-0\">\n" +
            "        <div >\n" +
            "            <div id=\"questionExplorerQuestionCard_0\" class=\"card discussion-card mt-4\">\n" +
            "                <nav>\n" +
            "                    <ol class=\"breadcrumb\">\n";
        if(discussionQuestions.tags != undefined ){ //&& discussionQuestions.tags.length >0
            var tag = discussionQuestions.tags
            // for(var i=tag.length-1;i >= (tag.length - 2)  && i>=0;i--){
                var tagStr = tag;//"";
                // if(tag[i].split("_")[1] != undefined && tag[i].split("_")[1] != null && tag[i].split("_")[1] != "") {
                //     tagStr = tag[i].split("_")[1];
                //     if(tagStr == "nextExams") tagStr = "Next Exam";
                //     htmlStr = htmlStr + "<li class=\"breadcrumb-item\"><a >" + tagStr + "</a></li>\n";
                // }
            if(tagStr == "nextExams") tagStr = "Next Exam";
            htmlStr = htmlStr + "<li class=\"breadcrumb-item\"><a >" + tagStr + "</a></li>\n";
                // else tagStr = tag[i]
            // }
            htmlStr = htmlStr + "                               </ol>\n" +
                "                           </nav>\n";
        }else{
            htmlStr = htmlStr + "                               </ol>\n" +
                "                           </nav>\n";
        }
        htmlStr = htmlStr + " <div onclick='openUserProfile(\""+discussionQuestions.userId+"\")' class=\"profile\">\n";
        if(discussionQuestions.uImgName == undefined || discussionQuestions.uImgName == "" || discussionQuestions.uImgName == null || discussionQuestions.uImgName == 'null') htmlStr = htmlStr + " <img src=\"${assetPath(src: 'landingpageImages/profile.jpg')}\" alt=\"\" class=\"rounded-circle\">\n";
        else htmlStr = htmlStr + "<img  src=\"/funlearn/showProfileImage?id="+discussionQuestions.uImgId+"&amp;fileName="+discussionQuestions.uImgName+"&amp;type=user&amp;imgType=passport\" class=\"rounded-circle\">";
        htmlStr = htmlStr + " <h4 id=\"username\">"+discussionQuestions.userName+"</h4>\n" ;
        if(response.hours != undefined && response.hours != null) {
            htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+response.hours+" hrs</p>\n";
        }
        else {
            var d = new Date(discussionQuestions.dateCreated);
            var mins = d.getMinutes();
            var displayDate = "";
            if(mins < 10) displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":0" +d.getMinutes();
            else displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":" +d.getMinutes();
            htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+displayDate+"</p>\n";
        }
        htmlStr = htmlStr + " </div>\n" +
            "                                    <div  class=\"content\">\n" +
            "                                        <p class=\"mt-1\">"+discussionQuestions.question+"</p>\n";
        if(discussionQuestions.qImgName != undefined && discussionQuestions.qImgName != "" && discussionQuestions.qImgName != null) htmlStr = htmlStr + "<img style='width: 100%' onclick='openImageModal(\""+discussionQuestions.qImgId+"\",\""+discussionQuestions.qImgName+"\")' src=\"/discussion/showDoubtImage?id="+discussionQuestions.qImgId+"&amp;fileName="+discussionQuestions.qImgName+"&amp;type=discussionDoubt&amp;imgType=passport\" class=\"mt-3\">";
        htmlStr = htmlStr + " </div>\n" +
            "                <div class=\"card-actions a\">\n" ;
        if(discussionQuestions.upVote == undefined) discussionQuestions.upVote = 0;
        htmlStr = htmlStr + "  <input hidden value=\""+discussionQuestions.upVote+"\" id=\"VoteCount_"+-1+"\" >";
        if(discussionQuestions.voted == true) htmlStr = htmlStr + "<div class='flex-action'><button id='Vote_-1' style='color: #2F80ED' onclick=\"actionClicked('',"+-1+",'downvoteQuestion'," + discussionQuestions._id+")\">"+discussionQuestions.upVote+"<i style='color: #2F80ED' class=\"material-icons circle bord\">thumb_up</i> Thanked</button>\n";
        else {
           if(discussionQuestions.upVote == 1) htmlStr = htmlStr + "<div class='flex-action'> <button id='Vote_-1' onclick=\"actionClicked('',"+-1+",'upvoteQuestion'," + discussionQuestions._id+")\">"+discussionQuestions.upVote+"<i class=\"material-icons circle\">thumb_up</i> Thanks</button>\n";
           else htmlStr = htmlStr + " <div class='flex-action'><button id='Vote_-1' onclick=\"actionClicked('',"+-1+",'upvoteQuestion'," + discussionQuestions._id+")\">"+discussionQuestions.upVote+"<i class=\"material-icons circle\">thumb_up</i>Thanks</button>\n";
        }
        if(!(appType == "android" || appType == "ios")) {
            htmlStr = htmlStr + "  <button onclick='shareBtnClicked(\"explorer" + 1 + "\"," + discussionQuestions._id+ ")' id='share-button'  class=\" dropdown-toggle rounded-0\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons circle\">share</i></button>\n" +
                "                                   <ul id='share-dropdown_explorer" + 1 + "' class=\"dropdown-menu d-none\">\n" +
                " </ul>" ;
        }
        htmlStr +=" </div>";
        if(discussionQuestions.qaList.length == 1) htmlStr = htmlStr + " <div class='flex-action'><button> "+discussionQuestions.qaList.length+"</button>\n";
        else htmlStr = htmlStr + " <div class='flex-action'><button> "+discussionQuestions.qaList.length+"</button>\n";
        htmlStr = htmlStr +
            "                                    <button onclick='showPostAnswer(\"questionExplorer\","+0+"," + discussionQuestions._id+")' id=\"questionExplorerAnswerBtn_"+0+"\" class=\"answer-post answer\">\n" +
            "                                        <i class=\"material-icons\">message</i>\n" +
            "                                        Answer\n" +
            "                                    </button>\n" +
            "             </div> </div>\n" +

            "         </div>\n" +
            "            <h4 class=\"mt-4 answer-head\">Answers</h4>\n";
        if(discussionQuestions.qaList != undefined && discussionQuestions.qaList.length > 0) for(var a=0; a<discussionQuestions.qaList.length; a++) {
            if(discussionQuestions.qaList[a].answer != undefined && discussionQuestions.qaList[a].answer != "" && discussionQuestions.qaList[a].answer != null){
            htmlStr = htmlStr + " <div class=\"card discussion-card mt-4\">\n" +
                "                 <div class=\"all-answers\">\n" +
                "                    <div class=\"answerContent\">\n" ;
            htmlStr = htmlStr + " <div class='flex-action justify-content-between answer-drop'><div class=\"profile\">\n";
            if(discussionQuestions.qaList[a].uImgName == undefined || discussionQuestions.qaList[a].uImgName == "" || discussionQuestions.qaList[a].uImgName == null || discussionQuestions.qaList[a].uImgName == 'null') htmlStr = htmlStr + " <img src=\"${assetPath(src: 'landingpageImages/profile.jpg')}\" alt=\"\" class=\"rounded-circle\">\n";
            else htmlStr = htmlStr + "<img src=\"/funlearn/showProfileImage?id="+discussionQuestions.qaList[a].uImgId+"&amp;fileName="+discussionQuestions.qaList[a].uImgName+"&amp;type=user&amp;imgType=passport\" class=\"rounded-circle\">";
            htmlStr = htmlStr + " <h4 id=\"username\">"+discussionQuestions.qaList[a].userName+"</h4>\n" ;
            if(discussionQuestions.qaList[a].hours != undefined && discussionQuestions.qaList[a].hours != null) {
                htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+discussionQuestions.qaList[a].hours+" hrs</p>\n";
            }
            else {
                var d = new Date(discussionQuestions.qaList[a].dateCreated);
                var mins = d.getMinutes();
                var displayDate = "";
                if(mins < 10) displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":0" +d.getMinutes();
                else displayDate = d.getDate()+ "-" + (d.getMonth() + 1) + "-" +  d.getFullYear()   + " " + d.getHours() + ":" +d.getMinutes();
                htmlStr = htmlStr + " <p id=\"time\"><span>&#8226;</span> "+displayDate+"</p>\n";
            }
            htmlStr += "</div> ";
            if (!instituteDoubt) {

                htmlStr +=  "<div class=\"dropdown dropleft\">\n" +
                "                                <button class=\"drop-menu dropdown-toggle\" type=\"button\" data-toggle=\"dropdown\"><i class=\"material-icons\">more_horiz</i></button>\n" +
                "                                <ul class=\"dropdown-menu dropdown-menu-right\">\n" +
                "                                    <li" +
                " onclick=\"answerActionClicked('all'," + a + ",'addAnswerAbuse'," + discussionQuestions.qaList[a].id + ")\"" +
                " ><a ><i class=\"material-icons\">\n" +
                "                                        visibility\n" +
                "                                    </i>Report Abuse</a></li>\n" +
                "                                    <li" +
                " onclick=\"answerActionClicked('all'," + a + ",'addAnswerIncorrect'," + discussionQuestions.qaList[a].id + ")\"" +
                " ><a ><i class=\"material-icons\">\n" +
                "                                        visibility\n" +
                "                                    </i>Report Incorrect</a></li>\n" +
                "                                </ul>\n" +
                "                            </div>\n";
            }

            htmlStr = htmlStr + " </div>\n" +
                "                        <div class=\"content\">\n" +
                "                            <div class=\"userAnswer\">\n" +
                "                                        <p class=\"mt-1\">"+discussionQuestions.qaList[a].answer+"</p>\n";
            if(discussionQuestions.qaList[a].imgName != undefined && discussionQuestions.qaList[a].imgName != "" && discussionQuestions.qaList[a].imgName != null) htmlStr = htmlStr + "<img onclick='openAnswerImagel("+discussionQuestions._id+",\""+discussionQuestions.qaList[a].imgName+"\","+discussionQuestions.qaList[a].id+")' src=\"/discussion/showAnswerImage?qId="+discussionQuestions._id+"&amp;ansId="+discussionQuestions.qaList[a].id+"&amp;fileName="+discussionQuestions.qaList[a].imgName+"&amp;type=discussionDoubt&amp;imgType=passport\" class=\"mt-3\">";
            htmlStr = htmlStr + " </div>\n" +
                "                        </div>\n" +
                "                        <div class=\"card-actions b\">\n" ;
            if(discussionQuestions.qaList[a].voted == true) htmlStr = htmlStr + " <button id='allVote_"+a+"' style='color: #2F80ED' onclick=\"answerActionClicked('all',"+a+",'downvoteAnswer'," +discussionQuestions.qaList[a].id+")\">Thanked</button>\n";
            else htmlStr = htmlStr + " <button id='allVote_"+a+"' onclick=\"answerActionClicked('all',"+a+",'upvoteAnswer'," +discussionQuestions.qaList[a].id+")\"> Thanks</button>\n";
                htmlStr = htmlStr +
                "                        </div>\n" +
                "                    </div>\n" +
                "                </div>\n" +
                "            </div>\n";
        }
        }
        htmlStr = htmlStr + " </div>\n" +
            "    </div>\n" +
            "</div>";

        document.getElementById(tab).innerHTML = htmlStr;
        window.scroll({
            top: 0,
            left: 0,
        });
        renderMathInElement(document.body);
    }

    function updateFileObj(index){
        var answerImg = document.getElementById("fileoption_"+index).files[0];
        if (answerImg && answerImg.size > 2097152) // 2 mb for bytes.
        {
            $('#ans-image-preview_'+index).attr('src', '');
            $('#ans-image-preview_'+index).hide();
            $('.ans-close-img').hide();
            $("#fileoption_"+index).val("");
            fileObj['file_'+index] = "";
            alert("Image size limit maximum 2MB ");
            return;
        }else if(answerImg){
            fileObj['file_'+index] = answerImg
            $('#ans-image-preview_'+index).show();
            $('.ans-close-img').show();
            var previewAnswerImg = document.getElementById("ans-image-preview_"+index);
            previewAnswerImg.style.width = "100%";
            previewAnswerImg.style.height = "50px";
            if($(window).width()<767) {
                $("#ans-image-preview_"+index).css("margin","5px");
            }
            var reader = new FileReader();
            reader.onload = function(e) {
                var htmlStr = "<img style='width: 100%' id='ans-preview-modal' src=\"#\" class=\"mt-3\">";
                $('#image-modal-body').html(htmlStr);
                $('#ans-image-preview_'+index).attr('src', e.target.result);
                $('#ans-preview-modal').attr('src', e.target.result);
                $(".ans-modal-img-remove").html("");
                $(" <button id='attached-ans-img-cancel-btn' type='button' class=\"btn ans-modal-img-remove cancels\" onclick=\"javascript:removeSelectedAnsImg("+index+");\">Remove Image</button>\n" ).insertBefore("#image-modal-close");
            }
            reader.readAsDataURL(answerImg); // convert to base64 string

        }else{
            $('#ans-image-preview_'+index).attr('src', '');
            $('#ans-image-preview_'+index).hide();
            $('.ans-close-img').hide();
            $("#fileoption_"+index).val("");
            $("#image-modal-close").trigger("click");
            fileObj['file_'+index] = "";
        }
    }

    function questionFileChanged() {
        var qImgObj = document.getElementById("questionFile").files[0];
        if (qImgObj && qImgObj.size <= 2097152 ) {
            $('#image-preview').show();
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#q-image-preview').attr('src', e.target.result);
            }

            reader.readAsDataURL(qImgObj); // convert to base64 string
        }else{
            if(qImgObj!=undefined && qImgObj.size > 2097152) alert("Image size limit maximum 2MB ");
            $('#q-image-preview').attr('src', '');
            $('#image-preview').hide();
            $("#questionFile").val("");
        }

    }

    function attachFileClicked(index){
        $('#fileoption_'+index).trigger('click');
    }



    var cardIndex = "";

    function subjectChanged(field) {
        // var searchString = "subject_"+field[field.selectedIndex].value
        // if(field[field.selectedIndex].value != ''){
        //     $('#add-tags-btn').css('display','block');
        // }else {
        //     $('#add-tags-btn').css('display','none');
        // }
        tags[0] = field[field.selectedIndex].value;
        if (tags[0] !=null || tags[0] !=""){
            $(".clearFilter").removeAttr('disabled');
        }
        if(tagsModalOpened == 0) getFilteredData();
    }

    function classChanged(field){
        batchId = field[field.selectedIndex].getAttribute('data-value');
        if (batchId !=null || batchId !=""){
            $(".clearFilter").removeAttr('disabled');
        }
        if (instituteDoubt &&  batchId !=null){
            <g:remoteFunction controller="discussion" action="questionGlobalSearch"  onSuccess="showQuestions(data,'all')" params="'siteId='+siteId+'&query= '+'&type=filter'+'&batchId='+batchId" />
        }else{
            getDoubts("all");
        }
    }

    function answerFilterChanged(field) {
        var filter = field[field.selectedIndex].value;
        if (filter !="" || filter !=null){
            $(".clearFilter").removeAttr('disabled');
        }
        var paramFilterValue;
        if (instituteDoubt){
            paramFilterValue  = 'siteId='+siteId+'&batchIndex='+batchIndex+'&filter='+filter+'&batchId='+batchId+'&instituteId='+instituteId;
        }else{
            paramFilterValue  = 'siteId='+siteId+'&batchIndex='+batchIndex+'&filter='+filter
        }
        <g:remoteFunction controller="discussion" action="getMainPageQuestions"  onSuccess="showQuestions(data,'all')" params="paramFilterValue" />
    }

    function getFilteredData(){
        var sendTags = [];
        var paramsValue

        if(tags[0] != '' && tags[0] != undefined){
            if(tags[0] != 'ALL') {
                if(paramFilter == "" || paramFilter == undefined || paramFilter == null) sendTags.push(tags[0]);
                else sendTags.push(tags[0]);
            }
        }
        if(sendTags.length > 0){
            batchIndex = 0;
            if (instituteDoubt){
                paramsValue='siteId='+siteId+'&query= &type=filter&tagsList='+sendTags+'&batchId='+batchId+'&instituteId='+instituteId;
            }else{
                paramsValue='siteId='+siteId+'&query= &type=filter&tagsList='+sendTags;
            }
            <g:remoteFunction controller="discussion" action="questionGlobalSearch"  onSuccess="showQuestions(data,currentTab)" params="paramsValue" />
        }else{
            batchIndex = 0;
            getDoubts("all");
        }
    }

    function booksListReceived(data){
        subjects = [];
        var books = JSON.parse(data.books);
        for(var i = 0; i<books.length;i++){
            if(subjects.indexOf(books[i].subject) < 0) //Compatible IE 9 or later
                subjects.push(books[i].subject);
        }
        subjects.sort();

        var select;
        select = document.getElementById("subject");
        select.options.length = 1;
        if(subjects.length > 0){
            var el = document.createElement("option");
            el.textContent = "ALL";
            el.value = "ALL";
            select.appendChild(el);
        }
        for (var i = 0; i < subjects.length; i++) {
            var el = document.createElement("option");
            el.textContent = subjects[i];
            el.value = subjects[i];
            select.appendChild(el);
        }
        document.getElementById("subject").focus();
    }

    function resetGrade(){
        var select;
        select = document.getElementById("grade");
        select.options.length = 1;
        resetSubject();
    }

    function resetSubject(){
        var select;
        select = document.getElementById("subject");
        select.options.length = 1;
    }

    function questionAttachFileClicked(){
        $('#questionFile').trigger('click');
    }
    $("#mydoubts-tab").click(function () {
        batchIndex = 0;
        $("#pagination-currentpage-count").val(0);
        currentTab = "myDoubtsTab";
        $('#image-preview').hide();
        $('#main-filters').attr("style", "display: none !important");
        $('#doubt-search').css('display','none');
        $('.search-ws').css('display','flex');
        $('#backToDoubts').hide();
        getDoubts("myDoubtsTab");
    });
    $("#alldoubts-tab").click(function () {
        batchIndex = 0;
        $("#pagination-currentpage-count").val(0);
        currentTab = "all";
        $('#image-preview').hide();
        $('#doubt-search').css('display','none');
        $('.search-ws').css('display','flex');
        $('#backToDoubts').hide();
        if(paramFilter == "" || paramFilter == undefined || paramFilter == null) {
            $("#main-filters").removeAttr("style");
            getDoubts("all");
        }
        else getFilteredData();
    });
    $("#myAnswer-tab").click(function () {
        batchIndex = 0;
        $("#pagination-currentpage-count").val(0);
        currentTab = "myAnswers";
        $('#image-preview').hide();
        $('#main-filters').attr("style", "display: none !important");
        $('#doubt-search').css('display','none');
        $('.search-ws').css('display','flex');
        $('#backToDoubts').hide();
        getDoubts("myAnswers")
    })



    var paramSearchVal;
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            if (instituteDoubt){
                paramSearchVal ={query:query,siteId:siteId,type:'search',paramFilter:paramFilter,instituteId:instituteId}
            }else{
                paramSearchVal = {query:query,siteId:siteId,type:'search',paramFilter:paramFilter}
            }
            $.ajax({
                url: '/discussion/questionGlobalSearch',
                method: 'GET',
                data: paramSearchVal,
                dataType: 'JSON',
                success: function fetchBooks1(data) {
                    $('.loading-icon').addClass('hidden');
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return($("<div/>").html(item).text());
                        } else {
                            return $("<div/>").html(item).text();
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });

    function submitSearch(){
        var searchString = document.getElementById("search-book").value;
        batchIndex = 0;
        var paramsValue;
        if(searchString != "" && searchString != undefined) {
            $('.loading-icon').removeClass('hidden');
            if (instituteDoubt){
                paramsValue='siteId='+siteId+'&query='+searchString+'&type=questions&paramFilter='+paramFilter+'&instituteId='+instituteId;
            }else{
                paramsValue='siteId='+siteId+'&query='+searchString+'&type=questions&paramFilter='+paramFilter;
            }
            <g:remoteFunction controller="discussion" action="questionGlobalSearch"  onSuccess="showQuestions(data,'all')" params="paramsValue" />
        }

    }

    $('#search-book').focusout(function () {
        if($('#search-book').val() == "") {
            batchIndex = 0;

            if(paramFilter == "" || paramFilter == undefined || paramFilter == null) {
                $("#main-filters").removeAttr("style");
                getDoubts("all");
            }
            else {
                $('#main-filters').attr("style", "display: none !important");
                getFilteredData();
            }

            // getDoubts("all");
        }
    });

    function shareBtnClicked(index,q_a_id) {

        if (!instituteDoubt){
            var doubtUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.origin);
            doubtUrl = doubtUrl + "/doubts?qId="+q_a_id+"__questionExplorer__"+$("<textarea/>").html(currentUser).text();
        }else if(instituteDoubt){
            var doubtUrl = "https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.origin);
            doubtUrl = doubtUrl + "/doubts?instituteId="+instituteId+"&qId="+q_a_id+"__questionExplorer__"+$("<textarea/>").html(currentUser).text();
        }

        openShareContentModal("doubts", doubtUrl);
    }

    function showPagination(screen){
        var  data = [], currentPageValue = 0;
        if(screen == "all") data = allQuestionsList;
        else if(screen == "myDoubtsTab") data = myDoubtsList;
        else if(screen == "myAnswers") {
            data = myAnswerList["discussionQuestion"]
        }
        currentPageValue = parseInt($("#pagination-currentpage-count").val())+1;
        $("#pagination-currentpage-count").val(currentPageValue);
        if(data.length == 0) {
            $('.loading-icon').addClass('hidden');
            $("#pagination-div").html("");
            if(screen == "myDoubtsTab"){
                document.getElementById("myDoubtsTab").innerHTML = "<p> Currently you have not added any doubts. The doubts you ask and the ones you mark for adding to your list, will be visible here </p>";
            }else if(screen == "myAnswers"){
                document.getElementById("myAnswerTab").innerHTML = "<p style='padding: 0 15px;'>Currently you have not answered any doubts. Answer some doubts and help others.</p>";
            }
        }else if(data.length > 0) {
            if(currentPageValue <= Math.ceil(data.length/recordsPerPage)) {
                var loopInitialValue = 0;
                var loopLimiter = 0;
                loopInitialValue = (currentPageValue - 1) * recordsPerPage;
                if (data.length > loopInitialValue + recordsPerPage) loopLimiter = loopInitialValue + recordsPerPage;
                else loopLimiter = data.length;
                $('.loading-icon').addClass('hidden');
                if (screen == "all") displayAllQuestions(data, loopInitialValue, loopLimiter);
                else if (screen == "myDoubtsTab") displayMyDoubts(data, loopInitialValue, loopLimiter);
                else if (screen == "myAnswers") displayMyAnswers(myAnswerList, loopInitialValue, loopLimiter);
                if (data.length > 20){
                    $("#pagination-div").html("<a onclick='showPagination(\"" + screen + "\")' class='btn btn-showMore'> Show More </a>");
                        }
            }else if(data.length < count){
                batchIndex = Math.floor(((currentPageValue - 1) * recordsPerPage)/recordsPerBatch);
                $("#pagination-currentpage-count").val(currentPageValue -1);
                getDoubts(screen);
            }
        }
    }



    function openImageModal(imgId,imgName){
        if(imgId != undefined && imgId != ""){
            var htmlStr = "<img style='width: 100%' src=\"/discussion/showDoubtRealImage?id="+imgId+"&amp;fileName="+imgName+"&amp;type=discussionDoubt&amp;imgType=passport\" class=\"mt-3\">";
            $('#image-modal-body').html(htmlStr);
        }
        if(document.getElementById("attached-ans-img-cancel-btn") != null || document.getElementById("attached-ans-img-cancel-btn") != undefined) $('#attached-ans-img-cancel-btn').remove();
        $('#image-modal').modal('show');
    }

    function openAnswerImagel(qId,imageName,ansId){
        var htmlStr = "<img width='100%' src=\"/discussion/showAnswerRealImage?qId="+qId+"&amp;ansId="+ansId+"&amp;fileName="+imageName+"&amp;type=discussionDoubt\" class=\"mt-3\">";
        $('#image-modal-body').html(htmlStr);
        if(document.getElementById("attached-ans-img-cancel-btn") != null || document.getElementById("attached-ans-img-cancel-btn") != undefined) $('#attached-ans-img-cancel-btn').remove();
        $('#image-modal').modal('show');
    }

    $("#tags-modal").on('show.bs.modal', function(){
        // $('#add-tags-btn').css('display','none');
        tagsModalOpened = 1;
    });
    $("#tags-modal").on('hide.bs.modal', function(){
        tagsModalOpened = 0;
    });


    function closeTagsModal(){
        $("#tags-modal").modal('hide');
        showTab('nav-child-one');
    }

    function showTab(tabHrefId){
        $('.sectionHeight .nav-tabs,.askdoubt,.tab-content, .new-folder').show();
        $('.posting-question').hide();
        $('#image-preview').hide();
        $('#q-image-preview').attr('src', '');
        $('#'+tabHrefId).trigger('click');
        $('#backToDoubts').hide();
        $(".clearFilter").show();
        $('#doubt-text').html(' <span class="d-flex align-items-center">\n' +
            '<button id="goBack" class="material-icons border-0 mr-1 go-back-btn app-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button><div class="mdl-tooltip" data-mdl-for="goBack">Back</div><strong>Doubts</strong></span>');

        if($(window).width()<767){
            $('.mobile-footer').show();
            $('#doubt-search').hide();
            $('.mobile-drop').css('margin-top','150px')
        }
        if($(window).width()<991){
            $('.ask-doubt-btn').attr("style", "display: inline-block !important");
        }
        if(allQuestionsList == undefined || allQuestionsList.length == 0) {
           if(paramFilter == "" || paramFilter == undefined || paramFilter == null) {
               batchIndex = 0;
               $('.filter-wrapper').show();
               getDoubts("all");
           }
           else getFilteredData();
        }
        $('.doubt-menu').removeClass('pb-3');
    }

    function removeSelectedQuestionImg(){
        $('#questionFile').val('');
        questionFileChanged();
    }

    function removeSelectedAnsImg(index){
        $('#fileoption_'+index).val('');
        updateFileObj(index);
    }

    $('.search-ws').on('click',function () {
        $('#doubt-search').css('display','flex');
        $('.search-ws').css('display','none');
    });
    $('#showFilter').on('click',function () {
        if($(window).width()<767) {
            $('#rowFilters').toggleClass('d-flex',function () {
                if($('#rowFilters').is(':visible') == false){
                    document.getElementById("grade").innerHTML = "<option value=\"\" selected=\"selected\">Select Grade</option>";
                    document.getElementById("subject").innerHTML = "<option value=\"\" selected=\"selected\">Select Subject</option>";
                    tags[2]="";
                    tags[3]="";
                    getFilteredData();
                }
            });
        }
    });
    // if($(window).width()<767) {
    //     $(document).scroll(function () {
    //         var y = $(this).scrollTop();
    //         if (y < 20) {
    //             $('#doubt-search').fadeIn();
    //         } else {
    //             $('#doubt-search').fadeOut();
    //         }
    //     });
    // }

    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                e.preventDefault();
                submitSearch();
            }
        }
    });

    //constructing json for app to populate class and subject for institute doubts
    if (instituteDoubt){
        var classList = [];
        var subjectList;
        <g:each in="${institutes}" var="institute" status="i">
        classList.push({"className":"${institute.name}","batchId":${institute.batchId},"classId":${institute.id}});
        </g:each>
        <g:each in="${subjects}" var="subject" status="i">
        subjectList = "${subjects.name}";
        </g:each>
    }

    function callAndroidAddQuestion(){
        if (instituteDoubt){
            JSInterface.openQuestionEvent(JSON.stringify(classList),subjectList);
        }else{
            JSInterface.openQuestionEvent();
        }
    }

    function callAndroidAddAnswer(qId){
        JSInterface.openAnswerEvent(qId,instituteId);
    }

    function callIOSAddQuestion(){
        webkit.messageHandlers.openQuestionEvent.postMessage('');
    }

    function callIOSAddAnswer(qId){
        var json = {"qId":qId};
        webkit.messageHandlers.openAnswerEvent.postMessage(JSON.stringify(json));

    }
    function deleteDoubt(id,doubtId){
        <g:remoteFunction controller="discussion" action="deleteQuestionByInstituteAdmin" params="'instituteId='+instituteId+'&id='+doubtId" onSuccess="deleteSuccess(data)" />
    }


    function deleteSuccess(data){
       $("#question-deleteSuccessModal").modal('show');
    }

    function closeDeleteModal(){
        $('.loading-icon').removeClass('hidden');
        window.location.reload();
    }

    if(appType == "android" || appType == "ios"){
       $('.doubt-menu').addClass('reset-app');
       $('.mobile-drop').addClass('reset-app');
        $('#student-problems-section').addClass('reset-app');
       $('#openApps').modal('hide');
        $('#loginOpen').modal('hide');
    }



    if(appType=='ios'){
        document.addEventListener('touchmove', function (event) {
            if (event.scale !== 1) { event.preventDefault(); }
        }, false);
    }



    if (instituteDoubt){
        $("#class").removeClass('d-none');
    }


    $( document ).ready(function() {
        $('.loading-icon').removeClass('hidden');
    });

    $(window).load(function() {
        $('.loading-icon').addClass('hidden');
    });

    function clearFilters(){
        var dropDown = document.getElementById("answerFilter");
        dropDown.selectedIndex = 0;

        $("#class").val('');
        $("#class").selectpicker("refresh");
        $("#subject").val('');
        $("#subject").selectpicker("refresh");
        $("#main-filters").removeAttr("style");
        $("#mobile-filter").removeClass('d-none');
        currentTab = "all";
        getDoubts("all");
    }


</script>
<div class="modal" id="tutorial">
    <div class="modal-dialog">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <div></div>
                <div class="ml-4">
                    <ul class="carousel-indicators indicator-override">
                        <li data-target="#demo" data-slide-to="0" class="active"></li>
                        <li data-target="#demo" data-slide-to="1"></li>
                    </ul>
                </div>
                <div>
                <button type="button" class="btn btn-default btn-next"  href="#demo" data-slide="next">Next</button>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
            </div>

            <!-- Modal body -->
            <div class="modal-body d-flex align-items-end align-items-lg-center">
            <div id="demo" class="carousel" data-ride="carousel">

                <!-- Indicators -->


                <!-- The slideshow -->
                <div class="carousel-inner">
                    <div class="carousel-item active text-center">
                        <h2>Have Doubts?</h2>
                        <p class="mt-4">You can <strong>earn +5 points</strong> if <strong>your question gets answered.</strong></p>
                        <div class="position-relative">
                            <img src="${assetPath(src: 'ws/arrow.png')}" class="posting-question" style="left:0;height: 65px;">
                            <img src="${assetPath(src: 'ws/line-doubts.png')}">
                        </div>
                        <p>Ask your peers in the community and</p>
                        <p><strong><i class="material-icons">thumb_up</i> Thank </strong> the best of all answers.</p>
                    </div>
                    <div class="carousel-item tutor text-center">
                        <h2>Before you start!</h2>
                        <p><strong>Stars</strong> Shows the level of <strong>User</strong> who has posted the Question or the Answer</p>
                        <div class="d-flex align-items-center justify-content-center mt-4">
                            <p id="grade-rating"><i class="material-icons">grade</i></p>
                            <span class="wonderwonk">Wonder Wonk Ultimate (1000 points)</span>
                        </div>
                        <div class="d-flex align-items-center justify-content-center mt-2">
                            <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i></p>
                            <span class="wonderwonk">Wonder Wonk Ultimate (3000 points)</span>
                        </div>
                        <div class="d-flex align-items-center justify-content-center mt-2">
                            <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i> <i class="material-icons">grade</i></p>
                            <span class="wonderwonk">Wonder Wonk Ultimate (5000 points)</span>
                        </div>
                        <img src="${assetPath(src: 'ws/line-doubts.png')}">
                        <div class="mt-4">
                            <p><strong>Answering a question</strong> earns you +5 Points.
                            Try and help your peers as much as you can!</p>
                            <button class="btn btn-answer mt-4 mb-2"><i class="material-icons">chat</i> Answer</button>
                        </div>
                        <img src="${assetPath(src: 'ws/line-doubts.png')}">
                        <div class="d-flex mt-4 mb-4">
                            <div class="circle"><i class="material-icons">share</i> </div>
                            <div><p><strong>Sharing Questions and Answers</strong> will make you earn extra <strong>+2 Points</strong>.</p></div>
                        </div>
                        <img src="${assetPath(src: 'ws/line-doubts.png')}">
                        <div class="d-flex mt-4">
                            <div class="circle"><i class="material-icons">thumb_up</i> </div>
                            <div><p>If your question or answer gets “thanked”, you’ll earn <strong>+2 Points.</strong></p></div>
                        </div>
                    </div>
                </div>

            </div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">

            </div>

        </div>
    </div>
</div>
<script>

    function openUserProfile(userName) {
        if(appType == "android"){
            callAndroidOpenUserProfilePage(1,'7days');
        }else if(appType == "ios"){
            callIOSOpenUserProfilePage(1,'7days');
        }else{
            window.location.href = "../usermanagement/index?userName="+userName+"&scoreType=1&rankType=7days";
        }
    }

    function callAndroidOpenUserProfilePage(scoreType,rankType) {
        JSInterface.openUserProfilePageEvent(scoreType,rankType);
    }

    function callIOSOpenUserProfilePage(scoreType,rankType) {
        var json = {'scoreType':scoreType,'rankType':rankType};
        webkit.messageHandlers.openUserProfilePageEvent.postMessage(JSON.stringify(json));
    }
</script>

<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
%{--<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.3/js/bootstrap-select.js"></script>--}%
<asset:javascript src="bootstrap-select.js"/>


</body>
</html>
