<div class="modal fade sage-modal" id="studentInformation" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-labelledby="sageModalLabel">
  <div class="modal-dialog modal-sm" role="document">
    <div class="modal-content">
      <div class="modal-header sage-modal-header"></div>
      <div class="modal-body">
        <form class="sage-login-form add-info-form" style="margin-top: 0;">
          <div class="form-group text-center">
            <p class="provide-add-info">Please provide your Institution information</p>
              <p class="errorField" style="display: none;">Please Fill atleast one field !</p>
          </div>
          <div class="form-group">
            <input type="text" class="form-control sage-input" id="inst-address" placeholder="Institution Address" value="<%=(session["userdetails"].address1!=null && session["userdetails"].address1!=""?session["userdetails"].address1:"")%>">
          </div>

          <div class="form-group">
            <input type="text" class="form-control sage-input" id="inst-city" placeholder="Institution City" value="<%=(session["userdetails"].city!=null && session["userdetails"].city!=""?session["userdetails"].city:"")%>">
          </div>

          <div class="form-group">
            <input type="text" class="form-control sage-input" id="inst-state" placeholder="Institution State" value="<%=(session["userdetails"].state!=null && session["userdetails"].state!=""?session["userdetails"].state:"")%>">
          </div>
          <div class="form-group">
            <input type="text" class="form-control sage-input" id="inst-pin" placeholder="PIN Code" value="<%=(session["userdetails"].twitter!=null && session["userdetails"].twitter!=""?session["userdetails"].twitter:"")%>">
          </div>
          <div class="form-group">
            <input type="button" class="form-control sage-input sage-login-btn full-width-input waves-effect waves-ripple" onclick="javascript:submitAddInfor();" value="Submit">
          </div>
          <div class="form-group text-center" style="margin-bottom: 0;">
            <a data-dismiss="modal" class="skip-add-info">Skip</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
    $(document).ready(function() {
<%     
    if(session["userdetails"]!=null && 
            ((session["userdetails"].address1==null ||  session["userdetails"].address1=="") ||
            (session["userdetails"].city==null || session["userdetails"].city=="") ||
            (session["userdetails"].state==null || session["userdetails"].state=="") || 
            (session["userdetails"].twitter==null || session["userdetails"].twitter==""))) { 
%>
        $('#studentInformation').modal('show');
        $('#studentInformation').modal({backdrop: 'static', keyboard: false})
<%  } %>    
    });

    function submitAddInfor() {
        var instituteAddress = document.getElementById('inst-address').value;
        var instituteCity = document.getElementById('inst-city').value;
        var instituteState = document.getElementById('inst-state').value;
        var institutePin = document.getElementById('inst-pin').value;
        var isFilled = $('input[type="text"]', 'form').filter(function() {
            return $.trim(this.value).length;  //text inputs have a value
        }).length;
        if(isFilled) {
            <g:remoteFunction controller="creation" action="updateUserInformation" params="'address1='+instituteAddress+'&city='+instituteCity+'&state='+instituteState+'&twitter='+institutePin"/>
            $('#studentInformation').modal('hide');
            $('.errorField').hide();
        }
        else{
            $('#studentInformation').modal('show');
            $('.errorField').show();
        }

  }
</script>