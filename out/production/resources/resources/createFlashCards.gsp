<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>

<div class="modal fade" id="successModal">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">

            </div>

            <!-- Modal body -->
            <div class="modal-body">
            <i class="material-icons">check_circle_outline</i>
                <p>The set <span id="setName"></span> is created.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" onclick="javascript:backToMain();" class="btn close-btn">Close</button>
            </div>

        </div>
    </div>
</div>
<section class="flashcard-home create-card">
    <div class="container" id="book-read-material">
        <div class="d-flex mobile-stocker-web pl-3 pl-lg-5">
       <h4 class="hero-title mt-lg-4 pl-lg-4 pr-lg-4">
           <span class="d-flex align-items-center">
           <i class="material-icons d-none d-lg-block mr-2">add</i>
               <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
               <h4 class="ml-3"> Add New</h4>
           <%} else{%>
               <i class="material-icons d-lg-none mr-2" onclick="javascript:window.history.back();">keyboard_backspace</i> Add New
           <%}%>
           </span>
       </h4>
        </div>

            <div role="tabpanel" class="tab-pane fade in show active" id="all">
                <div class="container">
                    <div id="content-data-all" class=""></div>
                    <div id="htmlreadingcontent" class="row quiz-section"></div>

                </div>
            </div>
    </div>
</section>






<g:render template="/${session['entryController']}/footer_new"></g:render>

<g:render template="/resources/revision"></g:render>
<script>
    //things required to have same code in book page and here
    var previousChapterId = -1;
    var studySetResId =-1;
    <%if(params.chapterId!=null){%>
    previousChapterId = ${params.chapterId};
    fromAppCreation=true;
    <%}%>
    if (!fromAppCreation){
        createNewSet();
    }

</script>
