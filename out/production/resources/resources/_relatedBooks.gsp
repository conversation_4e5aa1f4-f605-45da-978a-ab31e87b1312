<g:render template="/wonderpublish/loginChecker"></g:render>
<asset:stylesheet href="wonderslate/relatedBooksStyle.css" async="true"/>

<%if(!"true".equals(session["commonWhiteLabel"])){%>
<style>
.related-book-wrapper{
    margin-top: 0 !important;
}
</style>
<%}%>
<div class="popular_searches pt-4" id="relatedBooksContainer" style="display: none">
    <div class="bookDetails__container">

    </div>
    <div class="mt-3 pt-3">
        <div class="bookDetails__container">
            <%if(params.pBookId!=null){%>
            <h5 class="mb-2 relatedTitle">Related Products</h5>
            <%}else{%>
            <h5 class="mb-2 relatedTitle">Related Books/Courses</h5>
            <%}%>
            <div id="relatedBooks" class="row popular_search_lists">
            </div>
            <div class="text-right w-100">
                <%if(!(("android".equals(session["appType"]))||("ios".equals(session["appType"])))){%>
                    <%if("true".equals(session["commonWhiteLabel"])){%>
                        <a href="/sp/${session['siteName']}/store" id="show-more" class="text-center text-primary text-primary-modifier mr-3">Show More...</a>
                    <%} else { %>
                        <a href="/${session['entryController']}/store" id="show-more" class="text-center text-primary text-primary-modifier mr-3">Show More...</a>
                    <%}%>
                <%}%>
            </div>
        </div>
    </div>

</div>
<div class="popular_searches" id="bestSellerBooksContainer" style="display: none">
    <div class="bookDetails__container">
        <h5 class="mb-2 relatedTitle">Best Sellers</h5>
    </div>
    <div class="mt-1 py-1" >
        <div class="bookDetails__container">
            <div id="bestSellerBooks" class="row popular_search_lists" style="height:auto !important;gap: 18px;">
            </div>
            <div class="text-right w-100">
                <%if("true".equals(session["commonWhiteLabel"])){%>
                <a href="/sp/${session['siteName']}/store" id="bestSeller-show-more" class="text-center text-primary text-primary-modifier mr-3">Show More...</a>
                <%} else { %>
                <a href="/${session['entryController']}/store" id="bestSeller-show-more" class="text-center text-primary text-primary-modifier mr-3">Show More...</a>
                <%}%>
            </div>
        </div>
    </div>

</div>


<script>
    var siteName = "";

    <%if("true".equals(session["commonWhiteLabel"])){%>
    siteName = "${session['siteName']}";
    <%}else{%>
    siteName = "${session['entryController']}";
    <%}%>

    var tokenId="${params.tokenId}";
    function fetchBestSellerBooks(){

        <g:remoteFunction controller="wsshop" action="getRelatedBooks"  onSuccess='displayBestSellerBooks(data);' onFailure='handleBestSellerFailure();'
                  params="'fromApp=false&bestSellers=true&bookId=${params.bookId}'" />

    }
    function fetchRelatedBooks(){
        <%if(params.bookId!=null){%>
        <g:remoteFunction controller="wsshop" action="getRelatedBooks"  onSuccess='displayRelatedBooks(data);' onFailure='handleFailure();'
                  params="'fromApp=false&relatedBooks=true&bookId=${params.bookId}'" />
                  <%}else if(params.pBookId!=null){%>
        <g:remoteFunction controller="affiliation" action="getPrintRelatedBooks"  onSuccess='displayPrintRelatedBooks(data);' onFailure='handleFailure();'
                  params="'fromApp=false&relatedBooks=true&bookId=${params.pBookId}'" />
        <%}else{%>
        <%if(!("1".equals(""+session["siteId"]) || "28".equals(""+session["siteId"]))){%>
        fetchBestSellerBooks();
        <%}else{%>
        <g:remoteFunction controller="wsshop" action="getRelatedBooks"  onSuccess='displayRelatedBooks(data);' onFailure='handleFailure();'
                  params="'fromApp=false&relatedBooks=true'" />
        <%}}%>
    }

    function handleFailure(){
        $("#relatedBooksContainer").css('display', 'none');
    }
    function handleBestSellerFailure(){
        $("#bestSellerBooksContainer").css('display', 'none');
    }
    function displayPrintRelatedBooks(data){

        if(data === null || data === [] || data === '' || data.books === null || data.books === [] || data.books === '' || data.books === '[]'){
            $("#relatedBooksContainer").css('display', 'none');
            return;
        }
        var books = JSON.parse(data.books);
        var newObj = {};
        var newArr = [];

        $('#show-more').hide();

        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        var noOfEbooks  = 0;
        var ebookHtmlStr="";
        var bookTitle ="";

        for(var i=0; i<books.length && i<=9; i++){
            bookTitle = replaceAll(books[i].title,' ','-');
            bookTitle = replaceAll(bookTitle,'&','-');
            bookTitle = replaceAll(bookTitle,'\'','-');
            bookTitle = replaceAll(bookTitle,',','-');
            bookTitle = replaceAll(bookTitle,'--','-');
            bookTitle = replaceAll(bookTitle,'--','-');
            bookTitle = replaceAll(bookTitle,'/','-');
            ebookHtmlStr += "<div class=''>" ;
            ebookHtmlStr +=    "<a href='/" + bookTitle + "/ebook-details?siteName="+siteName+"&pBookId=" + books[i].id + "&preview=true'>" ;
            ebookHtmlStr +=    "<div class='img-wrapper'>";
            ebookHtmlStr += "<div class='bookShadow'>";

            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                ebookHtmlStr +="<div class='uncover'>"+
                    "<p>"+books[i].title+"</p>"+
                    "</div>";
            }
            else {
                ebookHtmlStr += "<img src='" + books[i].coverImage + "' alt=''/>" ;
            }
            ebookHtmlStr +="</div>";

            var offPercentage = (books[i].listPrice - books[i].offerPrice)
            offPercentage  = Math.round(offPercentage/books[i].listPrice * 100)

            ebookHtmlStr += "<div class='content-wrapper'>" +
                "<p>" + books[i].title + "</p>"+
                "<div class='price-sec'>" +
                    "<span class='offerPriceText'>&#x20b9 "+books[i].offerPrice+"</span>"+
                    "<span class='listPriceText'>&#x20b9 "+books[i].listPrice+"</span>";
                    if (books[i].offerPrice > 0 && books[i].listPrice > 0){
                        ebookHtmlStr +="<span class='offerPercentage'>("+offPercentage+"% off)</span>";
                    }
            ebookHtmlStr +="</div>"+
                "</div>";
            ebookHtmlStr += "</div>" +
                "</div>" +
                "</a>" +
                "</div>";
            document.getElementById('relatedBooks').innerHTML=ebookHtmlStr;
            noOfEbooks++;


        }

        if(noOfEbooks>0)   {
            $("#relatedBooksContainer").show();

        } else {
            $("#relatedBooksContainer").css('display', 'none');
        }


        $('.loading-icon').addClass('hidden');

        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%11];
        }
    }

    function displayRelatedBooks(data){
        <%if(!("1".equals(""+session["siteId"]) || "28".equals(""+session["siteId"]))){%>
        fetchBestSellerBooks();
        <%}%>
        if(data === null || data === [] || data === '' || data.books === null || data.books === [] || data.books === '' || data.books === '[]'){
            $("#relatedBooksContainer").css('display', 'none');
            return;
        }
        var books = JSON.parse(data.books);
        var newObj = {};
        var newArr = [];


        var showMore=data.eBooksUrl;

        if(showMore !=null || showMore !=''){
            <%if("true".equals(""+session["commonWhiteLabel"])){%>
            $('#show-more').attr('href','/sp/${session["siteName"]}/store?');
            <%}else{%>
            $('#show-more').attr('href','/${session["entryController"]}/store?'+showMore);
            <%}%>
        }
        else{
            $('#show-more').hide();
        }
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        var noOfEbooks  = 0;
        var ebookHtmlStr="";
        var imgSrc = "";
        for(var i=0; i<books.length && i<=9; i++){
            imgSrc = books[i].coverImage;
            if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
                imgSrc = books[i].coverImage;
                imgSrc = imgSrc.replace("~", ":");
            } else {
                imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
            }
            ebookHtmlStr += "<div class=''>" ;
            if(tokenId=="") {
                ebookHtmlStr +=    "<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true&publisher="+books[i].publisher+"&pageSource=relatedBooks'>" ;
            }else{
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-')+ "/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true&tokenId=" + tokenId + "'&publisher="+books[i].publisher+"&pageSource=relatedBooks'>"
            }

            ebookHtmlStr +=    "<div class='img-wrapper'>";

            ebookHtmlStr += "<div class='bookShadow'>";

            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                ebookHtmlStr +="<div class='uncover'>"+
                    "<p>"+books[i].title+"</p>"+
                    "</div>";
            }
            else {
                ebookHtmlStr += "<img src='" + imgSrc + "' alt=''/>" ;
            }
            ebookHtmlStr +="</div>";

            var bookOfferPriceVar=0
            var bookListPriceVar=0
            var offPercentage = 0
            if (books[i].listPrice && books[i].offerPrice){
                offPercentage = (books[i].listPrice - books[i].offerPrice)
                offPercentage  = Math.round(offPercentage/books[i].listPrice * 100)
                bookOfferPriceVar = books[i].offerPrice
                bookListPriceVar = books[i].listPrice
            }else if(books[i].testsPrice && books[i].testsListprice){
                offPercentage = (books[i].testsListprice - books[i].testsPrice)
                offPercentage  = Math.round(offPercentage/books[i].testsListprice * 100)
                bookOfferPriceVar = books[i].testsPrice
                bookListPriceVar = books[i].testsListprice
            }

            ebookHtmlStr += "<div class='content-wrapper'>" +
                "<p>" + books[i].title + "</p>"+
                "<div class='price-sec'>" +
                    "<span class='offerPriceText'>&#x20b9 "+bookOfferPriceVar+"</span>"+
                    "<span class='listPriceText'>&#x20b9 "+bookListPriceVar+"</span>";
                    if (bookOfferPriceVar > 0 && bookListPriceVar > 0){
                        ebookHtmlStr +="<span class='offerPercentage'>("+offPercentage+"% off)</span>";
                    }
            ebookHtmlStr +="</div>";
            ebookHtmlStr +="</div>";
            if(books[i].bookType === "bookgpt" || books[i].bookType ==="ebookwithai"){
                ebookHtmlStr +="<a href='/prompt/bookgpt?bookId="+books[i].id+"' style='padding: 5px 10px;background: #F79420;color: #fff;border-radius: 5px;display: flex;text-align: center;margin-top: auto;justify-content: center;'>Try for Free</a>"
            }else if(books[i].printOnly == false){
                ebookHtmlStr +="<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook?siteName="+siteName+"&bookId=" + books[i].id+"&preview=true' style='padding: 5px 10px;background: #F79420;color: #fff;border-radius: 5px;display: flex;text-align: center;margin-top: auto;justify-content: center;'>Try for Free</a>"
            }
            ebookHtmlStr += "</div>" +
                "</div>" +
                "</a>" +
                "</div>";
            document.getElementById('relatedBooks').innerHTML=ebookHtmlStr;
            noOfEbooks++;


        }

        if(noOfEbooks>0)   {
            $("#relatedBooksContainer").show();
           
        } else {
            $("#relatedBooksContainer").css('display', 'none');
        }


        $('.loading-icon').addClass('hidden');

        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%11];
        }
    }

    function displayBestSellerBooks(data){

        if(data === null || data === [] || data === '' || data.books === null || data.books === [] || data.books === '' || data.books === '[]'){
            $("#bestSellerBooksContainer").css('display', 'none');
            return;
        }
        var books = JSON.parse(data.books);
        var newObj = {};
        var newArr = [];
        for(var i=0; i< books.length; i++){
            newObj[books[i]['id']] = books[i];
        }
        var keys = Object.keys(newObj);
        for(var i=keys.length-1; i>=0; i--){
            newArr.push(newObj[keys[i]])
        }
        books=newArr

        var showMore=data.eBooksUrl;

        if(showMore !=null || showMore !=''){
            <%if("true".equals(""+session["commonWhiteLabel"])){%>
            $('#show-more').attr('href','/sp/${session["siteName"]}/store?');
            <%}else{%>
            $('#show-more').attr('href','/${session["entryController"]}/store?'+showMore);
            <%}%>
        }
        else{
            $('#show-more').hide();
        }
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        var noOfEbooks  = 0;
        var ebookHtmlStr="";
        var imgSrc = "";
        for(var i=0; i<books.length && i<=9; i++){
            imgSrc = books[i].coverImage;
            if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
                imgSrc = books[i].coverImage;
                imgSrc = imgSrc.replace("~", ":");
            } else {
                imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
            }
            ebookHtmlStr += "<div class=''>" ;
            if(tokenId=="") {
                ebookHtmlStr +=    "<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true&publisher="+books[i].publisher+"'>" ;
            }else{
                ebookHtmlStr += "<a href='/" + replaceAll(books[i].title,' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true&tokenId=" + tokenId + "'&publisher="+books[i].publisher+"'>"
            }

            ebookHtmlStr +=    "<div class='img-wrapper'>";

            ebookHtmlStr += "<div class='bookShadow'>";

            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                ebookHtmlStr +="<div class='uncover'>"+
                    "<p>"+books[i].title+"</p>"+
                    "</div>";
            }
            else {
                ebookHtmlStr += "<img src='" + imgSrc + "' alt=''/>" ;
            }
            ebookHtmlStr +="</div>";
            var bookOfferPriceVar=0
            var bookListPriceVar=0
            var offPercentage = 0
            if (books[i].listPrice && books[i].offerPrice){
                offPercentage = (books[i].listPrice - books[i].offerPrice)
                offPercentage  = Math.round(offPercentage/books[i].listPrice * 100)
                bookOfferPriceVar = books[i].offerPrice
                bookListPriceVar = books[i].listPrice
            }else if(books[i].testsPrice && books[i].testsListprice){
                offPercentage = (books[i].testsListprice - books[i].testsPrice)
                offPercentage  = Math.round(offPercentage/book.testsListprice * 100)
                bookOfferPriceVar = books[i].testsPrice
                bookListPriceVar = books[i].testsListprice
            }
            ebookHtmlStr += "<div class='content-wrapper'>" +
                        "<p>" + books[i].title + "</p>"+
                        "<div class='price-sec'>" +
                        "<span class='offerPriceText'>&#x20b9 "+bookOfferPriceVar+"</span>"+
                        "<span class='listPriceText'>&#x20b9 "+bookListPriceVar+"</span>";
                    if (bookOfferPriceVar > 0 && bookListPriceVar > 0){
                        ebookHtmlStr +="<span class='offerPercentage'>("+offPercentage+"% off)</span>";
                    }
            ebookHtmlStr +="</div>";
            ebookHtmlStr += "</div>" +
                "</div>" +
                "</a>" +
                "</div>";
            document.getElementById('bestSellerBooks').innerHTML=ebookHtmlStr;
            noOfEbooks++;


        }

        if(noOfEbooks>0)   {
            $("#bestSellerBooksContainer").show();
            // $("#bestSellerBooks").slick('slickAdd',
            //     ebookHtmlStr
            // );
        } else {
            $("#bestSellerBooksContainer").css('display', 'none');
        }


        $('.loading-icon').addClass('hidden');

        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%11];
        }
    }

    $(document).ready(function(){
        fetchRelatedBooks();
    })

    function replaceAll(str, find, replace) {
        if (str == undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }
    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }
</script>

