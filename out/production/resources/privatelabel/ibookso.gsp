<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/privatelabel/navheader_new"></g:render>
<style>
.new-categoryList {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 1rem;
    margin-top: 2rem;
}
@media (max-width: 768px) {
    .new-categoryList {
        grid-template-columns: repeat(1, 1fr);
    }
}
.new-categoryList_item-link {
    position: relative;
}
.new-categoryList_item-link img {
    width: 100%;
}
.new-categoryList_item-link button {
    position: absolute;
    top: 35px;
    right: 20px;
    background: transparent;
    border: 1px solid rgba(0, 0, 0, 0.6);
    width: 125px;
    height: 32px;
    color: rgba(0, 0, 0, 0.6);
}
</style>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div id="wrapper" class="index-page">

    <div class="header-wrapper">
        <div class="bg-wrapper-sec">

            <div id="slider-desktop" class="carousel slide this-is-a-web-view-slider" data-ride="carousel">
                <ol class="carousel-indicators" id="slider-desktop-indicators"></ol>
                <div class="carousel-inner" id="slider-desktop-views"></div>
            </div>

            <div id="bindAllItem">
                <div id="slider-mobile" class="carousel slide this-is-a-responsive-view-slider" data-ride="carousel">
                    <ol class="carousel-indicators" id="slider-mobile-indicators"></ol>
                    <div class="carousel-inner" id="slider-mobile-views"></div>
                </div>
            </div>

        </div>
    </div>

    <div class="categories-section">
        <div class="container">
            <div class="justify-content-center global-search searching-book-store">
                <form class="form-inline col-12 p-0">
                    <h1 style="color: red">Never pay more</h1><br><br>

                    </form>
            </div>
            <div class="justify-content-center global-search searching-book-store">
                <form class="form-inline col-12 p-0">
                    <h4>Search and compare price</h4><br><br>

                    <input type="text" class="form-control form-control-modifier border-0 shadow typeahead w-100" name="search-print-book-store" id="search-print-book-store" autocomplete="off" placeholder="Search your product for price comparison." style="height: 50px;">
                    <button type="button" class="btn bg-transparent text-primary text-primary-modifier" onclick="javascript:submitPrintSearch('search-print-book-store');" id="search-btn-store"><i class="material-icons">search</i></button>
                </form>
            </div>
            <div class="row"><br><br></div>
            <div class="row">
                <div class="col-md-10 col-lg-7">
                    <div>
                        <h4>Categories</h4>
                    </div>
                </div>
            </div>

            <g:render template="/printbooks/categories"></g:render>

        </div>
    </div>



</div>

<script>
    var base_url = location.protocol + '//' + location.host;
    window.history.replaceState("", "", base_url);
    var activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    const groupedData = activeCategoriesSyllabus.reduce((acc, obj) => {
        const key = obj.level;
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(obj);
        return acc;
    }, {});

    let html = '';

    for (const key in groupedData) {
        const group = groupedData[key];
        html += "<div class='category_level'>"+
            "<h4><a href='/${session["entryController"]}/store?level="+encodeURIComponent(key)+"' style='font-size:inherit;color: inherit;font-weight: inherit;'> "+key+"</a></h4>";
        html += "<div class='category_cards'>";
        for (const obj of group) {
            html +="<a href='/${session["entryController"]}/store?level="+encodeURIComponent(key)+"&syllabus="+encodeURIComponent(obj.syllabus)+"' class='category_card'>"+
                "<div class='category_card-title'>"+
                "<p>"+obj.syllabus+"</p>"+
                "</div>"+
                "</a>";
        }
        html += '</div>'+
            "</div>";
    }



    function logout(){
        setCookie("level","");
        setCookie("syllabus","");
        setCookie("grade","");
        setCookie("subject","");
        window.location.href = '/logoff';
    }
</script>

<g:render template="/privatelabel/footer_new"></g:render>
<g:render template="/privatelabel/ibooksoBanner"></g:render>


</body>
</html>
