<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
          integrity="sha384-pzjw8/3q4H9mgtz8eG4xtX6p7xfo0j7RjHfo5mtN7jqKp2yjUarxNSf+qQ5cKGhq"
          crossorigin="anonymous">
    <style>
    /* Custom CSS for enhanced styling */

    h1 {
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .chart-container {
        margin-bottom: 20px;
        height: 400px;
    }

    .table-responsive {
        margin-bottom: 20px;
    }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function () {
            $("#applyFilterBtn").click(function () {
                var selectedMonth = $("#monthFilter").val();

                $.ajax({
                    url: "${createLink(action: 'filterData')}",
                    type: "GET",
                    data: {selectedMonth: selectedMonth},
                    success: function (data) {
                        renderSection1(data.section1Data);
                        renderSection2(data.section2Data);
                        renderSection3(data.section3Data);
                        renderSection4(data.section4Data);
                    },
                    error: function (xhr, status, error) {
                        console.error("Error occurred while applying filter: " + error);
                    }
                });
            });

            function renderSection1(data) {
                var section1Container = $("#section1Container");
                section1Container.empty();

                var table = $("<table>").addClass("table table-striped table-hover");
                var thead = $("<thead>").appendTo(table);
                var tbody = $("<tbody>").appendTo(table);

                var tr = $("<tr>").appendTo(thead);
                $("<th>").text("Date").appendTo(tr);
                $("<th>").text("Books Sold").appendTo(tr);
                $("<th>").text("Sales Value").appendTo(tr);

                var totalBooksSold = 0;
                var totalSalesValue = 0;

                data.forEach(function (row) {
                    tr = $("<tr>").appendTo(tbody);
                    $("<td>").text(formatDate(row.date)).appendTo(tr);
                    $("<td>").text(row.booksSold).appendTo(tr);
                    $("<td>").text(row.salesValue).appendTo(tr);

                    totalBooksSold += row.booksSold;
                    totalSalesValue += row.salesValue;
                });

                tr = $("<tr>").appendTo(tbody);
                $("<td>").text("Total").appendTo(tr);
                $("<td>").text(totalBooksSold).appendTo(tr);
                $("<td>").text(totalSalesValue).appendTo(tr);

                section1Container.append(table);
            }

            function renderSection2(data) {
                var section2Container = $("#section2Container");
                section2Container.empty();

                // Render table
                var table = $("<table>").addClass("table table-striped table-hover");
                var thead = $("<thead>").appendTo(table);
                var tbody = $("<tbody>").appendTo(table);

                var tr = $("<tr>").appendTo(thead);
                $("<th>").text("Publisher").appendTo(tr);
                $("<th>").text("Sales Value").appendTo(tr);
                $("<th>").text("Number of books").appendTo(tr);

                data.forEach(function(row) {
                    tr = $("<tr>").appendTo(tbody);
                    $("<td>").text(row.publisher).appendTo(tr);
                    $("<td>").text(row.salesValue).appendTo(tr);
                    $("<td>").text(row.booksSold).appendTo(tr);
                });

                section2Container.append(table);

                // Render chart
                var chartContainer = $("<div>").addClass("chart-container").css({
                    width: "400px",
                    height: "300px"
                }).appendTo(section2Container);

                var chartCanvas = $("<canvas>").appendTo(chartContainer);

                var publishers = data.map(function (row) {
                    return row.publisher;
                });
                var salesValues = data.map(function (row) {
                    return row.salesValue;
                });

                var chartData = {
                    maintainAspectRatio: false, // Disable aspect ratio to control width and height
                    responsive: true, // Allow the chart to be responsive within its container
                    width: 400, // Set the width of the chart
                    height: 300,
                    labels: publishers,
                    datasets: [{
                        data: salesValues,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(255, 206, 86, 0.6)',
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)',
                            'rgba(255, 99, 132, 0.6)'
                        ],
                        borderWidth: 1

                    }]
                };

                var chartOptions = {
                    responsive: true,
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            fontColor: '#333'
                        }
                    }
                };

                new Chart(chartCanvas, {
                    type: 'pie',
                    data: chartData,
                    options: chartOptions
                });
            }

            function renderSection3(data) {
                var section3Container = $("#section3Container");
                section3Container.empty();

                // Render table
                var table = $("<table>").addClass("table table-striped table-hover");
                var thead = $("<thead>").appendTo(table);
                var tbody = $("<tbody>").appendTo(table);

                var tr = $("<tr>").appendTo(thead);
                $("<th>").text("Level").appendTo(tr);
                $("<th>").text("Syllabus").appendTo(tr);
                $("<th>").text("Books Sold").appendTo(tr);

                data.forEach(function (row) {
                    tr = $("<tr>").appendTo(tbody);
                    $("<td>").text(row.level).appendTo(tr);
                    $("<td>").text(row.syllabus).appendTo(tr);
                    $("<td>").text(row.booksSold).appendTo(tr);
                });

                section3Container.append(table);

                // Render chart
                var chartContainer = $("<div>").addClass("chart-container").css({
                    width: "400px",
                    height: "300px"
                }).appendTo(section3Container);
                var chartCanvas = $("<canvas>").appendTo(chartContainer);

                var levels = data.map(function (row) {
                    return row.level;
                });
                var booksSold = data.map(function (row) {
                    return row.booksSold;
                });

                var chartData = {
                    labels: levels,
                    datasets: [{
                        data: booksSold,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(255, 206, 86, 0.6)',
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)',
                            'rgba(255, 99, 132, 0.6)'
                        ],
                        borderWidth: 1
                    }]
                };

                var chartOptions = {
                    responsive: true,
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            fontColor: '#333'
                        }
                    }
                };

                new Chart(chartCanvas, {
                    type: 'pie',
                    data: chartData,
                    options: chartOptions
                });
            }

            function renderSection4(data) {
                var section4Container = $("#section4Container");
                section4Container.empty();

                // Render table
                var table = $("<table>").addClass("table table-striped table-hover");
                var thead = $("<thead>").appendTo(table);
                var tbody = $("<tbody>").appendTo(table);

                var tr = $("<tr>").appendTo(thead);
                $("<th>").text("Site").appendTo(tr);
                $("<th>").text("Books Sold").appendTo(tr);
                $("<th>").text("Sales Value").appendTo(tr);

                data.forEach(function (row) {
                    tr = $("<tr>").appendTo(tbody);
                    $("<td>").text(row.siteName).appendTo(tr);
                    $("<td>").text(row.booksSold).appendTo(tr);
                    $("<td>").text(row.salesValue).appendTo(tr);
                });

                section4Container.append(table);

                // Render chart
                var chartContainer = $("<div>").addClass("chart-container").css({
                    width: "400px",
                    height: "300px"
                }).appendTo(section4Container);
                var chartCanvas = $("<canvas>").appendTo(chartContainer);

                var siteIds = data.map(function (row) {
                    return row.siteName;
                });
                var salesValues = data.map(function (row) {
                    return row.salesValue;
                });

                var chartData = {
                    labels: siteIds,
                    datasets: [{
                        data: salesValues,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(255, 206, 86, 0.6)',
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)',
                            'rgba(255, 99, 132, 0.6)'
                        ],
                        borderWidth: 1
                    }]
                };

                var chartOptions = {
                    responsive: true,
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            fontColor: '#333'
                        }
                    }
                };

                new Chart(chartCanvas, {
                    type: 'pie',
                    data: chartData,
                    options: chartOptions
                });
            }

            function formatDate(dateString) {
                var date = new Date(dateString);
                var day = String(date.getDate()).padStart(2, '0');
                var month = String(date.getMonth() + 1).padStart(2, '0');
                var year = date.getFullYear();

                return day + "/" + month + "/" + year;
            }
        });
    </script>
</head>
<body>
<div class="container"><br><br>
    <h1>Sales Dashboard</h1>
    <hr>
    <div class="form-group">
        <label for="monthFilter">Select Month:</label>
        <select id="monthFilter" name="selectedMonth" class="form-control">
            <g:each in="${months}" var="month">
                <option value="${month}" ${month == selectedMonth ? 'selected' : ''}>${month}</option>
            </g:each>
        </select>

    </div>
    <button id="applyFilterBtn" class="btn btn-primary">Apply Filter</button>
    <hr>
    <h4 style="color: red">Daily Sales</h4>
    <div id="section1Container" class="table-responsive"></div>
    <hr>
    <h4 style="color: red">Publishers</h4>
    <div id="section2Container" class="table-responsive"></div>
    <hr>
    <h4 style="color: red">Categories</h4>
    <div id="section3Container" class="table-responsive"></div>
    <hr>
    <h4 style="color: red">Sites</h4>
    <div id="section4Container" class="table-responsive"></div>
</div>
</body>

<g:render template="/${session['entryController']}/footer_new"></g:render>
