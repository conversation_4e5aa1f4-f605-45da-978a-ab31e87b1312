<%@ page import="com.wonderslate.data.SiteMst" %>
<%if(!'true'.equals(params.hideJS)) {%>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js" integrity="sha512-0bEtK0USNd96MnO4XhH8jhv3nyRF0eK87pJke6pkYf3cM0uDIhNJy9ltuzqgypoIFXw3JSuiy04tVk4AjpZdZw==" crossorigin="anonymous"></script>
<%}%>
<script src="https://apis.google.com/js/api:client.js" defer></script>


<script type="text/javascript">

    var siteId = "<%=session.getAttribute("siteId")%>";
    jQuery(document).ready(function() {
        var windowW = window.innerWidth || $(window).width();
        var fullwidth;
        var fullscreen;

        if (windowW > 767) {
            fullwidth = "off";
            fullscreen = "on";
        } else {
            fullwidth = "on";
            fullscreen = "off";
        }
    }); //ready
</script>
<script>
    function scrollFunction() {
        if(!$("#myBtn").length) return;

        if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
            $("#myBtn").css("display", "block");
        } else {
            $("#myBtn").css("display", "none");
        }
    }

    // When the user clicks on the button, scroll to the top of the document
    function topFunction() {
        document.body.scrollTop = 0; // For Chrome, Safari and Opera
        document.documentElement.scrollTop = 0; // For IE and Firefox
    }

    if(siteId !==6) {
        $(window).scroll(function() {
            scrollFunction();
            var scroll = $(window).scrollTop();
            if (scroll >= 100) {
                $('header').addClass('header-shadow');
                $('body').addClass('custom-fix');
            } else {
                $('header').removeClass('header-shadow');
                $('body').removeClass('custom-fix');
            }
        });
    }


</script>

<script>
    $('.user-profile-dropdown').mouseenter(function() {
        $(this).addClass('open');
    }).mouseleave(function() {
        $(this).removeClass('open');
    });

    var serverPath= "${request.contextPath}";

    function showregister(registerType){
        $("#registerModal").modal("show");
        document.getElementById('forgotPassword').style.display = 'none';
        document.getElementById('tandc').style.display = 'none';

        if("login"==registerType) {
            document.getElementById('signup').style.display = 'none';
            document.getElementById('login').style.display = 'block';
            document.getElementById('loginFailed').style.display = 'none';
        } else {
            document.getElementById('login').style.display = 'none';
            document.getElementById('signup').style.display = 'block';
        }
    }

    var syllabusType="${syllabusType}";

    var country ="${country}";
    var noOfNotifications=0;
    var messagetype="message";



    $('#chapter-details-tabs a').click(function (e) {
        e.preventDefault()
        $(this).tab('show');
    });

    function getLocalDate(dateStr){
        var newDateStr = dateStr.substr(0,dateStr.length-18)+" "+dateStr.substr(dateStr.length-5);
        return newDateStr;
    }

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    function deleteCookie(cookiename){
        document.cookie = cookiename + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    }

    function setSelectedValue(selectObj, valueToSet) {
        for (var i = 0; i < selectObj.options.length; i++) {
            if (selectObj.options[i].text.replace(/\W+/g, '') == valueToSet.replace(/\W+/g, '')) {
                selectObj.options[i].selected = true;
                return;
            }
        }
    }

    function updateQuizPoints(quizId,correctAnswers) {
        <g:remoteFunction controller="funlearn" action="addQuizPoints" params="'quizId='+quizId+'&correctAnswers='+correctAnswers" />
    }

    function getStars(stars) {
        var starsString="";
        var starsString = "<span class='orange'>";

        for(var i = 0; i < 5; i++) {
            if(stars >= 1) {
                stars = stars - 1;
                starsString += "<i class='fa fa-star fa-x'>";
            } else if(stars > 0 && stars <= 1) {
                stars = stars - 1;
                starsString += "<i class='fa fa-star-half-empty fa-x'></i>";
            } else {
                starsString += "<i class='fa fa-star-o fa-x'>";
            }
        }

        starsString += "</span>";
        return starsString;
    }

    function showLoader(){
        $('body').removeClass('loaded');
    }

    function removeLoader(){
        $('body').addClass('loaded');
    }

    $('.smartbanner').on('click', '#close-advert', function() {
        $('html, body').addClass('no-padding');
    });

    function showRegistration() {
        showSignUpModal();
        <g:remoteFunction controller="wonderpublish" action="storeBookIdForPurchase" params="'bookId='+purchaseBookId" />
    }

    $(document).ready(function() {
        $(window).scroll(function() {
            var sliderHeight = $('.smart-books-slider').height() || $('.arihant-slider').height();
            var headerHeight = $('.sticky-header').height() || $('.mobile-header').height();
            var scrollPage = $(window).scrollTop();
            var siteId;

            if(siteId != 9) {
                if(scrollPage >= headerHeight) {
                    $('.sticky-header').addClass('fixed-header');
                    $('.tab-sub-categories, .book-read-tabs').addClass('fixed-tabs-catos-mobile').css({
                        'top' : '0',
                        'transition': 'all .3s ease'
                    });
                } else {
                    $('.sticky-header').removeClass('fixed-header');
                    $('.tab-sub-categories, .book-read-tabs').removeClass('fixed-tabs-catos-mobile').css({
                        'top' : ''
                    });
                }

                if(scrollPage >= sliderHeight) {
                    $('.tabs-holder').addClass('fixed-tabs-holder-desktop').css({
                        'top' : headerHeight
                    });

                    $('.tab-sub-categories').addClass('tab-margin');
                } else {
                    $('.tabs-holder').removeClass('fixed-tabs-holder-desktop').css({
                        'top' : ''
                    });

                    $('.tab-sub-categories').removeClass('tab-margin');
                }
            }
        });

        $('#htmlreadingcontent').scroll(function() {
            if($('#htmlreadingcontent').scrollTop() > 100) {
                $('.section-btns').slideUp();
            }

            if($('#htmlreadingcontent').scrollTop() <= 100) {
                $('.section-btns').slideDown();
            }

            if($('#htmlreadingcontent').scrollTop() + $('#htmlreadingcontent').scrollTop() == $(document).height()) {
                $('.section-btns').slideDown();
            }
        });
    });

    $('.tab-sub-categories-item').on('click', '.tab-sub-categories-item-btn', function() {
        $('.tab-sub-categories-item .tab-sub-categories-item-btn').removeClass('active');
        $(this).addClass('active');
    });

    function showSignup() {
        $('#social-login').finish().delay().fadeOut(100, function(){
            $('#social-signup').fadeIn(100);
            $('#sign-up-div').hide();
        });
    }

    function showSignIn() {
        $('#social-signup').finish().delay().fadeOut(100, function(){
            $('#social-login').fadeIn(100);
            $('#sign-in-div').hide();
        });
    }

    function hideSocialSignIn() {
        $('#social-login').finish().delay().fadeOut(100, function(){
            $('#sign-in-div').fadeIn(100);
        });
    }

    function hideSocialSignUp() {
        $('#social-signup').finish().delay().fadeOut(100, function(){
            $('#sign-up-div').fadeIn(100);
        });
    }

    function showFGpassword() {
        $('#sign-in-div').finish().delay().fadeOut(100, function(){
            $('#forgot-password-div').fadeIn(100);
        });
    }

    function bckLogin() {
        $('#forgot-password-div').finish().delay().fadeOut(100, function(){
            $('#sign-in-div').fadeIn(100);
            $('#reset-password-div').hide();
            $('#google-password-div').hide();
        });
    }

    function showSignUpModal() {
        $('#signin-modal').modal('show');
        $('#social-signup').show();
        $('#social-login').hide();
        $('#sign-up-div').hide();
        $('#sign-in-div').hide();
        $('#forgot-password-div').hide();
    }

    function showSignInModal() {
        $('#loginSignup').modal('show');
        $('#connecting-div').hide();
        $('#social-signup').hide();
        $('#social-login').show();
        $('#sign-in-div').hide();
        $('#sign-up-div').hide();
        $('#loginFailed').hide();
        $('#forgot-password-div').hide();
    }

    (function(window) {
        'use strict';

        var Waves = Waves || {};
        var $$ = document.querySelectorAll.bind(document);

        // Find exact position of element
        function isWindow(obj) {
            return obj !== null && obj === obj.window;
        }

        function getWindow(elem) {
            return isWindow(elem) ? elem : elem.nodeType === 9 && elem.defaultView;
        }

        function offset(elem) {
            var docElem, win,
                box = {top: 0, left: 0},
                doc = elem && elem.ownerDocument;

            docElem = doc.documentElement;

            if(typeof elem.getBoundingClientRect !== typeof undefined) {
                box = elem.getBoundingClientRect();
            }

            win = getWindow(doc);
            return {
                top: box.top + win.pageYOffset - docElem.clientTop,
                left: box.left + win.pageXOffset - docElem.clientLeft
            };
        }

        function convertStyle(obj) {
            var style = '';

            for(var a in obj) {
                if(obj.hasOwnProperty(a)) {
                    style += (a + ':' + obj[a] + ';');
                }
            }

            return style;
        }

        var Effect = {
            // Effect delay
            duration: 750,

            show: function(e, element) {
                // Disable right click
                if (e.button === 2) {
                    return false;
                }

                var el = element || this;

                // Create ripple
                var ripple = document.createElement('div');
                ripple.className = 'waves-ripple';
                el.appendChild(ripple);

                // Get click coordinate and element witdh
                var pos         = offset(el);
                var relativeY   = (e.pageY - pos.top);
                var relativeX   = (e.pageX - pos.left);
                var scale       = 'scale('+((el.clientWidth / 100) * 10)+')';

                // Support for touch devices
                if ('touches' in e) {
                    relativeY   = (e.touches[0].pageY - pos.top);
                    relativeX   = (e.touches[0].pageX - pos.left);
                }

                // Attach data to element
                ripple.setAttribute('data-hold', Date.now());
                ripple.setAttribute('data-scale', scale);
                ripple.setAttribute('data-x', relativeX);
                ripple.setAttribute('data-y', relativeY);

                // Set ripple position
                var rippleStyle = {
                    'top': relativeY+'px',
                    'left': relativeX+'px'
                };

                ripple.className = ripple.className + ' waves-notransition';
                ripple.setAttribute('style', convertStyle(rippleStyle));
                ripple.className = ripple.className.replace('waves-notransition', '');

                // Scale the ripple
                rippleStyle['-webkit-transform'] = scale;
                rippleStyle['-moz-transform'] = scale;
                rippleStyle['-ms-transform'] = scale;
                rippleStyle['-o-transform'] = scale;
                rippleStyle.transform = scale;
                rippleStyle.opacity   = '1';

                rippleStyle['-webkit-transition-duration'] = Effect.duration + 'ms';
                rippleStyle['-moz-transition-duration']    = Effect.duration + 'ms';
                rippleStyle['-o-transition-duration']      = Effect.duration + 'ms';
                rippleStyle['transition-duration']         = Effect.duration + 'ms';

                rippleStyle['-webkit-transition-timing-function'] = 'cubic-bezier(0.250, 0.460, 0.450, 0.940)';
                rippleStyle['-moz-transition-timing-function']    = 'cubic-bezier(0.250, 0.460, 0.450, 0.940)';
                rippleStyle['-o-transition-timing-function']      = 'cubic-bezier(0.250, 0.460, 0.450, 0.940)';
                rippleStyle['transition-timing-function']         = 'cubic-bezier(0.250, 0.460, 0.450, 0.940)';

                ripple.setAttribute('style', convertStyle(rippleStyle));
            },

            hide: function(e) {
                TouchHandler.touchup(e);

                var el = this;
                var width = el.clientWidth * 1.4;

                // Get first ripple
                var ripple = null;
                var ripples = el.getElementsByClassName('waves-ripple');
                if (ripples.length > 0) {
                    ripple = ripples[ripples.length - 1];
                } else {
                    return false;
                }

                var relativeX   = ripple.getAttribute('data-x');
                var relativeY   = ripple.getAttribute('data-y');
                var scale       = ripple.getAttribute('data-scale');

                // Get delay beetween mousedown and mouse leave
                var diff = Date.now() - Number(ripple.getAttribute('data-hold'));
                var delay = 350 - diff;

                if (delay < 0) {
                    delay = 0;
                }

                // Fade out ripple after delay
                setTimeout(function() {
                    var style = {
                        'top': relativeY+'px',
                        'left': relativeX+'px',
                        'opacity': '0',

                        // Duration
                        '-webkit-transition-duration': Effect.duration + 'ms',
                        '-moz-transition-duration': Effect.duration + 'ms',
                        '-o-transition-duration': Effect.duration + 'ms',
                        'transition-duration': Effect.duration + 'ms',
                        '-webkit-transform': scale,
                        '-moz-transform': scale,
                        '-ms-transform': scale,
                        '-o-transform': scale,
                        'transform': scale,
                    };

                    ripple.setAttribute('style', convertStyle(style));

                    setTimeout(function() {
                        try {
                            el.removeChild(ripple);
                        } catch(e) {
                            return false;
                        }
                    }, Effect.duration);
                }, delay);
            },

            // Little hack to make <input> can perform waves effect
            wrapInput: function(elements) {
                for (var a = 0; a < elements.length; a++) {
                    var el = elements[a];

                    if (el.tagName.toLowerCase() === 'input') {
                        var parent = el.parentNode;

                        // If input already have parent just pass through
                        if (parent.tagName.toLowerCase() === 'i' && parent.className.indexOf('waves-effect') !== -1) {
                            continue;
                        }

                        // Put element class and style to the specified parent
                        var wrapper = document.createElement('i');
                        wrapper.className = el.className + ' waves-input-wrapper';

                        var elementStyle = el.getAttribute('style');

                        if (!elementStyle) {
                            elementStyle = '';
                        }

                        wrapper.setAttribute('style', elementStyle);

                        el.className = 'waves-button-input';
                        el.removeAttribute('style');

                        // Put element as child
                        parent.replaceChild(wrapper, el);
                        wrapper.appendChild(el);
                    }
                }
            }
        };

        /**
         * Disable mousedown event for 500ms during and after touch
         */
        var TouchHandler = {
            /* uses an integer rather than bool so there's no issues with
             * needing to clear timeouts if another touch event occurred
             * within the 500ms. Cannot mouseup between touchstart and
             * touchend, nor in the 500ms after touchend. */
            touches: 0,
            allowEvent: function(e) {
                var allow = true;

                if (e.type === 'touchstart') {
                    TouchHandler.touches += 1; //push
                } else if (e.type === 'touchend' || e.type === 'touchcancel') {
                    setTimeout(function() {
                        if (TouchHandler.touches > 0) {
                            TouchHandler.touches -= 1; //pop after 500ms
                        }
                    }, 500);
                } else if (e.type === 'mousedown' && TouchHandler.touches > 0) {
                    allow = false;
                }

                return allow;
            },
            touchup: function(e) {
                TouchHandler.allowEvent(e);
            }
        };

        /**
         * Delegated click handler for .waves-effect element.
         * returns null when .waves-effect element not in "click tree"
         */
        function getWavesEffectElement(e) {
            if (TouchHandler.allowEvent(e) === false) {
                return null;
            }

            var element = null;
            var target = e.target || e.srcElement;

            while (target.parentElement !== null) {
                if (!(target instanceof SVGElement) && target.className.indexOf('waves-effect') !== -1) {
                    element = target;
                    break;
                } else if (target.classList.contains('waves-effect')) {
                    element = target;
                    break;
                }
                target = target.parentElement;
            }

            return element;
        }

        /**
         * Bubble the click and show effect if .waves-effect elem was found
         */
        function showEffect(e) {
            var element = getWavesEffectElement(e);

            if (element !== null) {
                Effect.show(e, element);

                if ('ontouchstart' in window) {
                    element.addEventListener('touchend', Effect.hide, false);
                    element.addEventListener('touchcancel', Effect.hide, false);
                }

                element.addEventListener('mouseup', Effect.hide, false);
                element.addEventListener('mouseleave', Effect.hide, false);
            }
        }

        Waves.displayEffect = function(options) {
            options = options || {};

            if ('duration' in options) {
                Effect.duration = options.duration;
            }

            //Wrap input inside <i> tag
            Effect.wrapInput($$('.waves-effect'));

            if ('ontouchstart' in window) {
                document.body.addEventListener('touchstart', showEffect, false);
            }

            document.body.addEventListener('mousedown', showEffect, false);
        };

        /**
         * Attach Waves to an input element (or any element which doesn't
         * bubble mouseup/mousedown events).
         *   Intended to be used with dynamically loaded forms/inputs, or
         * where the user doesn't want a delegated click handler.
         */
        Waves.attach = function(element) {
            //FUTURE: automatically add waves classes and allow users
            // to specify them with an options param? Eg. light/classic/button
            if (element.tagName.toLowerCase() === 'input') {
                Effect.wrapInput([element]);
                element = element.parentElement;
            }

            if ('ontouchstart' in window) {
                element.addEventListener('touchstart', showEffect, false);
            }

            element.addEventListener('mousedown', showEffect, false);
        };

        window.Waves = Waves;

        document.addEventListener('DOMContentLoaded', function() {
            Waves.displayEffect();
        }, false);

    })(window);

    $(document).ready(function(){
        var emailRegex = /^\w+([-+.'][^\s]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        var mobileRegex = /[0-9]{10}/;
        var otpRegex = /[0-9]{6}/;

        $('.email-input').on('keyup',function(){
            if (!$(this).val().match(emailRegex)) {
                $('.email-input').addClass('has-error');
            } else {
                $('.email-input').removeClass('has-error');
                $('.email-input').addClass('is-correct');
            }
        });

        $('.mobile-input').on('keyup',function(e){
            $(this).attr('maxlength', '10');
            $(this).attr('minlength', '10');
            if (!$(this).val().match(mobileRegex)) {
                $('.mobile-input').addClass('has-error');
                $('.input-error-tooltip').show();
            } else {
                $('.mobile-input').removeClass('has-error');
                $('.mobile-input').addClass('is-correct');
                $('.mobile-error').hide();
                $('.input-error-tooltip').show();
            }

            if($.inArray(e.keyCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 || (e.keyCode === 65 && (e.ctrlKey === true || e.metaKey === true)) || (e.keyCode >= 35 && e.keyCode <= 40)) {
                return;
            } else {
                $('.input-error-tooltip').hide();
            }
            if((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                e.preventDefault();
                $('.input-error-tooltip').show();
            }
        });
    });

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(find, 'g'), replace);
    }

    function elementExists(elementId){
        var element =  document.getElementById(elementId);
        if (typeof(element) != 'undefined' && element != null) {
            return true;
        } else {
            return false;
        }
    }

    $(document).ready(function(){
        <%  if("true".equals(params.loginFailed)) { %>
        document.getElementById('loginFailed').innerText='Login failed. Please try again!';
        if(siteId!="12") {
            //for the cases where we have to do force login and then logout
            $(window).on('load',function() {
                var simultaneosCookie = getCookie("SimulError");
                if("Fail"==simultaneosCookie) {
                    setCookie("SimulError","invalid");
                }
                $('#loginOpen').modal('show');
            });


        }
        <%  } else if("true".equals(params.showSignIn)) { %>
        $('#loginOpen').modal('show');
        <%  }  %>
    });

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

</script>


<script>
    var lastReadTopicId = 0;
</script>
<!-- Start of Async Drift Code -->




<script>
    function isMobile() {
        var isMobile = false; //initiate as false
        var userAgent = navigator.userAgent || navigator.vendor || window.opera;
        // device detection
        // alert(userAgent);
        // iOS detection from: http://stackoverflow.com/a/9039885/177710
        if (/iPad|iPhone|iPod/.test(userAgent)) {
            isMobile = true;
        }

        if (/android/i.test(userAgent)) {
            isMobile = true;
        }
        return isMobile;
    }

    function setCookie(cname,cvalue) {
        document.cookie = cname + "=" + cvalue + ";path=/";
    }

    function getCookie(cname) {
        var name = cname + "=";
        var decodedCookie = decodeURIComponent(document.cookie);
        var ca = decodedCookie.split(';');

        for(var i = 0; i < ca.length; i++) {
            var c = ca[i];

            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }

            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }

        return "";
    }

    function checkCookie() {
        if(isMobile() == true) {
            var loadCount = getCookie("noOftimesPageLoaded");
            if (loadCount != '' || loadCount > 0) {
                // this will execute if the cookies are already there
                // increament the count size
                loadCount++;
                // update the cookie with the new value
                setCookie("noOftimesPageLoaded", loadCount);
                if(loadCount == 1) {
                    //$('#openApps').modal('show');
                    //    document.getElementById("app-wrapper").style.display = "block";
                }
                else if ((loadCount) % 4 == 0) {
                    // alert("tenth");
                    // write the code to hide the div
                    // reset the cookie value

                    // document.getElementById("app-wrapper").style.display = "block";
                    //setCookie("noOftimesPageLoaded", '');

                    //$('#openApps').modal('show');

                } else {
                    $('#openApps').modal('hide');

                }
            } else {
                 //$('#openApps').modal('show');
                setCookie("noOftimesPageLoaded", 1);
            }
        }else{
            $('#openApps').modal('hide');
        }

    }
    <%if("1".equals(""+session["siteId"])&&params.tokenId==null){%>

    $(window).on('load',function(){
        <%if(("android".equals(session["appType"]))||("ios".equals(session["appType"]))){%>
        <%}else{%>
        checkCookie();
        <%}%>
    });
    <%}%>



    <sec:ifLoggedIn>
    function updateUserView(id,fromTab,viewedFrom){
        <g:remoteFunction controller="log" action="updateUserView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }
    </sec:ifLoggedIn>

    function updateView(id,fromTab,viewedFrom){

        <g:remoteFunction controller="log" action="updateView" params="'id='+id+'&source=web&fromTab='+fromTab+'&viewedFrom='+viewedFrom" />
    }

</script>
