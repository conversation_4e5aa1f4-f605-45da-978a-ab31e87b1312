<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader"></g:render>
<asset:stylesheet href="publisher.css"/>
<div class="container-fluid main-content" style="padding: 96px 0;">
  <div class="container publisher-container">
    <div class="row">
      <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="publisher-cover-photo">
          <img src="${assetPath(src: 'publisher/cover.png')}" class="img-responsive" alt="">
        </div>
        <div class="publisher-profile">
          <div class="publisher-profile-img">
            <img src="/funlearn/showProfileImage?id=${params.id}&fileName=${publisher.logo}&type=publishers&imgType=passport" class="img-responsive" alt="">
          </div>
          <div class="publisher-name-wrapper">
            <h1 class="publisher-name">${publisher.name}</h1>
            <p class="total-books"></p>
          </div>
        </div>
      </div>
      <div class="publisher-contact col-md-12 col-sm-12 col-xs-12">
        <ul class="col-md-6 col-xs-12 col-sm-12 contact-details">
          <li class="phone">${publisher.mobile}</li>
          <li class="email">${publisher.name}</li>
          <li class="website">${publisher.website}</li>
          <li class="address">${publisher.address1}<br>${publisher.address2}<br>${publisher.city}</li>
        </ul>
        <div class="col-md-6 col-xs-12 col-sm-12 publisher-desc">
          <p>${publisher.description}</p>
        </div>
      </div>
    </div>
  </div>
</div>


<g:render template="/${session['entryController']}/footer"></g:render>