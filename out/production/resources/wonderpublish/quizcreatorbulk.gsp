<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<asset:stylesheet href="wonderslate/adminCommonStyle.css" async="true"/>
<style>
.image-upload > input {
    display: none;
}

.cke_textarea_inline {
    height: 80px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.form-group .cke_textarea_inline {
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.bulkcreation .cke_textarea_inline {
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

</style>
<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<!--<div>-->
<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="quizCreatorBulk">
    <div class="container-fluid wplandingblueimage topnavblue">
        <div class="row">
            <div class="col-md-12 text-center">
                <h4 class="whitetext">Quiz Creation</h4>
            </div>
        </div>

    </div>
    <div class="container quizDirection" >
        <div  class='row quizb ' id="bookdtl" >
<div class="col-12">
            <form class="form-horizontal" enctype="multipart/form-data" role="form" name="addquiz" id="addquiz" action="/resourceCreator/addQuiz" method="post">
                <input type="hidden" name="resourceType" value="Multiple Choice Questions">
                <input type="hidden" name="chapterId" value="${chapterId}">
                <input type="hidden" name="mode" value="create">
                <input type="hidden" name="finished" value="true">
                <input type="hidden" name="bulkcreation" value="true">
                <input type="hidden" name="difficultylevel" value="Medium">
                <input type="hidden" name="bookId" value="${bookId}">
                <input type="hidden" name="page" value="${page}">
                <input type="hidden" name="resSubType" value="${params.resSubType}">
                <input type="hidden" name="currentAffairsType" value="${params.currentAffairsType}">
                <input type="hidden" name="noOfQuestions" value="${questionsArray.length}">

                <div class='col-md-12 main'>
                    <%for(int i=0;i < questionsArray.length;i++){%>
                    <div class="row">
                        <div class="col-md-12">
                            <label class="bluelabel" for="directions${i}">Directions </label>
                            <div class="cktext">
                                <textarea  rows="2" class="form-control" id="directions${i}" name="directions${i}">${directionsArray[i]}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <label class="bluelabel" for="question${i}">Question ${i+1}</label>
                            <div class="cktext">
                                <textarea  rows="2" class="form-control" id="question${i}" name="question${i}">${questionsArray[i]}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="light-grey-background">
                        <div class="row">
                            <div class="col-md-12">
                                <b>Answers options</b>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 bulkcreation">
                            <div class="cktext">
                                <textarea  rows="1" class="form-control whitebackground" id="option${i}_1" name="option${i}_1"> ${optionKeys[i][0]}</textarea>&nbsp;&nbsp;
                            </div>
                        </div>
                        <div class="col-md-1">
                            <input type="checkbox" name="answer${i}_1" id="answer${i}_1" value="Yes" <%= ("A".equals(correctAnswers[i])||"a".equals(correctAnswers[i])||"1".equals(correctAnswers[i]))?"checked":"" %>>
                        </div>
                        <div class="col-md-4 bulkcreation">
                            <div class="cktext">
                                <textarea  rows="1" class="form-control" id="option${i}_2" name="option${i}_2">${optionKeys[i][1]}</textarea>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <input type="checkbox" name="answer${i}_2" id="answer${i}_2" value="Yes" <%= ("B".equals(correctAnswers[i])||"b".equals(correctAnswers[i])||"2".equals(correctAnswers[i]))?"checked":"" %>>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 bulkcreation">
                            <div class="cktext">
                                <textarea  rows="1" class="form-control" id="option${i}_3" name="option${i}_3">${optionKeys[i][2]}</textarea>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <input type="checkbox" name="answer${i}_3" id="answer${i}_3" value="Yes" <%= ("C".equals(correctAnswers[i])||"c".equals(correctAnswers[i])||"3".equals(correctAnswers[i]))?"checked":"" %>>
                        </div>
                        <div class="col-md-4 bulkcreation">
                            <div class="cktext">
                                <textarea  rows="1" class="form-control" id="option${i}_4" name="option${i}_4">${optionKeys[i][3]}</textarea>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <input type="checkbox" name="answer${i}_4" id="answer${i}_4" value="Yes" <%= ("D".equals(correctAnswers[i])||"d".equals(correctAnswers[i])||"4".equals(correctAnswers[i]))?"checked":"" %>>
                        </div>
                    </div>
                    <%if(noOfOptions>4){%>
                    <div class="row">
                        <div class="col-md-4 bulkcreation">
                            <div class="cktext">
                                <textarea  rows="1" class="form-control" id="option${i}_5" name="option${i}_5">${optionKeys[i][4]}</textarea>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <input type="checkbox" name="answer${i}_5" id="answer${i}_5" value="Yes" <%= ("E".equals(correctAnswers[i])||"e".equals(correctAnswers[i])||"5".equals(correctAnswers[i]))?"checked":"" %>>
                        </div>

                    </div>
                    <%}%>
                    <div class="row">
                        <div class="col-md-4 smallText">Above directions apply&nbsp;<input type="checkbox" name="direction${i}"></div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <hr style="border-color: #f15a29">
                        </div>
                    </div><br><br>
                    <input type="hidden" name="answerDescription${i}" value="${answerExplanations[i]}">
                    <%}%>
                    <div class="row">
                        <div class="col-sm-9 col-sm-offset-1 resourceName">
                            <label class="bluelabel" for="passage">Passage (For passage based quiz)</label>
                            <div class="cktext">
                                <textarea  rows="2" class="form-control" id="passage" name="passage" placeholder="Enter the passage here"></textarea>
                            </div><br>

                        </div>
                    </div>
                    <div class="row quizb1">
                        <div class="col-sm-9 col-sm-offset-1">
                            <div class="form-group resourceName float-label-control">
                                <div class="cktext">
                                    <input type="text" class="form-control" id="resourceName" name="resourceName" placeholder="Name of your quiz" value="Multiple Choice Questions" maxlength="255">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row"><br>
                        <div class="col-md-12 col-md-offset-1 red smallText" style="display: none" id="bulkquizerror">  </div>
                    </div>
                    <div class="row"><br>
                        <div class="col-md-12 col-md-offset-1 text-center"> <button type="button" class="btn btn-primary light11" onclick="submitQuiz();" style="border-radius: 0px;border-width: 0px;"><i class='fa fa-file-text-o fa-x'></i>&nbsp;&nbsp;  SUBMIT </button>
                        </div><br><br>
                    </div>
                </div>
            </form>
        </div>
        </div>

    </div>
    <div class="push"></div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>

<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<script src="https://cdn.ckeditor.com/4.6.2/standard-all/ckeditor.js"></script>
<script>
$('.loading-icon').addClass('hidden');
    CKEDITOR.disableAutoInline = true;
    var no=0
    for(i=0;i<<%=questionsArray.length%>;i++) {
        //callCKEditor(i);
    }

    function callCKEditor(no) {
        window.setTimeout(
            function(){
                if($.trim($('#directions'+no).val())=="") $('#directions'+no).val('_');
                if($.trim($('#question'+no).val())=="") $('#question'+no).val('_');

                CKEDITOR.replace('question'+no,{
                    customConfig: '/assets/ckeditor/customConfig.js',
                    height: 200
                });

                for(j=1;j<=<%=noOfOptions>4?5:4%>;j++) {
                    if($.trim($('#option'+no+'_'+j).val())=="") $('#option'+no+'_'+j).val('_');

                    CKEDITOR.inline('option'+no+'_'+j,{
                        customConfig: '/assets/ckeditor/customConfig.js',
                        height: 200
                    });
                }
            },
            //checking for firefox
            (typeof InstallTrigger !== 'undefined')?no*10:0);
    }

    function submitQuiz(){

        $("#bulkquizerror").hide(500);
        var noOfQuestions = ${questionsArray.length};
        var errorStr="";
        var noErrors=true;
        var optionEmpty=false;
        var optMaxLenExc=false;
        var answerEmpty=true;
        var a,b;
        var noOfOptions=4;
        <%	if(noOfOptions>4) { %>
        noOfOptions=5;
        <%	} %>

        for(i=0;i<noOfQuestions;i++){
            a  = eval('document.addquiz.question'+i);
            //  b  = eval('CKEDITOR.instances.question'+i);
            //   a.value=b.getData();

            if(a.value==""){
                noErrors=false;
                errorStr+="Question "+(i+1)+" is empty.<br>";
            }

            optionEmpty=false;
            optMaxLenExc=false;
            answerEmpty=true;

            for(j=1;j<(noOfOptions+1);j++){
                a  = eval('document.addquiz.option'+i+'_'+j);
                //   b  = eval('CKEDITOR.instances.option'+i+'_'+j);
                //   a.value = b.getData();
                if(a.value=="") optionEmpty=true;
                //if(a.value.length>255) {optMaxLenExc=true;
                if(document.getElementById("answer"+i+"_"+j).checked) answerEmpty=false;
            }

            if(optionEmpty){
                noErrors=false;
                errorStr+="Question "+(i+1)+", please enter all the answer options.<br>";
            }

            if(optMaxLenExc){
                noErrors=false;
                errorStr+="Question "+(i+1)+", please check options length. One or more exceeded maximum limit of 255.<br>";
            }

            if(answerEmpty){
                noErrors=false;
                errorStr+="Question "+(i+1)+", please select atleast one right answer.<br>";
            }
        }

        if(document.getElementById("resourceName").value==""){
            noErrors=false;
            errorStr+="Please enter the name of the quiz.<br>";
        }

        if(!noErrors) {
            document.getElementById("bulkquizerror").innerHTML=errorStr;
            $("#bulkquizerror").show(500);
        } else {
            $('.loading-icon').removeClass('hidden');
            document.addquiz.submit();
        }
    }
    submitQuiz();
</script>
</body>
</html>
