<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader"></g:render>

<sec:ifNotLoggedIn>
  <script>
    loggedIn=false;
  </script>
</sec:ifNotLoggedIn>

<sec:ifLoggedIn>
  <script>
    loggedIn=true;
  </script>
</sec:ifLoggedIn>

<div class="container-fluid tabs-holder-library">
  <div class="container">
    <div class="row">
      <ul class='nav nav-tabs nav-tabs-wrapper' role='tablist'>
        <li role='presentation' class='nav-tabs-list-item active'>
          <a href='#libraryBooks' aria-controls='libraryBooks' class='nav-tabs-list-item-link' role='tab' data-toggle='tab' id="tabLibraryBooks">Books</a>
        </li>

        <li role='presentation' class='nav-tabs-list-item'>
          <a href='#libraryAssignment' aria-controls='libraryAssignment' class='nav-tabs-list-item-link' role='tab' data-toggle='tab' id="tabLibraryAssignment">Assignments</a>
        </li>
      </ul>
    </div>  
  </div>
</div>

<div class="tab-content">
  <div role="tabpanel" class="tab-pane fade in active" id="libraryBooks">
    <div class="container" style="min-height: calc(100vh - 162px);">
      <div class="row user-greeting">
        <div class="col-md-12 col-xs-12 col-sm-12 user-clm">
          <p class="greeting-user">Hey,
            <span class="greeting-user-name">${session['userdetails'].name.split(" ")[0]}!</span>
          </p>
          <p class="total-books" id="total-books-of-user"></p>
        </div>
      </div>
      <div class="row books-content-wrapper" id="content-data-books"></div>  
    </div>
  </div>

  <div role="tabpanel" class="tab-pane fade" id="libraryAssignment">
    <div id="instructor-review" class="additional-ref-section additional-assignment-section">
      <div class='additional-ref-wrapper additional-assignment'>
        <div class='additional-assignment-item'>
          <div class='additional-ref-img-wrapper additional-assignment-teacher-img-wrapper'> 
            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS1qB5kjPUmQnnC5UNkmRqaeceqNr9lqdkLbL4amM1uzW4d_EJS" class='additional-assignment-teacher-img' alt=''/>
          </div> 
          <div class='additional-ref-info additional-assignment-teacher-name-wrapper'>
            <p class='additional-assignment-teacher-name'>Instructor Name</p>
            <p class='additional-assignment-teacher-time'>3:31 PM</p>
          </div>
        </div>
        <div class="row review-attempt-wrapper">
          <div class="col-md-6 col-xs-12 text-center review-attempt-clm">
            <div class="col-md-6 col-xs-12 text-center" style="border-right: 1px solid rgba(68, 68, 68, 0.24);">
              <p class="assignment-not-done">24</p>
              <p class="done-student">Done</p>
            </div>
            <div class="col-md-6 col-xs-12 text-center">
              <p class="assignment-not-done">32</p>
              <p class="done-student">Not Done</p>
            </div>
          </div>
          <div class="col-md-6 col-xs-12 text-center review-attempt-clm">
            <button class="btn review-assignment-btn waves-effect waves-ripple" onclick="javascript:showReviewTable();">Review</button>
          </div>
        </div>
      </div>
    </div>
    <div id="instructor-review" class="additional-ref-section additional-assignment-section">
      <div class='additional-ref-wrapper additional-assignment'>
        <div class='additional-assignment-item'>
          <div class='additional-ref-img-wrapper additional-assignment-teacher-img-wrapper'>
            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS1qB5kjPUmQnnC5UNkmRqaeceqNr9lqdkLbL4amM1uzW4d_EJS" class='additional-assignment-teacher-img' alt=''/>
          </div>
          <div class='additional-ref-info additional-assignment-teacher-name-wrapper'>
            <p class='additional-assignment-teacher-name'>Instructor Name</p>
            <p class='additional-assignment-teacher-time'>3:31 PM</p>
          </div>
        </div>
        <div class="row review-attempt-wrapper">
          <div class="col-md-6 col-xs-12 text-center review-attempt-clm">
            <div class="col-md-6 col-xs-12 text-center" style="border-right: 1px solid rgba(68, 68, 68, 0.24);">
              <p class="assignment-not-done">24</p>
              <p class="done-student">Done</p>
            </div>
            <div class="col-md-6 col-xs-12 text-center">
              <p class="assignment-not-done">32</p>
              <p class="done-student">Not Done</p>
            </div>
          </div>
          <div class="col-md-6 col-xs-12 text-center review-attempt-clm">
            <button class="btn review-assignment-btn waves-effect waves-ripple" onclick="javascript:showReviewTable();">Review</button>
          </div>
        </div>
      </div>
    </div>

    <div id="student-review-table" class="container-fluid" style="display: none;">
      <div class="row">
        <div class="col-xs-12">
          <table class="table table-responsive publisher-books-table detailed-table">
            <tbody>
              <tr>
                <th class="table-chapters">Student</th>
                <th class="right-aligned-data">Time Takes</th>
                <th class="right-aligned-data">Total Questions</th>
                <th class="right-aligned-data">Skipped</th>
                <th class="right-aligned-data correct">Correct</th>
                <th class="right-aligned-data incorrect">Incorrect</th>
              </tr>
              <tr>
                <td class="table-chapters-data">Rajesh</td>
                <td class="right-aligned-data">0.31</td>
                <td class="right-aligned-data">302</td>
                <td class="right-aligned-data">100</td>
                <td class="right-aligned-data correct">100</td>
                <td class="right-aligned-data incorrect">100</td>
              </tr>
              <tr>
                <td class="table-chapters-data">Rajesh</td>
                <td class="right-aligned-data">0.31</td>
                <td class="right-aligned-data">302</td>
                <td class="right-aligned-data">100</td>
                <td class="right-aligned-data correct">100</td>
                <td class="right-aligned-data incorrect">100</td>
              </tr>
              <tr>
                <td class="table-chapters-data">Rajesh</td>
                <td class="right-aligned-data">0.31</td>
                <td class="right-aligned-data">302</td>
                <td class="right-aligned-data">100</td>
                <td class="right-aligned-data correct">100</td>
                <td class="right-aligned-data incorrect">100</td>
              </tr>
              <tr>
                <td class="table-chapters-data">Rajesh</td>
                <td class="right-aligned-data">0.31</td>
                <td class="right-aligned-data">302</td>
                <td class="right-aligned-data">100</td>
                <td class="right-aligned-data correct">100</td>
                <td class="right-aligned-data incorrect">100</td>
              </tr>
              <tr>
                <td class="table-chapters-data">Rajesh</td>
                <td class="right-aligned-data">0.31</td>
                <td class="right-aligned-data">302</td>
                <td class="right-aligned-data">100</td>
                <td class="right-aligned-data correct">100</td>
                <td class="right-aligned-data incorrect">100</td>
              </tr>
              <tr>
                <td class="table-chapters-data">Rajesh</td>
                <td class="right-aligned-data">0.31</td>
                <td class="right-aligned-data">302</td>
                <td class="right-aligned-data">100</td>
                <td class="right-aligned-data correct">100</td>
                <td class="right-aligned-data incorrect">100</td>
              </tr>              
            </tbody>
          </table>  
        </div>
      </div>
    </div>
  </div>
</div>

<g:render template="/${session['entryController']}/footer"></g:render>

<script>
  function showReviewTable() {
    $('#instructor-review').hide();
    $('#student-review-table').fadeIn(1000);
  }
</script>