
<div class="row justify-content-center">
    <div class="col-md-10 col-sm-10 col-xs-12 col-sm-offset-1 col-md-offset-1"><br>
        <form class="form-horizontal" enctype="multipart/form-data" role="form" name="addhtml" id="addhtml" action="/resourceCreator/addHTML" method="post">
            <input type="hidden" name="resourceType">
            <input type="hidden" name="bookId">
            <input type="hidden" name="chapterId">
            <input type="hidden" name="mode">
            <input type="hidden" name="resourceDtlId">
            <input type="hidden" name="htmlId">
            <input type="hidden" name="page" value="notes">
            <input type="hidden" name="sharing" value="createdbyuser">
            <input type="hidden" name="quizMode" value="${params.quizMode}">
            <input type="hidden" name="batchIds">

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group resourceName float-label-control">
                        <div class="cktext">
                            <input type="text" class="form-control" id="resourceNameUserAdd" name="resourceName" placeholder="Name of your notes"  maxlength="255" value="">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="form-group notes float-label-control">
                        <div class="cktext">
                            <textarea  rows="30" class="form-control" id="notes" name="notes" placeholder="Enter your notes here"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="alert alert-warning col-sm-12" id="alertbox" style="display: none">
                    Please complete required fields marked in red.
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="form-group">
                    <div class=" col-sm-12 notesBtn">
                        <button type="button" onclick="javascript:formCancelNotes()" class="btn btn-primary">Cancel</button>
                        <button type="button" onclick="javascript:formAddNotes()" class="btn btn-primary">Add</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
