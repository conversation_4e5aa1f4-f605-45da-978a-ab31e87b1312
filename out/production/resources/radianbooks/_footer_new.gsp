<g:render template="/wonderpublish/commonfooter_new"></g:render>
<script>
  var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
  var userLoggedIn=false;
</script>
<sec:ifLoggedIn>
  <script>
    userLoggedIn=true;
  </script>
</sec:ifLoggedIn>
<style>
  .copy-right-text-footer span{
    color: grey !important;
  }
</style>
<% if(params.tokenId==null && !isBookPage){%>
<footer class="footer">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-4 responsive-none-view">
        <div class="image-wrapper-footer-logo">
          <img src="${assetPath(src: 'radianbooks/radian-footer-Logo.png')}" alt="" title="" />
        </div>
        <div class="text-center-align-here">
          <ul class="there-social-footer-link-wrp">
            <li><a href="https://www.facebook.com/radianbooks" target="_blank"><i class="fa fa-facebook"></i></a></li>
            <li><a href="https://twitter.com/radianbooks" target="_blank"><i class="fa fa-twitter"></i></a></li>
            <li><a href="https://www.instagram.com/radianbooks" target="_blank"><i class="fa fa-instagram"></i></a></li>
            <li><a href="https://www.youtube.com/channel/UCkgWnC7DOKoRVQtbqbPmpWA" target="_blank"><i class="fa fa-youtube-play"></i></a></li>
            <div class="clearfix"></div>
          </ul>
        </div>
      </div>

      <div class="col-md-8 responsive-view-none">
        <div class="row">


          <div class="col-lg-4 col-md-6 main-div-box-link-footer">
            <h3 class="footer-link-title">Categories</h3>
            <ul class="link-ul-footer" id="footerCategoryLinks">

            </ul>
          </div>

        </div>
      </div>


    </div>
  </div>

  <div class="row footer-copyright mt-2 mt-md-4 mx-0">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <p class="copy-right-text-footer"></p>
        </div>
      </div>
    </div>
  </div>
</footer>

<div class="mobile-footer-nav d-md-none" id="mobile-footer-nav"></div>
<%}%>
<script>
  //Dynamic Year in Footer
  var strDate = new Date();
  var shortYear = strDate.getFullYear();
  var nextYear = (new Date().getFullYear()+1);
  var twoDigitYear = nextYear.toString().substr(-2);
  $('.copy-right-text-footer').html('&copy;' + shortYear +'-'+twoDigitYear+"<span> Powered by Wonderslate</span> ");

  var footerCategoryLinks="";
  for (var i = 0; i < activeCategories.length; i++) {
    footerCategoryLinks +="<li><a href='/radianbooks/store?level="+replaceAll(activeCategories[i].level.replace('&', '~'),' ','-')+"'>"+activeCategories[i].level+"</a></li>";
  }
  <% if(params.tokenId==null && !isBookPage){%>
  document.getElementById("footerCategoryLinks").innerHTML=footerCategoryLinks;
  <%}%>

  var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));

  var displayLevel = "";
  var categoryChanged = true;
  var categorySyllabusHtml="";
  for (var i = 0; i < activeCategoriesSyllabus.length; i++) {
    if(activeCategoriesSyllabus[i].level!=displayLevel){
      categoryChanged = true;
      displayLevel = activeCategoriesSyllabus[i].level;
      //closing tag logic
      if(displayLevel!=""){
        categorySyllabusHtml +=" </ul>\n" +
                "  </div>\n" +
                "  </div>";
      }
      categorySyllabusHtml +="<div class=\"col-sm-4\">\n" +
              "          <div class=\"manage-row-fluides-menu-big\">\n" +
              "          <ul class=\"manage-with-all-links-big-menus\">\n" +
              "          <h4>"+activeCategoriesSyllabus[i].level+"</h4>";
    }
    if("Medical Entrances"==activeCategoriesSyllabus[i].level&&"NEET"==activeCategoriesSyllabus[i].syllabus) continue;
    categorySyllabusHtml +="<li><a href='/radianbooks/store?level="+ replaceAll(activeCategoriesSyllabus[i].level.replace('&', '~'),' ','-')+"&syllabus="+replaceAll(activeCategoriesSyllabus[i].syllabus.replace('&', '~'),' ','-')+"&grade=null'>"+activeCategoriesSyllabus[i].syllabus+"</a></li>";

  }
  //last tag close
  categorySyllabusHtml +=" </ul>\n" +
          "  </div>\n" +
          "  </div>";

</script>
<script>



  var mobileFooterNav =
          '        <div class="d-flex row justify-content-around w-100">\n' +
          '            <a href="/radianbooks/store" class="ebooks-menu d-flex align-items-center col">\n' +
          '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-library.svg')}">\n' +
          '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-ebooks-filled.svg')}">\n' +
          '                <p>eBooks Store</p>\n' +
          '            </a>\n' +
          '<sec:ifNotLoggedIn>\n' +
          '            <a href="javascript:loginOpen()" class="home-menu d-flex align-items-center col">\n' +
          '                <img class="mb-1 inactive" src="${assetPath(src: 'arihant/icon-arihant-library.svg')}">\n' +
          '                <img class="mb-1 active d-none" src="${assetPath(src: 'arihant/icon-arihant-library-filled.svg')}">\n' +
          '                <p>My Books</p>\n' +
          '            </a>\n' +
          '</sec:ifNotLoggedIn>'+
          '<sec:ifLoggedIn>'+
          '            <a href="/wsLibrary/myLibrary" class="home-menu d-flex align-items-center col">\n' +
          '                <img class="mb-1 inactive" src="${assetPath(src: 'arihant/icon-arihant-library.svg')}">\n' +
          '                <img class="mb-1 active d-none" src="${assetPath(src: 'arihant/icon-arihant-library-filled.svg')}">\n' +
          '                <p>My Books</p>\n' +
          '            </a>\n' +
          '</sec:ifLoggedIn>'+
          '        </div>';

  $(document).ready(function(){
    document.getElementById('mobile-footer-nav') ? document.getElementById('mobile-footer-nav').innerHTML = mobileFooterNav : null;

    var url = window.location.href;
    if(url.indexOf("/funlearn/quiz") != -1){
      //$('.mobile-footer-nav').addClass('hide-menus');
    } else if(url.indexOf("/wsLibrary/myLibrary") != -1){
      $('.mobile-footer-nav .home-menu').addClass('active-menu');
      $('.mobile-footer-nav .home-menu .active').removeClass('d-none');
      $('.mobile-footer-nav .home-menu .inactive').addClass('d-none');
      $('.mobile-footer-nav .ebooks-menu').addClass('common-footer-nav');
    } else if(url.indexOf("/radianbooks/store") != -1){
      $('.mobile-footer-nav .ebooks-menu').addClass('active-menu');
      $('.mobile-footer-nav .ebooks-menu .active').removeClass('d-none');
      $('.mobile-footer-nav .ebooks-menu .inactive').addClass('d-none');
      $('.mobile-footer-nav .home-menu').addClass('common-footer-nav');
    } else {
      $('.mobile-footer-nav a').addClass('common-footer-nav');
    }

  });

</script>
<asset:javascript src="landingpage/jquery.shorten.js" />
<asset:javascript src="whitelabel/popper.min.js"/>
<asset:javascript src="whitelabel/bootstrap.min.js"/>
<asset:javascript src="whitelabel/jquery-ui.min.js"/>
<asset:javascript src="arihant/waypoints.min.js"/>
<asset:javascript src="arihant/jquery.counterup.min.js"/>
<asset:javascript src="arihant/custom.js"/>
<asset:javascript src="landingpage/slick.js"/>
<asset:javascript src="wonderslate/material.min.js"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<g:render template="/books/pomodoro"></g:render>
<script>
  window.addEventListener('keydown', function(event) {
    if (event.keyCode === 80 && (event.ctrlKey || event.metaKey) && !event.altKey && (!event.shiftKey || window.chrome || window.opera)) {
      event.preventDefault();
      if (event.stopImmediatePropagation) {
        event.stopImmediatePropagation();
      } else {
        event.stopPropagation();
      }
      return;
    }
  }, true);

</script>

<g:render template="/wsshop/cartScripts"></g:render>
<g:render template="/wsshop/searchScripts"></g:render>