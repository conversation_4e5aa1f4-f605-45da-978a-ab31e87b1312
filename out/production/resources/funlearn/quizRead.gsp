<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="katex.min.css"/>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>

<asset:javascript src="katex.min.js"/>
<asset:javascript src="auto-render.min.js"/>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<style>
.questionBlock {
    display:flex;
}
.questionBlock span {
    margin-right:2%;
    font-weight: bold;
}
ul {
    padding:0;
    margin-top:2%;
}
li {
    list-style:none;
    padding:3px;
}
li span {
    margin-right:2%;
}
div#answer-list ul {
    width:100%;
    max-width:100%;
}
div#answer-list ul li{
    display:inline-block;
    width:auto;
    text-align:center;
    border:1px solid ;
    width:69px;

}
div#answer-list
{
    display:block;
    width:100%;
}
div#answer-list h6
{
    display:inline;
    margin:0 !important;
}
div#answer-list span:after,.questionBlock span:after {
    content:".";
}
.container h4{
    padding:10px;
}
div#description li{
    display:flex;
}
div#description li h6
{
    margin:0px !important;
    display:flex;
    align-items:center;
    justify-content:center;
    margin-right:5px !important;

}
div#description li > span:nth-child(1):after
{
    content:"."
}

</style>
<div class="container">
    <div class="row" >
        <div class="col-12 mt-2" >
            <h4>Read Quiz</h4>
            <div class="inner-ques" id="getdata"></div>
        </div>
        <div class="col-12 mt-4 answers-liat" >
            <h4>Quiz answers</h4>
            <div class="answer-block" id="answer-list"></div>
        </div>
        <div class="col-12 answer-description-block">
            <h4>Answer with explanations</h4>
            <div class="description" id="description"></div>
        </div>

    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>


<script>


    function getQuestionAnswers(){
        $('.loading-icon').removeClass('hidden');
        $('.que-side-menu .tab').hide();
        <g:remoteFunction controller="funlearn" action="newQuizQA" params="'quizId='+${params.quizId}+'&resId='+${params.resId}" onSuccess = "initializeQuizDataWithAnswers(data);"/>
    }
    function initializeQuizDataWithAnswers(data) {
        var htmlStr="";
        var htmlAnswer= "";
        var htmlDescription = "";
        var checkInput=''
        var cont=JSON.parse(data.results)
        console.log(cont)
        for(var i=0 ; i<cont.length; i++){
            checkInput =cont[i].op5?("<li><span>(e)</span>"+(cont[i].op5).replace(/<\/?[^>]+(>|$)/g, ""))+"</li>":""

            htmlStr+="<div class='questionBlock'>"+"<span>"+(i+1)+"</span>"+"<h5>"+(cont[i].ps).replace(/<\/?[^>]+(>|$)/g, "")+"</h5>"+"</div>"+"<ul>"+"<li><span>(a)</span>"+(cont[i].op1).replace(/<\/?[^>]+(>|$)/g, "")+"</li>"+"<li><span>(b)</span>"+(cont[i].op2).replace(/<\/?[^>]+(>|$)/g, "")+"</li>"+"<li><span>(c)</span>"+(cont[i].op3).replace(/<\/?[^>]+(>|$)/g, "")+"</li>"+"<li><span>(d)</span>"+(cont[i].op4).replace(/<\/?[^>]+(>|$)/g, "")+"</li>"+checkInput+"</ul>";


            var keys = Object.keys(cont[i]).filter(k=>cont[i][k]==="Yes");
            if(keys == 'ans1'){
                htmlAnswer += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(a)</h6>"+"</li>";
                if(cont[i].answerDescription)
                {
                    htmlDescription += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(a)</h6>"+"<span>"+cont[i].answerDescription+"</span>"+"</li>"
                }
            }
            else if(keys == 'ans2'){
                htmlAnswer += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(b)</h6>"+"</li>";
                if(cont[i].answerDescription)
                {
                    htmlDescription += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(b)</h6>"+"<span>"+cont[i].answerDescription+"</span>"+"</li>"
                }
            }
            else if(keys == 'ans3'){
                htmlAnswer += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(c)</h6>"+"</li>";
                if(cont[i].answerDescription)
                {
                    htmlDescription += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(c)</h6>"+"<span>"+cont[i].answerDescription+"</span>"+"</li>"
                }
            }
            else if(keys == 'ans4'){
                htmlAnswer += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(d)</h6>"+"</li>";
                if(cont[i].answerDescription)
                {
                    htmlDescription += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(d)</h6>"+"<span>"+cont[i].answerDescription+"</span>"+"</li>"
                }
            }
            else if(keys == 'ans5'){
                htmlAnswer += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(e)</h6>"+"</li>";
                if(cont[i].answerDescription)
                {
                    htmlDescription += "<li>"+"<span>"+(i+1)+"</span>"+"<h6>(e)</h6>"+"<span>"+cont[i].answerDescription+"</span>"+"</li>"
                }
            }



        }

        var htmlinside = document.getElementById("getdata");
        var z = document.createElement('div'); // is a node
        z.innerHTML = htmlStr;
        htmlinside.appendChild(z);
        var htmlinsideanswer = document.getElementById("answer-list");
        var ans = document.createElement('ul');
        ans.innerHTML = htmlAnswer;
        htmlinsideanswer.appendChild(ans);
        var discription =document.getElementById("description");
        var dis = document.createElement('ul');
        dis.innerHTML = htmlDescription;
        discription.appendChild(dis);
        optionVAlidation();
        renderMathInElement(document.body);
    }
    getQuestionAnswers();

    function optionVAlidation(){
        var listItem = document.querySelectorAll("#getdata li");
        for(var i=0 ; i<listItem.length ; i++){
            if(listItem[i] == "" || listItem[i] == "undefined" || listItem[i] == null ){
                listItem[i].style.display = "none";
            }
        }
    }

</script>
