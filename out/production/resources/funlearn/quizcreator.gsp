<g:render template="navheader"></g:render>

<asset:stylesheet href="material-input.css"/>
<asset:stylesheet href="fileuploadwithpreview.css"/>

<style>
.image-upload > input
{
    display: none;
}

.cke_textarea_inline
{
    height: 80px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.form-group .cke_textarea_inline
{
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}


</style>
<div>
    <div class="container">
        <div class="row">


        <div id="static-content" class="col-md-9 main maincontent">
            <div class="row">
                <div class="col-md-12">
                    <h4>Multiple Choice Questions</h4><br>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <form class="form-horizontal" enctype="multipart/form-data" role="form" name="addquiz" id="addquiz" action="/resourceCreator/addQuiz" method="post">
                        <input type="hidden" name="resourceType">
                        <input type="hidden" name="topicId">
                        <input type="hidden" name="quizId">
                        <input type="hidden" name="mode">
                        <input type="hidden" name="resourceDtlId">
                        <input type="hidden" name="objectiveMstId">
                        <input type="hidden" name="finished">
                        <input type="hidden" name="fileoption1name" id="fileoption1name">
                        <input type="hidden" name="fileoption2name" id="fileoption2name">
                        <input type="hidden" name="fileoption3name" id="fileoption3name">
                        <input type="hidden" name="fileoption4name" id="fileoption4name">
                        <input type="hidden" name="fileoption5name" id="fileoption5name">
                        <div class="row">
                            <div class="col-sm-9 col-sm-offset-2">
                                <div class="form-group resourceName float-label-control">
                                    <div class="cktext">
                                 <input type="text" class="form-control" id="resourceName" name="resourceName" placeholder="Name of your quiz" value="" maxlength="255">
                                        </div>
                            </div>
                        </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-2">
                                <div class="image-upload text-center">
                                    <br><br><div id="filelabel"><label for="file"><span class="fa fa-image fa-x" style="cursor: pointer"></span></label></div>
                                    <input id="file" name="file" type="file"  accept="image/png, image/jpeg, image/gif"/><br> Upload Image
                                </div>
                            </div>
                         <div class="col-sm-9">
                                 <label class="bluelabel" for="question">Question</label>
                             <div class="cktext">
                                 <textarea  rows="2" class="form-control" id="question" name="question" placeholder="Type your question here"></textarea>
                             </div>
                         </div>

                        </div>

                        <div class="col-sm-12">&nbsp;<br><br></div>

                        <div class="row">
                            <div class="col-sm-2">
                                <div class="image-upload text-center">
                                    <br><br><div id="filelabel1"><label for="fileoption1"><span class="fa fa-image fa-x" style="cursor: pointer"></span></label></div>
                                    <input id="fileoption1" name="fileoption1" type="file"  accept="image/png, image/jpeg, image/gif" onchange="changeLabel(this,1);"/>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-group">
                                <label class="bluelabel smallerText" for="option1">Option 1</label>
                                    <div class="cktext">
                                    <textarea  rows="2" class="form-control" id="option1" name="option1" placeholder="Option 1"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-1 text-center">
                               <br><br> <input type="checkbox" name="answer1" id="answer1" value="Yes">
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-sm-2">
                                <div class="image-upload text-center">
                                    <br><br> <div id="filelabel2"> <label for="fileoption2"><span class="fa fa-image fa-x" style="cursor: pointer"></span></label></div>
                                    <input id="fileoption2" name="fileoption2" type="file" accept="image/png, image/jpeg, image/gif" onchange="changeLabel(this,2);"/>
                                </div>
                            </div>
                        <div class="col-sm-9">
                            <div class="form-group">
                                <label class="bluelabel smallerText" for="option2">Option 2</label>
                                <div class="cktext">
                                <textarea  rows="2" class="form-control" id="option2" name="option2" placeholder="Option 2"></textarea>
                                </div>
                            </div>
                        </div>
                            <div class="col-sm-1 text-center">
                                <br><br><input type="checkbox" name="answer2" id="answer2" value="Yes">
                            </div>

                            </div>

                        <div class="row">
                            <div class="col-sm-2">
                                <div class="image-upload text-center">
                                    <br><br> <div id="filelabel3"><label for="fileoption3"><span class="fa fa-image fa-x" style="cursor: pointer"></span></label></div>
                                    <input id="fileoption3" name="fileoption3" type="file" accept="image/png, image/jpeg, image/gif" onchange="changeLabel(this,3);"/>
                                </div>
                            </div>
                        <div class="col-sm-9">
                            <div class="form-group">
                                <label class="bluelabel smallerText" for="option3">Option 3</label>
                                <div class="cktext">
                                <textarea  rows="2" class="form-control" id="option3" name="option3" placeholder="Option 3"></textarea>
                                </div>
                            </div>
                        </div>
                            <div class="col-sm-1 text-center">
                                <br><br><input type="checkbox" name="answer3" id="answer3" value="Yes">
                            </div>

                            </div>

                        <div class="row">
                        <div class="col-sm-2">
                            <div class="image-upload text-center">
                                <br><br><div id="filelabel4"><label for="fileoption4"><span class="fa fa-image fa-x" style="cursor: pointer"></span></label></div>
                                <input id="fileoption4" name="fileoption4" type="file" accept="image/png, image/jpeg, image/gif" onchange="changeLabel(this,4);"/>
                            </div>
                        </div>
                        <div class="col-sm-9">
                            <div class="form-group">
                                <label class="bluelabel smallerText" for="option4">Option 4</label>
                                <div class="cktext">
                                    <textarea  rows="2" class="form-control" id="option4" name="option4" placeholder="Option 4"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-1 text-center">
                            <br><br><input type="checkbox" name="answer4" id="answer4" value="Yes">
                        </div>

                    </div>
                        <div class="row">
                            <div class="col-sm-2">
                                <div class="image-upload text-center">
                                    <br><br><div id="filelabel5"><label for="fileoption5"><span class="fa fa-image fa-x" style="cursor: pointer"></span></label></div>
                                    <input id="fileoption5" name="fileoption5" type="file" accept="image/png, image/jpeg, image/gif" onchange="changeLabel(this,5);"/>
                                </div>
                            </div>
                            <div class="col-sm-9">
                                <div class="form-group">
                                    <label class="bluelabel smallerText" for="option5">Option 5</label>
                                    <div class="cktext">
                                        <textarea  rows="2" class="form-control" id="option5" name="option5" placeholder="Option 5"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-1 text-center">
                                <br><br><input type="checkbox" name="answer5" id="answer5" value="Yes">
                            </div>

                        </div>
                        <div class="row">
                            <div class="col-sm-9  col-sm-offset-2">
                                <div class="form-group float-label-control">
                                <label for="hint">Hint</label>
                                <input type="text" class="form-control" id="hint" name="hint" placeholder="Hint" value="" maxlength="255">
                            </div>
                            </div>
                            </div>

                        <div class="row">
                            <div class="col-sm-9  col-sm-offset-2">
                                <div class="form-group float-label-control">
                                <label for="answerDescription">Answer Explanation</label>
                                <textarea  rows="4" class="form-control" id="answerDescription" name="answerDescription" placeholder="Answer Explanation"></textarea>
                            </div>
                        </div>
                            </div>
                        <div class="row">
                            <div class="col-sm-4  col-sm-offset-2">
                                <div class="form-group smallerText greyText">
                                    <label for="difficultylevel">Difficulty Level </label>&nbsp;&nbsp;
                                   <select id="difficultylevel" name="difficultylevel">
                                       <option></option>
                                       <option value="Easy">Easy</option>
                                       <option value="Medium">Medium</option>
                                       <option value="Tough">Tough</option>
                                   </select>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group smallerText greyText">
                                    <label for="subtopicid">Topic </label>&nbsp;&nbsp;
                                    <select id="subtopicid" name="subtopicid">
                                        <option></option>
                                    <% topics.each{ topic ->%>
                                    <option value="${topic.topicId}">${topic.topicName}</option>
                                    <%}%>

                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">

                        <div class="alert alert-warning col-sm-12" id="alertbox" style="display: none">
                            Please complete required fields marked in red.
                        </div>
                            </div>
                        <div class="row">
                        <div class="form-group">
                            <div class=" col-sm-12 text-center">
                                <button type="button" onclick="javascript:formSubmit('Next')" class="btn btn-primary">Save and Add Next</button>
                                <button type="button" onclick="javascript:formSubmit('Done')" class="btn btn-primary">Save and Finish</button>
                            </div>
                        </div>
                        </div>
                    </form>
                </div>
            </div>

        </div>

            <div id="sidebar" class="col-md-2 hidden-xs hidden-sm text-left">

            </div>

        </div>
    </div>


    <g:render template="footer"></g:render>
</div>
<asset:javascript src="jquery-1.11.2.min.js"/>

<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="material-input.js"/>
<asset:javascript src="fileuploadwithpreview.js"/>
<asset:javascript src="quizcreator.js"/>
<asset:javascript src="ckeditor.js"/>

<script>
    var serverPath = "${request.contextPath}";
    var mode = "${params.mode}";
    var resourceType = "${params.resourceType}";
    var topicId = "${params.topicId}";
	
	
    CKEDITOR.disableAutoInline = true;
    CKEDITOR.inline( 'question');
    CKEDITOR.inline( 'option1');
    CKEDITOR.inline( 'option2');
    CKEDITOR.inline( 'option3');
    CKEDITOR.inline( 'option4');
    CKEDITOR.inline( 'option5');


    <% if("edit".equals(params.mode)){
    objectives.each{ objective ->%>

    var quizItem={};
    quizItem.resourceName="${resourceDtl.resourceName}";
    quizItem.question =htmlDecode( "${objective.question.replaceAll("(\\r|\\n)", "")}");
    quizItem.answer1 = "${objective.answer1}";
    quizItem.answer2 = "${objective.answer2}";
    quizItem.answer3 = "${objective.answer3}";
    quizItem.answer4 = "${objective.answer4}";
    quizItem.option1 = htmlDecode("${objective.option1.replaceAll("(\\r|\\n)", "")}");
    quizItem.option2 = htmlDecode("${objective.option2.replaceAll("(\\r|\\n)", "")}");
    quizItem.option3 = htmlDecode("${objective.option3.replaceAll("(\\r|\\n)", "")}");
    quizItem.option4 = htmlDecode("${objective.option4.replaceAll("(\\r|\\n)", "")}");
    quizItem.option5 = htmlDecode("${objective.option5!=null?objective.option5.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.fileoption1name = "${objective.fileoption1}";
    quizItem.fileoption2name = "${objective.fileoption2}";
    quizItem.fileoption3name = "${objective.fileoption3}";
    quizItem.fileoption4name = "${objective.fileoption4}";
    quizItem.fileoption5name = "${objective.fileoption5}";
    quizItem.hint = "${objective.hint}";
    quizItem.answerDescription = "${objective.answerDescription}";
    quizItem.objectiveMstId = "${objective.id}";
    quizItem.questionFilename = "${objective.questionFilename}";
    quizItem.difficultylevel = "${objective.difficultylevel}";
    quizItem.subtopicid = "${objective.topicId}";
     quizItems.push(quizItem);


   <% }
  %>
    resourceDtlId = "${resourceDtl.id}";
    quizId = "${resourceDtl.resLink}";
    quizName = quizItems[0].resourceName;
    gotoQuestion(0);
    document.getElementById("sidebar").innerHTML = "<br><b> Saved Questions </b> <br>";
    for(i=0;i<quizItems.length;i++){
        var quest = quizItems[i].question
        if(quest.indexOf("<p")==0) quest = quest.substring(3,(quest.length-4));
        document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + i + ")' class='questions'>" + (i + 1) + ". " + quest + "</a><br><br>";

    }

    <%}%>

 function changeLabel(field,number){
     var files = field.files;
     if (!files.length) {
       // default
         console.log("no files");
         return;
     }

     for (var i = 0, l = files.length; i < l; i++) {
      //   $('label[for='+field.name+']').text(files[i].name + '\n');
         var t = eval('document.addquiz.fileoption'+number+'name');
         t.value=files[i].name;
         document.getElementById("filelabel"+number).innerHTML = "<label for='fileoption"+number+"'><span class='smallerText' style='cursor: pointer'>"+files[i].name+"</span></label>";

     }
 }

    function formSubmit(submitType) {
        if (validate()) {
            document.addquiz.mode.value=mode;
            document.addquiz.resourceType.value=resourceType;
            document.addquiz.topicId.value=topicId;
            document.addquiz.quizId.value=quizId;
            document.addquiz.resourceDtlId.value=resourceDtlId;
            document.addquiz.objectiveMstId.value=objectiveMstId;
            document.addquiz.resourceName.value=quizName;

            if('Next' == submitType) {

                var oData = new FormData(document.forms.namedItem("addquiz"));

                var url = "${createLink(controller:'funlearn',action:'addQuiz')}";
                console.log("the url created is "+url);
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: oData,
                    processData: false,  // tell jQuery not to process the data
                    contentType: false,
                    success: function (req) {
                        questionAdded(req);
                    }
                });
                document.getElementById("addquiz").reset();
           //     if(tinyMCE.get('question')!=null) tinyMCE.get('question').setContent('');
                CKEDITOR.instances.question.setData('');
                CKEDITOR.instances.option1.setData('');
                CKEDITOR.instances.option2.setData('');
                CKEDITOR.instances.option3.setData('');
                CKEDITOR.instances.option4.setData('');
                CKEDITOR.instances.option5.setData('');
                document.addquiz.fileoption1name.value='';
                document.addquiz.fileoption2name.value='';
                document.addquiz.fileoption3name.value='';
                document.addquiz.fileoption4name.value='';
                document.addquiz.fileoption5name.value='';
                document.addquiz.difficultylevel.selectedIndex=0;
                document.addquiz.subtopicid.selectedIndex=0;
                document.getElementsByName('question')[0].placeholder = 'Question ' + (quizItems.length + 1);
                document.getElementById("filelabel1").innerHTML = "<label for='fileoption1'><span class='fa fa-image' style='cursor: pointer'></span></label>";
                document.getElementById("filelabel2").innerHTML = "<label for='fileoption2'><span class='fa fa-image' style='cursor: pointer'></span></label>";
                document.getElementById("filelabel3").innerHTML = "<label for='fileoption3'><span class='fa fa-image' style='cursor: pointer'></span></label>";
                document.getElementById("filelabel4").innerHTML = "<label for='fileoption4'><span class='fa fa-image' style='cursor: pointer'></span></label>";
                document.getElementById("filelabel5").innerHTML = "<label for='fileoption5'><span class='fa fa-image' style='cursor: pointer'></span></label>";
                clearContent();
                $("html, body").animate({ scrollTop: 0 }, "slow");

            }else{
                document.addquiz.finished.value='true';
                document.addquiz.submit();
            }

        }

    }
    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };




</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>
</body>
</html>
