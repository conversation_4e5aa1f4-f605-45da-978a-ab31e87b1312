<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <div class="container">
                        <h2 class="text-center">Question Papers Home</h2>
                        <div class="row mx-0 px-3 col-12 col-md-10 mx-auto fadein-animated d-flex justify-content-center">
                            <a href="/questionPaper/createQuestionPaperPage" class="btn btn-info fadein-animated mr-2">Create Question Paper</a>
                            &nbsp;<a href="/questionPaper/listPatterns" class="btn btn-primary fadein-animated mr-2">Paper Patterns</a>
                        </div><br>

                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th>Name</th>
                                <th>Created By</th>
                                <th>Created On</th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <% String groupId = null;
                             boolean show = true
                            %>
                            <g:each in="${questionPaperSets}" var="questionPaperSet">
                                <% int counter=1;
                                // if groupId is null, set the groupId to the current questionPaperSet's groupId.
                                    if(groupId == null) {
                                        groupId = questionPaperSet.groupId;
                                        show = true
                                    }else {
                                        // if the groupId is not null, check if the current questionPaperSet's groupId is different from the groupId.
                                        // if it is different, set the groupId to the current questionPaperSet's groupId.
                                        if(groupId != questionPaperSet.groupId) {
                                            groupId = questionPaperSet.groupId;
                                            show = true
                                        }else{
                                            // if the groupId is the same as the current questionPaperSet's groupId, skip the current iteration.
                                            show = false;
                                        }
                                    }
                                    if(show){
                                %>
                                <tr>
                                    <td>${questionPaperSet.questionPaperName}</td>
                                   <td>${questionPaperSet.name}</td>
                                    <td>${g.formatDate(date: questionPaperSet.dateCreated, format: 'dd-MM-yyyy HH:mm')}</td>
                                    <td>
                                        <g:each in="${groupedQuestionPapers[questionPaperSet.groupId]}" var="set">
                                            <a href="${createLink(controller: 'QuestionPaper', action: 'viewQuestionPaper', params: [id: set.id])}" class="btn btn-sm btn-primary">
                                                View Set ${counter}
                                            </a>
                                            <% counter++; %>
                                        </g:each>
                                    </td>
                                </tr>
                                <% } %>
                            </g:each>
                            </tbody>
                        </table>

                        <!-- Pagination Controls -->
                        <div class="text-center">
                            <g:if test="${offset > 0}">
                                <a href="${createLink(controller: 'QuestionPaper', action: 'listQuestionPapers', params: [offset: offset - max, max: max])}" class="btn btn-outline-secondary">
                                    Previous
                                </a>
                            </g:if>

                            <g:if test="${offset + max < totalSets}">
                                <a href="${createLink(controller: 'QuestionPaper', action: 'listQuestionPapers', params: [offset: offset + max, max: max])}" class="btn btn-outline-secondary">
                                    Next
                                </a>
                            </g:if>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
