
<g:render template="/ebouquet/navheader_new"></g:render>
<link rel="preconnect" href="https://fonts.gstatic.com">
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700;800&display=swap" rel="stylesheet">
<style>
.card {
    border-radius: 25px;
}
.card h1 {
    text-transform: uppercase;
    font-weight: bold;
    margin-bottom: 20px;
    font-family: 'Open Sans', sans-serif;
}
.card p {
    color: #000000;
    font-family: 'Open Sans', sans-serif;
}
.contact_form {
    background: #ddd;
    border-radius: 12px;
}
.contact_form input, .contact_form textarea {
    border: none;
    border-radius: 10px;
    color: #000000;
    font-family: 'Open Sans', sans-serif;
}
.contact_form .form-control:focus {
    color: #000000;
    border: none !important;
    box-shadow: none !important;
}
.contact_form button {
    border-radius: 10px;
    text-transform: uppercase;
    font-weight: 700;
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
}
</style>
<section class="contactus">
    <div class="container">
        <div class="card shadow p-4 p-md-5 my-md-5 col-12 col-lg-10 mx-auto">
            <h1>Request a Demo</h1>
            <p>Looking to create a virtual eBook library for your organization—Just fill the form below, and we will take you through a live demo of SAGE e-bouquet to show how. <strong>Seeing is believing!</strong></p>
            <p>If we’ve got you curious and excited, call us right away and speak to our sales manager, Rajesh Raheja at 9821161908 to know more.</p>
            <div class="contact_form col-12 col-md-8 col-lg-6 px-0 my-4 p-4">
                <form id="requestDemoForm" class="contact-form" method="post" action="/log/addRequestDemoForm" novalidate>
                    <div class="form-group">
                        %{--<label>Name <span class="text-danger">*</span> </label>--}%
                        <input type="text" name="name" class="form-control" placeholder="Name" maxlength="30" required>
                        <div class="invalid-feedback">
                            Please enter name.
                        </div>
                    </div>
                    <div class="form-group">
                        %{--<label>Name <span class="text-danger">*</span> </label>--}%
                        <input type="text" name="jobTitle" class="form-control" placeholder="Designation" maxlength="30" required>
                        <div class="invalid-feedback">
                            Please enter Job title or Designation.
                        </div>
                    </div>
                    <div class="form-group">
                        %{--<label>Name <span class="text-danger">*</span> </label>--}%
                        <input type="text" name="companyName" class="form-control" placeholder="Company" maxlength="50" required>
                        <div class="invalid-feedback">
                            Please enter Business or Company name.
                        </div>
                    </div>
                    <div class="form-group">
                        %{--<label>Email <span class="text-danger">*</span> </label>--}%
                        <input type="email" id="email" name="email" class="form-control" placeholder="Email" maxlength="50" required
                               pattern="^[a-zA-Z0-9]+(\.[_a-zA-Z0-9]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*(\.[a-zA-Z]{2,15})$" >
                        <div class="invalid-feedback">
                            Please enter valid email address.
                        </div>
                    </div>
                    <div class="form-group">
                        %{--<label>Phone Number <span class="text-danger">*</span> </label>--}%
                        <input type="tel" class="form-control" id="phone" name="phone" minlength="10" maxlength="10" placeholder="Mobile" required
                               pattern="^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$" onkeypress="return onlyNumberKey(event)" >
                        <div class="invalid-feedback">
                            Please enter valid phone number.
                        </div>
                    </div>
                    <div class="form-group">
                        %{--<label>Comment <span class="text-danger">*</span> </label>--}%
                        <textarea class="form-control" name="comment" placeholder="Message" required></textarea>
                        <div class="invalid-feedback">
                            Please enter your comment.
                        </div>
                    </div>
                    %{--<div class="form-group form-check">
                        <label class="form-check-label"><input type="checkbox" name="copy" class="form-check-input"> Send me a copy of this submission.</label>
                    </div>--}%
                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary col-md-4">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<script>
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('contact-form');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    } else {
                        setTimeout(function(){
                            alert('Submitted successfully!');
                        },100);
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    function onlyNumberKey(evt) {
        // Only ASCII charactar in that range allowed
        var ASCIICode = (evt.which) ? evt.which : evt.keyCode
        if (ASCIICode > 31 && (ASCIICode < 48 || ASCIICode > 57))
            return false;
        return true;
    }

</script>

<g:render template="/ebouquet/footer_new"></g:render>
