
<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/profile.css" async="true"/>
<style>

    @media screen and (min-width:768px) {
        .user_profile_scroller {
            overflow-x: scroll;
        }
    }
</style>
<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="overlay-profile">
    <div class="bg-white d-flex justify-content-between align-items-center d-lg-none mt-2 p-2">
        <div class="d-flex align-items-center my-profile">
            <button class="btn btn-backward pt-2 pb-0" onclick="javascript:history.back()"><i class="material-icons">keyboard_backspace</i></button>
            <p class="ml-1 p-0">My Profile</p>
        </div>
        <div>
            <button type="button" class="btn btn-leaderboard" onclick="leaderBoard()";><i class="material-icons">emoji_events</i> <span>Leaderboard</span></button>
        </div>
    </div>
    <div class="profile-layer pb-4 pb-lg-0">
        <div class="d-none d-lg-flex align-items-center my-profile">
            <button class="btn btn-backward ml-4 mt-2" onclick="javascript:history.back()"><i class="material-icons">keyboard_backspace</i></button>
        </div>
            <div class="jumbotron">

                <div class="row">
                   <div class="col-12 col-lg-4 text-center">
                       <!--@krishna Use class for updating rank field-->
%{--                       <div class="d-none d-lg-block">--}%
%{--                            <h4 class="rank">#5</h4>--}%
%{--                       </div>--}%
                       <!---->

                       <div class="profile mt-3">
                           <div class="image-wrapper">
                            <g:if test="${user.profilepic}">
                               <img src="/funlearn/showProfileImage?id=${user.id}&fileName=${user.profilepic}&type=user&imgType=passport" alt="" class="rounded-circle">
                            </g:if>
                               <g:else>
                                     <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle">
                               </g:else>
                            <g:if test="${showEdit}">
                                <a href="/usermanagement/editprofile" ><i class="material-icons" >edit</i></a>
                            </g:if>
                           </div>
                           <h4 class="user-name mt-2"><span id="firstname">${user.name}</span> <span id="secondname" class="ml-2"></span></h4>

                        <g:if test="${user.lifeTimePoints > 5000}">
                           <div class="d-flex align-items-center justify-content-center mt-2">
                               <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i><i class="material-icons">grade</i><i class="material-icons">grade</i></p>
                               <span class="wonderwonk">Wonder Wonk Ultimate</span>
                           </div>
                        </g:if>

                       <g:elseif test="${user.lifeTimePoints > 3000}">
                           <div class="d-flex align-items-center justify-content-center mt-2">
                               <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i><i class="material-icons">grade</i></p>
                               <span class="wonderwonk">Wonder Wonk Supreme</span>
                           </div>
                       </g:elseif>

                       <g:elseif test="${user.lifeTimePoints > 1000}">
                           <div class="d-flex align-items-center justify-content-center mt-2">
                               <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i></p>
                               <span class="wonderwonk">Wonder Wonk Star</span>
                           </div>
                       </g:elseif>

                       <g:elseif test="${user.lifeTimePoints > 0}">
                           <div class="d-flex align-items-center justify-content-center mt-2">
                               <p id="grade-rating"><i class="material-icons">grade</i></p>
                               <span class="wonderwonk">Wonder Wonk</span>
                           </div>
                       </g:elseif>

                           <div class="d-none d-lg-block">
                                <button type="button" class="btn btn-leaderboard mt-2" onclick="leaderBoard()"><i class="material-icons">emoji_events</i> <span>Leaderboard</span></button>
                           </div>
                       <!--@krishna Use class for updating rank field-->
                       <div class="d-lg-none">
                        <h4 class="rank mt-3" > <span class="my-rank"></span> </h4>
                       </div>
                       <!---->
                       </div>
                   </div>
                   <div class="col-12 col-lg-8">
                       <div class="d-flex justify-content-center align-items-center">
                           <button class="btn btn-makepoint d-none d-lg-flex" data-toggle="modal" data-target="#tutorial">How can I make more points? <i class="material-icons">info</i> </button>
                           <div class="d-flex ml-lg-4 justify-content-between">
                               <p class="points-earned-text"><span>Points earned</span></p>
                               <p class="points-earned">${user.lifeTimePoints}</p>
                           </div>
                       </div>
                       <div class="collapse d-lg-flex justify-content-center" id="tap-details">
                           <div class="d-flex col-12 col-lg-6 justify-content-center justify-content-lg-between m-lg-auto mtProfile">
                               <div class="title-profile">
                                   <g:each var="userPointsModuleWise" in="${userPointsModuleWiseList}">
                                       <p>${userPointsModuleWise.scoreType}</p>
                                   </g:each>
                               </div>
                               <div class="amt-profile">
%{--                                   <p id="total-doubts">${user.lifeTimePoints}</p>--}%
                                   <g:each var="userPointsModuleWise" in="${userPointsModuleWiseList}">
                                       <p>${userPointsModuleWise.score}</p>
                                   </g:each>
                               </div>
                           </div>
                       </div>
                       <button class="btn btn-tap-details mt-2 d-flex d-lg-none" data-toggle="collapse" >Tap to view details</button>
                     </div>
                </div>
            </div>
            <div class="row justify-content-center justify-content-lg-start pb-4 pb-lg-0 mx-0">
                <button class="btn btn-makepoint d-lg-none mb-4" data-toggle="modal" data-target="#tutorial">How can I make more points? <i class="material-icons">info</i> </button>
            </div>
    </div>


    <div class="container" id="leaderBoard" style="display: none;">
        <div class="leaderboard-layer pb-4 pb-lg-0">
            <div class="jumbotron">
                <div class="row">
                    <div class="col-12 col-lg-5 leaderboard-left">
                        <div class="d-flex justify-content-between align-items-center mt-3 p-1">
                            <div class="d-flex align-items-center leader-board">
                                <button class="btn btn-backward pt-2 pb-0" onclick="backToProfile()"><i class="material-icons">keyboard_backspace</i></button>
                                <p class="ml-1 p-0">LeaderBoard</p>
                            </div>
                            <div>
                                <label class="switch">
                                    <input onchange="rankTypeFilterChanged()" type="checkbox" checked>
                                    <span class="slider d-flex justify-content-around align-items-center">
                                        <p>All</p>
                                        <p>7 days</p>
                                    </span>
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-center align-items-center calc-height">

                            <div class="row w-100">

                                <div id="top-two" class="col-4 leader-img mt-4">
                                </div>
                                <div id="top-one" class="col-4 leader-img big">

                                </div>
                                <div id="top-three" class="col-4 leader-img mt-4">
                                </div>
                            </div>
                        </div>

                        <div class="card-wrappers d-flex align-items-center justify-content-between flex-nowrap">
                            <div class="profile-img">

                                <g:if test="${user.profilepic}">
                                    <img src="/funlearn/showProfileImage?id=${user.id}&fileName=${user.profilepic}&type=user&imgType=passport" alt="" class="rounded-circle">
                                </g:if>
                                <g:else>
                                    <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="rounded-circle">
                                </g:else>


%{--                                <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="rounded-circle">--}%
                                <p class="mt-1">${user.name}</p>
                            </div>
                            <div>
                                <p>My Rank</p>
                                <h3 class="mt-2"> <span class="my-rank"></span> <span>/10</span></h3>
                            </div>
                            <div>
                                <p>Points</p>
                                <h3 class="mt-2" id="my-module-score">${user.lifeTimePoints}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-7 d-flex justify-content-center reset-padding mt-lg-0">
                        <div class="container text-center mt-4">
                            <table class="table table-borderless">
                                <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Name</th>
                                    <th>Points</th>
                                </tr>
                                </thead>
                                <tbody id="leader-board-table">

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" id="tutorial">
        <div class="modal-dialog">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <div></div>
%{--                    <div class="ml-4">--}%
%{--                        <ul class="carousel-indicators indicator-override">--}%
%{--                            <li data-target="#demo" data-slide-to="0" class="active"></li>--}%
%{--                            <li data-target="#demo" data-slide-to="1"></li>--}%
%{--                        </ul>--}%
%{--                    </div>--}%
                    <div>
                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                    </div>
                </div>

                <!-- Modal body -->
                <div class="modal-body d-flex align-items-end justify-content-lg-center align-items-lg-center">
                    <div id="demo" class="carousel" data-ride="carousel">

                        <!-- Indicators -->


                        <!-- The slideshow -->
                        <div class="carousel-inner">
                        <div class="carousel-item active tutor text-center">
                            <h2>Before you start!</h2>
                            <p><strong>Stars</strong> Shows the level of <strong>User</strong> who has posted the Question or the Answer</p>
                            <div class="d-flex align-items-center justify-content-center mt-4">
                                <p id="grade-rating"><i class="material-icons">grade</i></p>
                                <span class="wonderwonk">Wonder Wonk Star (1000 points)</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-center mt-2">
                                <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i></p>
                                <span class="wonderwonk">Wonder Wonk Supreme (3000 points)</span>
                            </div>
                            <div class="d-flex align-items-center justify-content-center mt-2">
                                <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i> <i class="material-icons">grade</i></p>
                                <span class="wonderwonk">Wonder Wonk Ultimate (5000 points)</span>
                            </div>
                            <img src="${assetPath(src: 'ws/line-doubts.png')}">
                            <div class="mt-4">
                                <p><strong>Answering a question</strong> earns you +5 Points.
                                Try and help your peers as much as you can!</p>
                                <button class="btn btn-answer mt-4 mb-2"><i class="material-icons">chat</i> Answer</button>
                            </div>
                            <img src="${assetPath(src: 'ws/line-doubts.png')}">
                            <div class="d-flex justify-content-lg-center align-items-center mt-4 mb-4">
                                <div class="circle"><i class="material-icons">share</i> </div>
                                <div class="ml-2"><p><strong>Sharing Questions and Answers</strong> will make you earn extra <strong>+2 Points</strong>.</p></div>
                            </div>
                            <img src="${assetPath(src: 'ws/line-doubts.png')}">
                            <div class="d-flex justify-content-lg-center align-items-center mt-4">
                                <div class="circle"><i class="material-icons">thumb_up</i> </div>
                                <div class="ml-2"><p>If your question or answer gets “thanked", you’ll earn <strong>+2 Points.</strong></p></div>
                            </div>
                        </div>
%{--                            <div class="carousel-item tutor text-center">--}%
%{--                                <h2>Before you start!</h2>--}%
%{--                                <p><strong>Stars</strong> Shows the level of <strong>User</strong> who has posted the Question or the Answer</p>--}%
%{--                                <div class="d-flex align-items-center justify-content-center mt-4">--}%
%{--                                    <p id="grade-rating"><i class="material-icons">grade</i></p>--}%
%{--                                    <span class="wonderwonk">Wonder Wonk Ultimate (1000 points)</span>--}%
%{--                                </div>--}%
%{--                                <div class="d-flex align-items-center justify-content-center mt-2">--}%
%{--                                    <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i></p>--}%
%{--                                    <span class="wonderwonk">Wonder Wonk Ultimate (3000 points)</span>--}%
%{--                                </div>--}%
%{--                                <div class="d-flex align-items-center justify-content-center mt-2">--}%
%{--                                    <p id="grade-rating"><i class="material-icons">grade</i><i class="material-icons">grade</i> <i class="material-icons">grade</i></p>--}%
%{--                                    <span class="wonderwonk">Wonder Wonk Ultimate (5000 points)</span>--}%
%{--                                </div>--}%
%{--                                <img src="${assetPath(src: 'ws/line-doubts.png')}">--}%
%{--                                <div class="mt-4">--}%
%{--                                    <p><strong>Answering a question</strong> earns you +5 Points.--}%
%{--                                    Try and help your peers as much as you can!</p>--}%
%{--                                    <button class="btn btn-answer mt-4 mb-2"><i class="material-icons">chat</i> Answer</button>--}%
%{--                                </div>--}%
%{--                                <img src="${assetPath(src: 'ws/line-doubts.png')}">--}%
%{--                                <div class="d-flex mt-4 mb-4">--}%
%{--                                    <div class="circle"><i class="material-icons">share</i> </div>--}%
%{--                                    <div><p><strong>Sharing Questions and Answers</strong> will make you earn extra <strong>+2 Points</strong>.</p></div>--}%
%{--                                </div>--}%
%{--                                <img src="${assetPath(src: 'ws/line-doubts.png')}">--}%
%{--                                <div class="d-flex mt-4">--}%
%{--                                    <div class="circle"><i class="material-icons">thumb_up</i> </div>--}%
%{--                                    <div><p>If your question or answer gets “thanked�?, you’ll earn <strong>+2 Points.</strong></p></div>--}%
%{--                                </div>--}%
%{--                            </div>--}%
                        </div>

                    </div>
                </div>

                <!-- Modal footer -->
                <div class="modal-footer">

                </div>

            </div>
        </div>
    </div>
</section>
<script>
    var leaderBoardFilterValue = "";
    function leaderBoard(){
        $('#leaderBoard').show();
        $('.profile-layer').hide();
        $('.bg-white').removeClass('d-flex').addClass('d-none');
    }
    function backToProfile(){
        $('#leaderBoard').hide();
        $('.profile-layer').show();
        $('.bg-white').removeClass('d-none').addClass('d-flex');
    }

    function rankTypeFilterChanged() {
        $('.loading-icon').removeClass('hidden');
        var scoreType = ${scoreType};
        if(leaderBoardFilterValue == "7days") leaderBoardFilterValue = "lifeTime";
        else if(leaderBoardFilterValue == "lifeTime") leaderBoardFilterValue = "7days";
        else if(leaderBoardFilterValue == "") leaderBoardFilterValue = "7days";
        <g:remoteFunction controller="usermanagement" action="getTopRankers"  onSuccess="displayTopers(data)" params="'scoreType='+scoreType+'&rankType='+leaderBoardFilterValue" />
    }

    function displayTopers(data) {
        var myRank = "--",myModuleScore=0;
        var username = $("<textarea/>").html("${user.username}").text();
        var htmlStr = "";
        for(var i=data.topers.length - 1; i>= 0; i--){
            if(username == data.topers[i].username) myRank = "#" + data.topers[i].rank;
            htmlStr = htmlStr + "<tr>\n" +
                " <td>"+data.topers[i].rank+"</td>\n" +
                " <td>"+data.topers[i].name+"</td>\n" +
                " <td>"+data.topers[i].score+"</td>\n" +
                " </tr>";
        }
        $("#leader-board-table").html(htmlStr);
        if(leaderBoardFilterValue == "7days") myModuleScore = ${user7daysPoints};
        else if(leaderBoardFilterValue == "lifeTime") myModuleScore = ${lifeTimePoints};
        $('.my-rank').text(myRank);
        $('#my-module-score').text(myModuleScore)
        if(data.topOne != undefined && data.topOne != null && data.topOne.rank != undefined){
            htmlStr = "<h4 class=\"mb-4\">#1</h4>\n" ;
            if(data.topOne.userImg) {
                htmlStr += " <img src=\"/funlearn/showProfileImage?id=" + data.topOne.userId + "&fileName=" + data.topOne.userImg + "&type=user&imgType=passport\" alt=\"\" class=\"rounded-circle big\">";
            }
            else{
                htmlStr +=" <img src=\"${assetPath(src: 'landingpageImages/img_avatar3.png')}\" alt=\"\" class=\"rounded-circle\">";
            }
            htmlStr += " <h3 class=\"mt-3\">"+data.topOne.name+"</h3>\n" +
                " <p class=\"mt-1\">"+data.topOne.score+"</p>";
            $("#top-one").html(htmlStr);

        }
        if(data.topTwo != undefined && data.topTwo != null && data.topTwo.rank != undefined){
            htmlStr = "<h4 class=\"mb-4\">#2</h4>\n" ;
            if(data.topTwo.userImg) {
                htmlStr +=" <img src=\"/funlearn/showProfileImage?id=" + data.topTwo.userId + "&fileName=" + data.topTwo.userImg + "&type=user&imgType=passport\" alt=\"\" class=\"rounded-circle\">";
            }
            else{
                htmlStr +=" <img src=\"${assetPath(src: 'landingpageImages/img_avatar3.png')}\" alt=\"\" class=\"rounded-circle\">";
            }
            htmlStr +=" <h3 class=\"mt-3\">"+data.topTwo.name+"</h3>\n" +
                " <p class=\"mt-1\">"+data.topTwo.score+"</p>";
            $("#top-two").html(htmlStr);

        }
        if(data.topThree != undefined && data.topThree != null && data.topThree.rank != undefined){
            htmlStr = "<h4 class=\"mb-4\">#3</h4>\n" ;
            if(data.topThree.userImg) {
                htmlStr += " <img src=\"/funlearn/showProfileImage?id=" + data.topThree.userId + "&fileName=" + data.topThree.userImg + "&type=user&imgType=passport\" alt=\"\" class=\"rounded-circle\">";
            }
            else{
                htmlStr +=" <img src=\"${assetPath(src: 'landingpageImages/img_avatar3.png')}\" alt=\"\" class=\"rounded-circle\">";
            }
            htmlStr += " <h3 class=\"mt-3\">"+data.topThree.name+"</h3>\n" +
                " <p class=\"mt-1\">"+data.topThree.score+"</p>";
            $("#top-three").html(htmlStr);

        }
        $('.loading-icon').addClass('hidden');
    }

    rankTypeFilterChanged();

</script>

<g:render template="/books/footer_new"></g:render>
<script>
    $(".btn-tap-details").click(function(){
        $(".collapse").collapse('toggle');
    });
    $(".collapse").on('show.bs.collapse', function(){
        $('.btn-tap-details').text('Tap to Hide details');
    });

    $(".collapse").on('hide.bs.collapse', function(){
        $('.btn-tap-details').text('Tap to Show details');
    });

</script>
