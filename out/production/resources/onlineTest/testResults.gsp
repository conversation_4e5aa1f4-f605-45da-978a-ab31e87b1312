<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <div class="container">
                        <h2 class="text-center">Results for Test: ${testName}</h2>
                        <hr/>

                        <!-- Download Excel Link -->
                        <p class="text-right">
                        <%if("true".equals(showAnalytics)){%>
                        <g:link action="testQuestionAnalytics" params="[testId:testId, batchId:batchId]" class="btn btn-primary" target="_blank">
                            Analytics
                        </g:link>
                        <%}%>
                            <g:link action="downloadResultsExcel" params="[testId:testId, batchId:batchId]" class="btn btn-primary">
                                Download Excel
                            </g:link>
                        </p>

                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Correct</th>
                                <th>Incorrect</th>
                                <th>Skipped</th>
                                <th>Time (seconds)</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            <g:if test="${resultsList}">
                                <g:each in="${resultsList}" var="r">
                                    <tr>
                                        <td>${r.name}</td>
                                        <td>${r.status}</td>
                                        <td>${r.correctAnswers ?: 0}</td>
                                        <td>${r.incorrectAnswers ?: 0}</td>
                                        <td>${r.skipped ?: 0}</td>
                                        <td>${r.timeTaken ?: 0}</td>
                                        <td><%if(r.quizRecId){%>
                                            <g:link controller="prepjoy" action="prepJoyGame" params="[quizRecId:r.quizRecId, historyPage:'true',learn:'false']" class="btn btn-info btn-sm" target="_blank">Details</g:link>
                                            <%}%>
                                        </td>
                                    </tr>
                                </g:each>
                            </g:if>
                            <g:else>
                                <tr>
                                    <td colspan="8">No students found.</td>
                                </tr>
                            </g:else>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
