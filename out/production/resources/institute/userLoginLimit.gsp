<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<script>
    var loggedIn=false;
</script>
<style>
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main p-4' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <label for="userValue" style="display: block; font-weight: bold">Get User</label>
                <div class="form-group form-inline miguser">
                    <input type="text" class="form-control col-md-4 mr-4" name="userValue" id="userValue"  placeholder="Enter Username">
                    %{--&nbsp;&nbsp;Mobile number&nbsp;<input type="radio" name="userMode" value="mobile" checked>&nbsp;&nbsp;Email&nbsp;<input type="radio" name="userMode" value="email"><br>--}%
                    <button class="btn btn-lg btn-primary col-2 m-0"  onclick="getUser();">Get User</button>
                </div>
                <div id="errormsg" class="alert alert-danger has-error mt-4" role="alert" style="display: none; background: none;"></div>
                <div id="successmsg" style="display: none"></div>
                <div id="batchUsers" class="mt-4" style="display: none"></div>
                <div id="mmsg" class="mt-4" style="display: none; text-align: center; font-weight: bold"></div>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="removeLimit">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <h4 class="modal-title">Login Limit</h4>
                <button type="button" class="close" data-dismiss="modal" style='margin-top: -26px;'>&times;</button>
            </div>

            <!-- Modal body -->
            <div class="modal-body text-center">
                <input type='number' name='number' id='limit' placeholder='Enter Limit' maxlength="2" style='padding: 1rem;' oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">             </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="submit" class="btn btn-danger"  onclick="javascript:submitsearch()">Submit</button>
            </div>

        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

<script>
    var oldUsername;
    function getUser(){

        if(document.getElementById("userValue").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the user name"
            $("#errormsg").show();
        }
        else {
            $("#errormsg").hide();
            $("#successmsg").hide();
            $("#batchUsers").show();
            var userValue = document.getElementById("userValue").value;
            <g:remoteFunction controller="institute" action="getUsers" params="'userValue='+userValue" onSuccess = "showUsers(data);"/>
        }

    }

    function openModal(){
        oldUsername = oldUsername;
        $('#removeLimit').modal('show');

    }

    function showUsers(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "                    <table class='table table-hover table-bordered'>\n" +
            "                        <tr class='bg-primary text-white'>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Username</th>\n" +
            "                            <th>Limit</th>\n" +
            "                            <th class='text-center'>Login Limit</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var users = data.userList;
            oldUsername=users.username;
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users.name+"</td>"+
                    "<td>"+users.username+"</td>" +
                    "<td>"+users.maxLogins+"</td>" +
                    "<td class='text-center'><button type=\"button\" class=\"btn btn-primary\" onclick=\"javascript:openModal()\">\n" +
                    "    Edit Login Limit\n" +
                    "  </button>\n" +
                    "  </td>"+
                    "</tr>";
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
        }else{
            document.getElementById("batchUsers").innerHTML= "No user found with this information.";
        }
        $("#batchUsers").show();

    }

    function submitsearch(){
        $('#removeLimit').modal('hide');
        var userlimit = document.getElementById("limit").value;

        <g:remoteFunction controller="institute" action="addUserLoginLimit" params="'loginLimit='+userlimit+'&username='+oldUsername" onSuccess='showUsersdata(data);'/>
    }

    function showUsersdata(data){
        if(data.status=="OK"){
            document.getElementById("mmsg").innerText = "Updated Successfully!";
            $("#mmsg").show();
            getUser();
        }else{
            document.getElementById("mmsg").innerText = "Update Failed!";
        }
    }






</script>


</body>
</html>