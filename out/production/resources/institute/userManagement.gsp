<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<asset:javascript src="moment.min.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.8.0/jszip.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.8.0/xlsx.js"></script>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.dataTables.min.css" type="text/css" rel="stylesheet">
<asset:stylesheet href="stylesheets/dataTablesMobile.css" async="true"/>
<% if("books".equals(session["entryController"]) || "libwonder".equals(session["entryController"]||"true".equals(""+session["commonWhiteLabel"]))){%>
<style>
.eutkarsh.evidya.etexts.ebouquet.custom-fix button#toggle {
    display:none;
}
.libwonder button#toggle {
    display:none;
}

    @media (max-width:575px)
    {
/* 		!* Force table to not be like tables anymore */
table, thead, tbody, th, td, tr {
    display: block;
}

    }

table.dataTable th, table.dataTable td {
    font-family: 'Poppins', sans-serif !important;
    font-size: 13px;
}
table.dataTable thead th {
    font-weight: 500;
}
table.dataTable td button {
    font-size: 13px;
}
</style>
<%}%>
<script>
    var loggedIn=false;
</script>
<style>
#addBooksToUserModal {
    z-index: 9992;
}
#addBooksToUserModal h5 {
    font-family: 'Poppins', sans-serif !important;
}
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}

.page-item.active .page-link {
    color: #ffffff !important;
}
.page-item.disabled .page-link {
    color: #6c757d !important;
    cursor: auto !important;
}
.pagination .page-link:not(:disabled):not(.disabled) {
    cursor: pointer !important;
}
.pagination .page-link {
    padding: .5rem .75rem !important;
    margin-left: -1px !important;
    color: #007bff !important;
    /*background-color: #fff;*/
    border: 1px solid #dee2e6;
    font-size: 14px !important;
}
table.dataTable {
    width: 100% !important;
}
.form-group .btn-group {
    width: 100%; }

.pagination li a {
    font-size: 14px;
}
table.dataTable th, table.dataTable td {
    white-space: normal !important;
}
#batchUsers tbody {
    display: table-row-group;
}
/*table.dataTable thead th:first-child {
    width:100% !important;
}*/
table.dataTable thead th:nth-child(2) {
    width:40% !important;
}

</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-10 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3>${pageTitle}</h3>
                </div>
                <% if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"])) || session["siteId"].intValue()==25){%>
                <div class="col-md-3 ">
                    <select id="instituteAction" name="instituteAction" class="form-control admin" onchange="instituteAction(this.value);">
                        <option value="" disabled="disabled" selected>Select Action</option>
                        <% if(ipRestricted != "true"&&(params.accessMode==null||manageUsers)) { %>
                        <option value="addUser" >Add ${userType}</option>
                        <option value="viewUser" >View ${userType}</option>
                        <% if(params.userType==null) { %>
                        <option value="generateAccessCode" >Generate Access Code </option>
                        <option value="viewAccessCode" >View Access Code </option>
                        <%}%>
                        <%}%>
                        <% if(params.accessMode==null||manageBooks) { %>
                        <option value="viewBook" >View eBooks</option>
                        <%}%>
                        <% if(params.accessMode==null||manageWaitingList) { %>
                        <option value="viewWaitingList" >View Waiting List</option>
                        <%}%>
                    </select>
                </div>
                <%}%>
                <% if(ipRestricted != "true" ) { %>
                <div id="addUsers" class="p-2 d-none" >
                    <h6 id="noOfUsers"></h6>
                    <h6 id="usedlicenses"></h6>
                    <h6 id="pendinglicenses"></h6>
                    <div class="form-group">
                        <form id="orderManagement" name="orderManagement">

                            <div class="form-group col-md-10">
                                <input type="text" class="form-control" name="userName" id="userName" placeholder="Name">
                            </div>
                            <div class="form-group col-md-10">
                                <input type="tel" class="form-control" name="mobile" id="mobile" placeholder="Mobile Number" minlength="10" maxlength="10" pattern="[0-9]*" oninput="numberOnly(this.id)">
                            </div>
                            <div class="form-group col-md-10">
                                <input type="text" class="form-control" name="email" id="email" placeholder="Email">
                            </div>
                            <div class="form-group col-md-10">
                                <input type="text" class="form-control" name="pincode" id="pincode" placeholder="Pin code" oninput="numberOnly(this.id)">
                            </div>
                            <div class="form-group col-md-10">
                                <input type="text" class="form-control" name="validityDate" id="validityDate" placeholder="Validity Date">
                            </div>

                            <button class="btn btn-primary btadd" onclick="javascript:addUser1(event)">Add User</button>
                            <button class="btn  btadd" onclick="javascript:clearFields(event)">Clear</button>
                            <div class="form-group col-md-10 mx-auto">
                                <div class="alert alert-sm alert-primary p-2" id="addOrderResult" style="display: none"></div>
                            </div>

                        </form>
                    </div>
                    <div class="form-group">
                   <!--     <textarea class="form-control admin  col-md-5 " name="useremail" id="useremail"  placeholder="Add users email/mobile no.(separated by comma for multiple users)" autocomplete="off"></textarea><br>
                        <% if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))||session['siteId'].intValue()==25){%>
                        <div class="form-group">
                            <input type="text" class="form-control" name="institutePassword" id="institutePassword"  placeholder="Password">
                            <small style="font-size: 70%;">(<em>Password must not contain any space and special characters < > { } # / \ ? : ! & $ ^ * % ` | + "</em>)</small>
                        </div>
                        <%}%>
                        <button class="btn btn-primary btadd" onclick="addUser('student');">Add ${userType}</button> -->
                        <%if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))||session['siteId'].intValue()==25){%>
                        <p style="margin: 1rem;margin-left: 6rem;color: #444;"><b>- or -</b></p>
                            <div class="form-group">
                                <input type="hidden" name="mode" value="submit">
                                <input id="FileInputElement" type="file" class="form-control" name="FileInputElement" accept=".xlsx" />
                                <small><em>(Max 200 users at once)</em></small>
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control" name="institutePassword1" id="institutePassword1"  placeholder="Password">
                                <small style="font-size: 70%;">(<em>Password must not contain any space and special characters < > { } # / \ ? : ! & $ ^ * % ` | + "</em>)</small>
                            </div>
                            <button class="btn btn-primary btadd" onclick="javascript:uploadNotes()">Upload Users</button>
                            <button class="btn btn-primary btadd" onclick="javascript:downloadUploadFileSample()">Download Sample</button>
                        <%}%>
                    </div>
                <%}%>
                <% if(session["siteId"].intValue()!=1&&!"true".equals(""+session["commonWhiteLabel"]) && session["siteId"].intValue()!=25){%>
                <button onclick="getUserDetails()" class="btn btn-lg btn-primary mt-4">Get User Details</button>
                <%}%>
                </div>
                <% if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"])) || session["siteId"].intValue()==25){%>
                <div id="addGenerateAccessCode" class="p-3 mt-3 d-none">
                    <div class="form-row">
                        <div class="col-12 col-md-4 col-lg-3">
                            <div class="form-group form-group-modifier mb-0">
                                <input type="number" min="1" max="999" step="1" class="form-control form-control-modifier border-bottom w-100" name="accesscodegen" id="accesscodegen"  placeholder="Enter number.">
                            </div>
                        </div>
                        <div class="col-6 col-md-3 col-lg-2">
                            <button class="btn btn-primary btadd" onclick="addGenerateAccessCode();">Submit</button>
                        </div>
                    </div>
                </div>
                <%}%>
                <div style="margin-top: 10px;" class="p-2">
                    <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display:none;background: none;"></div>
                    <div id="successmsg" class="d-none"></div>
                    <div id="batchUsers" class=" d-none p-2"></div>
                    <h5 id="successmsgnew" class="d-none" style="color: green"></h5>
                </div>
            </div>
        </div>
    </div>

</div>
<div id='validityEdit'>
    <div class="modal fade" id="validityEditModal">
        <div class="modal-dialog  modal-dialog-centered">
            <div class="modal-content">

                <div class="modal-header">
                    <h3>Edit Validity Date</h3>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class="modal-body">
                    <div class="row quiz mb-4 px-2 px-lg-5" >
                        <div class="col-1 col-md-10 text-left" id="editName">

                        </div>
                    </div>
                    <div class="row quiz mb-4 px-2 px-lg-5">
                        <div class="col-1 col-md-10 text-left">
                            <b>Date:</b> <input type="text"  id="validityDateEdit" name="validityDateEdit">
                        </div>
                    </div>


                    <div class="row quiz6 ">
                        <div class="form-group">
                            <div class=" col-sm-12 text-center ">
                                <button type="button" onclick="javascript:formCancel('Next')" class="btn btn-primary">Cancel</button>
                                <button type="button" onclick="javascript:formSave('Done')" class="btn btn-primary">Save</button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>

<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>





<script>
    var batchId="${batchId}";
    var noOfUsers=null;
    var instituteId="${instituteId}";
    var pendinglicenses=null;
    var userType='student';
    const siteId1 = "${session["siteId"]}"
    function addUser(){
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        var batch = document.getElementById("batches");
        <%if("Instructors".equals(userType)&&(session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))){%>
        userType = "instructor";
        <%}%>

        <% if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))||session['siteId'].intValue()==25){%>
        var password=document.getElementById("institutePassword").value;
        password=password.replace(/&/g,'~');
        <%}%>

        if(document.getElementById("useremail").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the email id."
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            $("#errormsg").hide();
            var email = document.getElementById("useremail").value;
            var namearr=email.split(',');
            var count=namearr.length;
            if(validateMultipleEmailsCommaSeparated(email)){
                if(noOfUsers!=-1) {
                    if (pendinglicenses >= count) {
                        $('.loading-icon').removeClass('hidden');
                        <%if(session["siteId"].intValue()==24){%>
                        <g:remoteFunction controller="institute" action="genarateUsertoBatch" params="'batchId='+batchId+'&useremail='+email+'&instituteId='+instituteId" onSuccess = "generateUserAdded(data);"/>
                        <%}else if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"])) || session["siteId"].intValue()==25){ %>

                        <g:remoteFunction controller="institute" action="genarateUsertoBatchWS" params="'password='+password+'&batchId='+batchId+'&useremail='+email+'&userType='+userType" onSuccess = "userAdded(data);"/>

                        <%}else{%>
                        <g:remoteFunction controller="institute" action="addUserToInstitute" params="'batchId='+batchId+'&useremail='+email" onSuccess = "userAdded(data);"/>
                        <%}%>
                    } else {
                        document.getElementById('errormsg').innerText='No licenses left.'
                        $("#errormsg").show();
                    }
                }else{
                    $('.loading-icon').removeClass('hidden');
                    <%if(session["siteId"].intValue()==24){%>
                    <g:remoteFunction controller="institute" action="genarateUsertoBatch" params="'batchId='+batchId+'&useremail='+email+'&instituteId='+instituteId" onSuccess = "generateUserAdded(data);"/>
                    <%}else if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))  || session["siteId"].intValue()==25){ %>

                    <g:remoteFunction controller="institute" action="genarateUsertoBatchWS" params="'password='+password+'&batchId='+batchId+'&useremail='+email+'&userType='+userType" onSuccess = "userAdded(data);"/>

                    <%}else{%>
                    <g:remoteFunction controller="institute" action="addUserToInstitute" params="'batchId='+batchId+'&useremail='+email" onSuccess = "userAdded(data);"/>
                    <%}%>
                }
            }
        }
    }


    function getUserDetails() {
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();

        $('.loading-icon').removeClass('hidden');
        $("#errormsg").hide();
        var userType='student';
        <%if("Instructors".equals(userType)){%>
        userType = "instructor";
        <%}%>

        <g:remoteFunction controller="institute" action="showAllUsersForInstitute" params="'instituteId='+instituteId+'&batchId='+batchId+'&userType='+userType" onSuccess = "showUsersForInstitute(data);"/>


    }

    function searchUser() {
        if(document.getElementById("searchMobile").value==''){
            alert("Enter mobile number");
        }else{
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();

        $('.loading-icon').removeClass('hidden');
        $("#errormsg").hide();
        var userType='student';
        var mobile=document.getElementById("searchMobile").value;
        <%if("Instructors".equals(userType)){%>
        userType = "instructor";
        <%}%>

        <g:remoteFunction controller="institute" action="showAllUsersForInstitute" params="'instituteId='+instituteId+'&batchId='+batchId+'&userType='+userType+'&mobile='+mobile" onSuccess = "showUsersForInstitute(data);"/>
        }

    }
    const downloadUsers = ()=>{
        window.location.href = "/institute/showAllUsersForBatch?download=true&instituteId="+instituteId+"&userType="+userType+"&batchId="+batchId;
    }

    function showUsersForInstitute(data){
        var userType='student';
        <%if("Instructors".equals(userType)){%>
        userType = "instructor";
        <%}%>
        $('.loading-icon').addClass('hidden');

        var htmlStr = ""
        if(data.download){
            htmlStr+= "<button class='btn btn-primary m-2' onclick='downloadUsers()'>Download</button>"
        }
        htmlStr+="<br><input type='number' name='searchMobile' id='searchMobile' maxlength='10' placeholder='User Mobile'>&nbsp;&nbsp;<button class='btn btn-primary m-2' onclick='searchUser()'>Search</button>"
        <%if(((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"])) || session["siteId"].intValue()==25)&&userType!='Instructors'){%>
        htmlStr+=    "                    <table class='table table-striped table-bordered' id='viewuserstd'>\n" +
            "           <thead class='bg-primary text-white text-center'>              <tr>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Email</th>\n" +
            "                            <th>Mobile</th>\n"
        <%if(((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"])) || session["siteId"].intValue()==25)&&userType!='Instructors'){%>
        htmlStr+=
           "                        <th>Admission No.</th>\n"+
            "                        <th>Validity.</th>\n"
        <%}%>
        if(data.delete){
            htmlStr+=
                "                        <th>Delete</th>\n"
        }
        htmlStr+=
            "                        </tr> </thead>\n";

        <%}else{%>
        htmlStr+=    "                    <table class='table table-striped table-bordered' id='viewuser'>\n" +
            "           <thead class='bg-primary text-white text-center'>              <tr>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Email</th>\n" +
            "                            <th>Mobile</th>\n"
        if(data.delete){
            htmlStr+=
                "                        <th>Delete</th>\n"
        }
        htmlStr+=
            "                        </tr> </thead>\n";

        <%}%>


        if(data.status=="OK"){
            var users = data.users;
            for(i=0;i<users.length;i++){
                htmlStr +="<tr id='"+i+"'><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+users[i].email+"</td>" +
                    "<td>"+users[i].mobile+"</td>"

                <%if(((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"])) || session["siteId"].intValue()==25)&& userType!='Instructors'){%>
                htmlStr+=
                    "<td class='text-center'>"+(users[i].admissionNo?users[i].admissionNo:"<input class='m-2 admissionbox' id='admissionNoBox"+i+"' type='text' /><p class='d-none' id='usernamebox"+i+"'>"+users[i].username.split('_')[1]+"</p><a href='javascript:updateAdmissionNumber("+i+","+batchId+")'><i class='fas fa-edit'></i></a>")+"</td>"+
                    "<td>"+users[i].validityDate+"&nbsp;<a href='javascript:updateValidity(\""+users[i].username+"\",\""+users[i].validityDate+"\",\""+users[i].name+"\")'><i class='fas fa-edit'></i></a></td>"
                <%}%>
                if(data.delete) {
                    htmlStr +=
                        "<td class='text-center'><a href='javascript:deleteUser(" + users[i].userId + "," + batchId + ");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt='wonderslate'></a></td>"
                }
                htmlStr+=
                    "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#batchUsers table').DataTable( {
                "ordering": false
            });
            $('#download2').show();
            $('#download').hide();
            $('#download1').hide();
        }else{
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("batchUsers").innerHTML= "No "+userType+" added to this institute yet";
        }
        $("#batchUsers").removeClass('d-none')
    }

    const updateAdmissionNumber=( number, batchId)=>{
        var username = document.getElementById('usernamebox'+number).innerText
        var value = document.getElementById('admissionNoBox'+number).value
        if(value){
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="institute" action="updateAdmissionNumber" params="'batchId='+batchId+'&username='+username+'&admissionNo='+value" onSuccess="showAdmissionNumberUpdateSuccess(data)" />
        }else{
            document.getElementById('errormsg').innerText="Please enter a valid Admission No."
        }
    }
    const showAdmissionNumberUpdateSuccess=(data)=>{
        $('.loading-icon').addClass('hidden');
        if(data.message){
            document.getElementById("successmsg").innerText=data.message;
            $("#successmsg").show();
            $("#successmsg").removeClass('d-none')
        }
        if(data.error){
            document.getElementById("errormsg").innerText=data.error;
            $("#errormsg").show();
            $("#errormsg").removeClass('d-none')
        }
    }

    function userAdded(data){
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        if(!data.showErrorMessage){

            if(data.status=="no user"){
                document.getElementById("errormsg").innerHTML="This user is not registered. Kindly ask the user to register first."
                $("#errormsg").show();
            }else if(data.status=="user present"){
                document.getElementById("errormsg").innerHTML="This user is already added for this institute.Please check."
                $("#errormsg").show();
            }
            else{
                document.getElementById("successmsg").innerHTML=data.status;
                document.getElementById("useremail").value="";
                alert("Added Successfully");
                location.reload();
            }
        }else{
            document.getElementById("useremail").value=""
            if(document.getElementById("institutePassword"))document.getElementById("institutePassword").value=""
            if(data.status=="no user"){
                document.getElementById("errormsg").innerHTML="This user is not registered. Kindly ask the user to register first."
                $("#errormsg").show();
                $("#errormsg").removeClass('d-none')
            }else if(data.status=="user present"){
                document.getElementById("errormsg").innerHTML="This user is already added for this institute.Please check."
                $("#errormsg").show();
                $("#errormsg").removeClass('d-none')
            }
            else{
                document.getElementById("successmsg").innerText="User Added Successfully";
                $("#successmsg").show();
                $("#successmsg").removeClass('d-none')
                document.getElementById("useremail").value="";
                refreshLicenses()
            }
        }

    }
    function generateUserAdded(data){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');
        if(!data.showErrorMessage){
            alert(data.status + data.alreadyPresent)
            document.getElementById("useremail").value="";
            location.reload()
        }else {
            document.getElementById("useremail").value = ""
            if (document.getElementById("institutePassword")) document.getElementById("institutePassword").value = ""
            document.getElementById("successmsg").innerText = data.status + data.alreadyPresent;
            $("#successmsg").show();
            $("#successmsg").removeClass('d-none')
            document.getElementById("useremail").value = "";
            refreshLicenses()
        }
    }
    function validateMultipleEmailsCommaSeparated(emailcntl) {
        $('.loading-icon').addClass('hidden');
        var value = emailcntl;
        if (value != '') {
            var result = value.split(",");
            for (var i = 0; i < result.length; i++) {
                if (result[i] != '') {
                    if(result[i].indexOf('@')>-1){
                        if (!validateEmail(result[i])) {
                            document.getElementById("errormsg").innerText='Please check, `' + result[i] + '` email addresses not valid!';
                            $("#errormsg").show();
                            $("#errormsg").removeClass('d-none')
                            return false;
                        }
                    }else{
                       if(${(session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))} || ${session["siteId"].intValue()==25}){
                           var numberOnly = /^[0-9]*$/gm
                           if (result[i].length!=10 || !numberOnly.test(result[i])) {
                               document.getElementById("errormsg").innerText='Please check, `' + result[i] + '` mobile number not valid!';
                               $("#errormsg").show();
                               $("#errormsg").removeClass('d-none')
                               return false;
                           }
                       }else{
                           document.getElementById("errormsg").innerText='Please check, `' + result[i] + '` email addresses not valid!';
                           $("#errormsg").show();
                           $("#errormsg").removeClass('d-none')
                           return false;
                       }
                    }

                }
            }
        }
        return true;
    }
    function validateEmail(field) {
        var regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,5}$/;
        return (regex.test(field)) ? true : false;
    }
    function getBooksForBatch(){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#batchUsers").show();
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="institute" action="getBooksForBatch" params="'batchId='+batchId" onSuccess = "showBooksForBatch(data);"/>
    }
    function showBooksForBatch(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK") {
            var htmlStr =
                "                    <div class='table-responsive'> <table class='table table-striped table-bordered' id='viewBook'>\n" +
                "           <thead class='bg-primary text-white text-center'>              <tr>\n" +
                "                            <th>Book Id</th>\n" +
                "                            <th>Title</th>\n" +
                "                            <th>Isbn</th>\n"
            <% if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))||session["siteId"].intValue()==25){%>
            htmlStr+=
                "                            <th>Publishers</th>\n"
            <%}%>
            htmlStr+=
                "                            <th>Status</th>\n"
            <% if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))||session["siteId"].intValue()==25){%>
            htmlStr+=
                "                            <th>Number of copies</th>\n" +
                "                            <th>Validity (in days)</th>\n" +
                "                            <th></th>\n" +
                "                            <th></th>\n"
            <%}%>


            htmlStr+=    "</tr> </thead>\n";
        }
        if(data.status=="OK"){
            var books = data.books;
            for(i=0;i<books.length;i++){
                htmlStr +="<tr id='"+i+"'>" +
                    "<td style='text-transform:capitalize;'>"+books[i].bookId+"</td>"+
                    "<td>"+books[i].title+"</td>"+
                    "<td>"+books[i].Isbn+"</td>"
                <% if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))||session["siteId"].intValue()==25){%>
                htmlStr+="<td>"+books[i].publisher+"</td>"
                <%}%>
                htmlStr+="<td>"+("institutePublished"==books[i].bookStatus?"Published":books[i].bookStatus)+"</td>"

                <% if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))||session["siteId"].intValue()==25){%>
                htmlStr+="<td>"+(books[i].noOfLic?books[i].noOfLic:'')+"</td>"+
                    "<td>"+(books[i].validity?books[i].validity:'')+"</td>" +
                    "<td><button class='btn btn-sm btn-primary' onclick='getBookUsageDetailsData("+books[i].bookId+","+batchId+")'>View Usage</button></td>" +
                    "<td><button class='btn btn-sm btn-primary' onclick='getBookWaitingListData("+books[i].bookId+","+batchId+")'>Waiting List</button></td>"
                <%}%>
                htmlStr+="</tr>";
            }
            htmlStr +="</table></div>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            <% if((session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))||session["siteId"].intValue()==23||session["siteId"].intValue()==24){%>
            $('#batchUsers table').DataTable( {
                "ordering": ('1'=="${session["siteId"]}" || '25'=="${session["siteId"]}"),
            });
            <%}else{%>
            $('#batchUsers table').DataTable( {
                "ordering": ('1'=="${session["siteId"]}" || '25'=="${session["siteId"]}"),
                "columnDefs": [
                    { "targets": [7,8], "orderable": false }
                ]
            });
            <%}%>
            $('#download').show();
            $('#download2').hide();
            $('#download1').hide();
        }else{
            $('#download2').hide();
            $('#download').hide();
            $('#download1').hide();
            document.getElementById("batchUsers").innerHTML= "No eBooks added to this institute yet";
        }
        $("#batchUsers").removeClass('d-none')
    }
    function instituteAction(key){
        document.getElementById('batchUsers').classList.add('d-none')
        document.getElementById('addUsers').classList.add('d-none')
        document.getElementById('addGenerateAccessCode').classList.add('d-none')
        $("#errormsg").hide();
        $("#successmsg").hide();
        $("#successmsgnew").hide();
        if(key=='viewUser'){
            getUserDetails()
        }else if (key=='addUser'){
            document.getElementById('addUsers').classList.remove('d-none')
        }else if(key=='viewBook'){
            getBooksForBatch()
        } else if (key=='viewWaitingList'){
            getWaitingList();
        }else if(key=='generateAccessCode'){
            document.getElementById('addGenerateAccessCode').classList.remove('d-none')
        }else if(key=='viewAccessCode'){
            viewAccessCode();
        }
    }
    function getBookUsageDetailsData(bookId, batchId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="bookUsageDetailsData"
                          params="'batchId='+batchId+'&bookId='+bookId" onSuccess="bookDetailsDataRecieved(data)"/>
    }
    function bookDetailsDataRecieved(data) {
        if(data.status==='OK'){
            $('.loading-icon').addClass('hidden');
            var htmlStr="<button type='button' class='batchusers btn btn-sm btn-primary my-3' onclick='javascript:instituteAction(\"viewBook\");'>Go Back</button><table class='batchusers table table-striped table-bordered'>" +
                "<tr><th>Book Id</th><th>Username</th><th>Expiry Date</th><th>Date Added</th></tr>"
            var i=0;
            data.users.forEach(user=>{
                i++;
                htmlStr+="<tr id='"+i+"' ><td>"+user.bookId+"</td><td>"+(user.username.substring(user.username.indexOf('_')+1))+"</td><td>"+(user.expiryDate?new Date(user.expiryDate).toDateString():'')+"</td><td>"+(user.dateCreated?new Date(user.dateCreated).toDateString():'')+"</td>" +
                    "</tr>"
            })
            htmlStr+="</table>"
            document.getElementById('batchUsers').innerHTML=htmlStr
            $("#batchUsers").show()
            $('#download').hide();
        }else{
            $('.loading-icon').addClass('hidden');
            var htmlStr = "<button type='button' class='btn btn-sm btn-primary my-3' onclick='javascript:instituteAction(\"viewBook\");'>Go Back</button><br/><p>No users found for selected book.</p>"
            document.getElementById('batchUsers').innerHTML=htmlStr
            $("#batchUsers").show()
            $('#download').hide();
        }
    }

    function getWaitingList() {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="getQueueListByBatchId" params="'batchId='+batchId" onSuccess="showAllWaitingList(data)"/>
    }
    function showAllWaitingList(data) {
        $('.loading-icon').addClass('hidden');
        var books = data.QueueBooksByBookId;
        var htmlStr="<table class='table table-striped table-bordered'>" +
            "<tr><th width='30%'>Book Title</th><th width='20%'>Name</th><th>Username</th><th width='20%'>Email</th><th>Mobile</th></tr>";
        if(books.length>0) {
            for(var i=0;i<books.length;i++){
                htmlStr+="<tr>" +
                    "<td>"+books[i].title+"</td>" +
                    "<td>"+books[i].name+"</td>" +
                    "<td>"+(books[i].username.substring(books[i].username.indexOf('_')+1))+"</td>" +
                    "<td>"+books[i].email+"</td>" +
                    "<td>"+books[i].mobile+"</td>" +
                    "</tr>";
            }
        } else {
            htmlStr+="<tr>" +
                "<th colspan='5' class='text-center'>No users found in waiting list.</th>" +
                "</tr>";
        }
        htmlStr+="</table>";
        document.getElementById('batchUsers').innerHTML=htmlStr;
        $("#batchUsers").removeClass('d-none');
    }

    function getBookWaitingListData(bookId, batchId) {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wsLibrary" action="getQueueListByBookIdAndBatchId"
                          params="'batchId='+batchId+'&bookId='+bookId" onSuccess="bookWaitingListDataRecieved(data)"/>
    }
    function bookWaitingListDataRecieved(data) {
        $('.loading-icon').addClass('hidden');
        var books = data.QueueBooksByBookId;
        var htmlStr="<button type='button' class='btn btn-sm btn-primary my-3' onclick='javascript:instituteAction(\"viewBook\");'>Go Back</button><table class='managebooks table table-striped table-bordered'>" +
            "<tr><th>Name</th><th>Username</th><th>Email</th><th>Mobile</th></tr>";
        if(books.length>0) {
            for(var i=0;i<books.length;i++){
                htmlStr+="<tr id='"+i+"'>" +
                    "<td>"+books[i].name+"</td>" +
                    "<td>"+(books[i].username.substring(books[i].username.indexOf('_')+1))+"</td>" +
                    "<td>"+books[i].email+"</td>" +
                    "<td>"+books[i].mobile+"</td>" +
                    "</tr>";
            }
        } else {
            htmlStr+="<tr>" +
                "<th colspan='4' class='text-center'>No users found for selected book.</th>" +
                "</tr>";
        }
        htmlStr+="</table>";
        document.getElementById('batchUsers').innerHTML=htmlStr;
        $("#batchUsers").removeClass('d-none');
    }

    async function uploadNotes() {
        $("#errormsg").hide();
        $("#successmsg").hide()
        var password=document.getElementById("institutePassword1").value;
        password=password.replace(/&/g,'~');
        var qImg = document.getElementById("FileInputElement").files[0];
        if(qImg==undefined){
            document.getElementById("errormsg").innerText="Please upload the file to proceed.";
            $("#errormsg").show();
            $("#errormsg").removeClass('d-none')
        }else {
            if(noOfUsers>-1 && pendinglicenses <= (await getRowCount(qImg)-1)){
                document.getElementById("errormsg").innerText="No required number of licenses left.";
                $("#errormsg").show();
                $("#errormsg").removeClass('d-none')
                return
            }
            var formData = new FormData();
            formData.append('file', qImg);
            formData.append('batchId', batchId);
            formData.append('mode', "submit");
            formData.append('password', password);
            $.ajax({
                type: 'POST',
                url: '/institute/generateUserAddByExcel?'+(userType=='instructor'?'userType=instructor':'')+'&instituteId='+instituteId,
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    if(data.status=="FILE_ERROR"){
                        document.getElementById("errormsg").innerText="Please check the file and try again.";
                        $("#errormsg").show();
                        $("#errormsg").removeClass('d-none')
                    }else{
                        var usersAdded = data.usersAdded?data.usersAdded.split(',').length:0
                        var usersAlreadyPresent = data.usersAlreadyPresent?data.usersAlreadyPresent.split(',').length:0
                        var usersNotAdded = data.usersNotAdded?data.usersNotAdded.split(',').length:0
                        var html=""
                        html+=
                            "<table class='display nowrap text-center' style='width:100%'>" +
                            "<thead> <tr> <th>Users</th> <th>Count</th> <th>Action</th> </tr> </thead>" +
                            "<tbody> " +
                            "<tr> <td>Added</td> <td>"+usersAdded+"</td> <td><button onclick='showUsersNames(\"added\")' class='btn btn-primary'>View</button></td> </tr>" +
                            "<tr> <td>Already Present</td> <td>"+usersAlreadyPresent+"</td> <td><button onclick='showUsersNames(\"present\")'  class='btn btn-primary'>View</button></td> </tr>" +
                            "<tr> <td>Not Added</td> <td>"+usersNotAdded+"</td> <td>"+((usersNotAdded>0)?"<button onclick='downloadErrorUsers("+data.errorFile+")' class='btn btn-primary'>Download</button>":"")+"</td> </tr>" +
                            " </tbody>" +
                            "</table>"+
                                "<p id='usersAdded' class='d-none'><strong>Users Added: </strong>"+(data.usersAdded?data.usersAdded:"No Users")+"</p>"+
                                "<p id='usersAlreadyPresent' class='d-none'><strong>Users already present: </strong>"+(data.usersAlreadyPresent?data.usersAlreadyPresent:"No Users")+"</p>"
                        document.getElementById("successmsg").innerHTML=html
                        $("#successmsg").show();
                        $("#successmsg").removeClass('d-none')
                    }
                    document.getElementById("institutePassword1").value=""
                    document.getElementById("FileInputElement").value=""
                    refreshLicenses()
                },
                error: function (data) {
                    console.log("error");
                }
            });
        }
    }
    const showUsersNames=(data)=>{
        if(data=='added'){
            document.getElementById('usersAdded').classList.remove('d-none')
            document.getElementById('usersAlreadyPresent').classList.add('d-none')
        }
        if(data=='present'){
            document.getElementById('usersAdded').classList.add('d-none')
            document.getElementById('usersAlreadyPresent').classList.remove('d-none')
        }
    }
    const downloadErrorUsers = (file)=>{
        window.location.href = "/institute/downloadErrorUsers?file="+file
    }
    const getRowCount=(file)=> {
        return new Promise((resolve, reject) => {
            var reader = new FileReader();

            reader.onload = function (e) {
                var data = e.target.result;
                var workbook = XLSX.read(data, {
                    type: 'binary'
                });

                workbook.SheetNames.forEach(function (sheetName) {
                    resolve(XLSX.utils.sheet_to_row_object_array(workbook.Sheets[sheetName]).length)
                })
            };

            reader.onerror = function (ex) {
                console.log(ex);
            };

            reader.readAsBinaryString(file);
        })
    }
    const downloadUploadFileSample=()=>{
        if(userType=='instructor'){
            window.location.href = "/institute/downloadUserUploadSample?userType=Instructors"
        }else{
            window.location.href = "/institute/downloadUserUploadSample"
        }

    }
    const refreshLicenses=()=>{
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="institute" action="getUserStats" params="'instituteId='+instituteId" onSuccess="refreshLicensesData(data)"/>
    }
    const refreshLicensesData=(data)=>{
        $('.loading-icon').addClass('hidden');
        noOfUsers=data.noOfUsers
        usedlicenses=data.usedlicenses
        pendinglicenses=data.pendinglicenses
        if(noOfUsers!=-1){
            document.getElementById('noOfUsers').innerText = "Total number of licenses: "+data.noOfUsers
            document.getElementById('usedlicenses').innerText = "Total number of licenses used: "+data.usedlicenses
            document.getElementById('pendinglicenses').innerText = "Total number of licenses pending: "+data.pendinglicenses
        }
    }
    function deleteUser(userId,batchId){
        $("#successmsg").hide();
        $("#successmsgnew").hide();
        if(confirm("Do you want to go ahead and delete this user from the institute?")) {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="institute" action="removeUserFromBatch" params="'batchId='+batchId+'&userId='+userId" onSuccess = "userDeleted(data);"/>
        }
    }
    function userDeleted(data){
        if(data.status==="OK"){
            document.getElementById("successmsgnew").innerText="Deleted successfully."
            refreshLicenses()
            getUserDetails()
            $('.loading-icon').addClass('hidden');
            $("#successmsgnew").show();
            $("#successmsgnew").removeClass('d-none')
        }
    }

   function viewAccessCode(){
        <g:remoteFunction controller="institute" action="getAllAccessCodes"
            params="'instituteId='+instituteId" onSuccess = "showAccessCodes(data)"/>
    }

    function showAccessCodes(data){
        if(data.status=='OK'){
            var htmlStr = "<a class='btn btn-primary mb-3' target='_blank'  href='/institute/downloadAccessCodeDetails?instituteId="+instituteId+"' style='border-width: 0px;font-size:14px;padding:0.375rem 0.75rem;' >Download Access Codes Details</a>" +
                "<table class='table table-striped table-bordered' id='viewAccesscode'>" +
                "<thead class='bg-primary text-white text-center'><tr class=''><th class=''>Serial No.</th><th class=''>Access Code</th><th class=''>Status</th><th class=''>Username</th><th class=''>Date Created</th><th class=''>Date Redeemed</th></tr></thead>"
            var i=0
            data.accessCodes.forEach(ac=>{
                i++
                htmlStr +="<tr id='"+i+"' class=''><td class=''>"+i+"</td><td class=''>"+ac.code+"</td><td class=''>"+(ac.status?ac.status:"")+"</td><td class=''>"+(ac.username?ac.username.split(siteId1+'_')[1]:"")+"</td><td class=''>"+new Date(ac.dateCreated).getDate()+"/"+(new Date(ac.dateCreated).getMonth()+1)+"/"+new Date(ac.dateCreated).getFullYear()+"</td><td class=''>"+(ac.dateRedeemed ? (new Date(ac.dateRedeemed).getDate()+"/"+(new Date(ac.dateRedeemed).getMonth()+1)+"/"+new Date(ac.dateRedeemed).getFullYear()):"")+"</td></tr>"
            })
            htmlStr+="</table>"
            document.getElementById('batchUsers').innerHTML = htmlStr;
            $('#viewAccesscode').DataTable({
                "ordering": false
            });
        }else{
            document.getElementById('batchUsers').innerHTML = "<p class='mx-4'>No Access Codes Found.</p>"
        }
        $("#batchUsers").removeClass('d-none')
    }

    function addGenerateAccessCode(){
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        var accesscode=document.getElementById("accesscodegen").value;
        if(accesscode=="" ){
            document.getElementById("errormsg").innerHTML="Please enter the count of access code.";
            $("#errormsg").show();
            $("#accesscodegen").focus();
        }else if(accesscode==0 || accesscode.includes("-")){
            document.getElementById("errormsg").innerHTML="Please enter valid access code count.";
            $("#errormsg").show();
            $("#accesscodegen").focus();
        }
        else {
            var accessCount = document.getElementById("accesscodegen").value;
            if(noOfUsers!=-1) {
            if (pendinglicenses >= parseInt(document.getElementById("accesscodegen").value)) {
                $('.loading-icon').removeClass('hidden');
                $("#errormsg").hide();
                <g:remoteFunction controller="institute" action="generateAccessCodeForInstitute" params="'accessCount='+accessCount+'&instituteId='+instituteId" onSuccess = "accessUpdated(data);"/>
            } else {
                document.getElementById('errormsg').innerText='No licenses left.'
                $("#errormsg").show();
            }
        }else{
                $('.loading-icon').removeClass('hidden');
                <g:remoteFunction controller="institute" action="generateAccessCodeForInstitute" params="'accessCount='+accessCount+'&instituteId='+instituteId" onSuccess = "accessUpdated(data);"/>

            }
        }

    }


    function accessUpdated(data) {
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();
        if(data.status=="OK"){
            document.getElementById("successmsg").innerHTML="Added Successfully!";
            $("#successmsg").show().removeClass('d-none');
            refreshLicenses();
            $("#accesscodegen").val('');
        }
    }

    window.onload=function (){
        refreshLicenses()
        if('1'=="${session["siteId"]}" || '25'=="${session["siteId"]}"){
            document.getElementById('addUsers').classList.add('d-none')
            document.getElementById('addGenerateAccessCode').classList.add('d-none')
        }
        <%if("Instructors".equals(userType)&&(session["siteId"].intValue()==1||"true".equals(""+session["commonWhiteLabel"]))){%>
        userType = "instructor";
        <%}%>

    }

    $("#accesscodegen").keyup(function () {
        $("#errormsg").hide();
        this.value = this.value.replace(/[^0-9]/g, '');
    }).on('keypress', function () {
        if(this.value.length==3) return false;
    });

    function toggleAmenities(id){
        var tab=document.getElementById(id)
        if($(tab).hasClass("toggled")){
            document.getElementById(id).classList.remove('toggled');
            $(tab).children('td:not(:first-child)').hide();
            $(tab).children('td:has(#toggle)').find('#toggle').text('+');
        }
        else{
            $(tab).children('td:not(:first-child)').show();
            document.getElementById(id).classList.add('toggled');
            $(tab).children('td:has(#toggle)').find('#toggle').text('-');

        }
    }


    function addUser1(e) {
        e.preventDefault();
        $("#addOrderResult").html("").hide();
        if (document.getElementById("userName").value == "") {
            alert("Please enter name.");
            document.getElementById("userName").focus();

        } else if (document.getElementById("mobile").value == "") {
            alert("Please enter mobile number.");
            document.getElementById("mobile").focus();

        } else {
            var name = document.getElementById("userName").value;
            var mobile = document.getElementById("mobile").value;
            var email = document.getElementById("email").value;
            var pincode = document.getElementById("pincode").value;
            var validityDate = document.getElementById("validityDate").value;

            <g:remoteFunction controller="institute" action="createUserAndAddToLibrary" params="'name='+name+'&mobile='+mobile+'&email='+email+'&pincode='+pincode+'&instituteId=${instituteId}&validityDate='+validityDate " onSuccess = "userAddedNew(data);"/>
            $('.loading-icon').removeClass('hidden');
        }

    }

    function userAddedNew(data){
        $('.loading-icon').addClass('hidden');
        alert("User added. Login id="+data.user+" and password="+data.user);
        document.getElementById("addOrderResult").innerHTML="<p> User added. Login id="+data.user+" and password="+data.user+"</p>";
        $("#addOrderResult").show();
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }

    function clearFields(e){
        e.preventDefault()
        document.getElementById("userName").value="";
        document.getElementById("mobile").value="";
        document.getElementById("userEmail").value="";
        document.getElementById("pincode").value="";

    }

    var validityEditUsername;
    function updateValidity(username,validityDate,name){
        validityEditUsername=username;
        $('#validityEditModal').modal('show');
        document.getElementById("editName").innerHTML="<b>Name:&nbsp;</b>"+name;
        document.getElementById("validityDateEdit").value=validityDate;
    }

    function formCancel(){
        $('#validityEditModal').modal('hide');
    }

    function formSave() {
        $('.loading-icon').removeClass('hidden');
        var validityDate = document.getElementById("validityDateEdit").value;
        <g:remoteFunction controller="libraryBooks" action="updateUserLibraryValidity" params="'batchId='+batchId+'&username='+validityEditUsername+'&validityDate='+validityDate" onSuccess="validityUpdated(data)" />

    }

    function validityUpdated(data){
        $('.loading-icon').addClass('hidden');
        alert("Validity Date updated");
        location.reload();
    }
    $(function(){
        $('*[name=validityDate]').appendDtpicker({
            "autodateOnStart": false,
            "dateOnly":true,
            "dateFormat": "DD-MM-YYYY"
        });
        $('*[name=validityDateEdit]').appendDtpicker({
            "autodateOnStart": false,
            "dateOnly":true,
            "dateFormat": "DD-MM-YYYY"
        });

    });


</script>
</body>
</html>
