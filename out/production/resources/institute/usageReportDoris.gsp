<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader"></g:render>
<asset:javascript src="moment.min.js"/>

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />

<script>
    var loggedIn=false;
</script>
<style>
#batchUsers {
    overflow-x: auto;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}

.form-group .btn-group {
    width: 100%; }
.form-group .btn-group .multiselect.dropdown-toggle {
    width: 100% !important;
    height: 44px;
    line-height: normal;
    background-color: #FFFFFF;
    text-align: left;
    padding: 12px 0 12px 16px;
    box-shadow: none;
    border: 1px solid #cccccc;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
    color: #000000;
    font-weight: normal; }
.form-group .btn-group .multiselect-container {
    width: 100%;
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64);
    max-height: 250px;
    overflow: hidden;
    overflow-y: auto; }
.form-group .btn-group .multiselect-container li {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64); }
.form-group .btn-group .multiselect-container a {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64); }
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <div class="form-group">
                        <h3 class="text-center">USAGE REPORT</h3>
                        <div class="d-flex align-items-center">
                            <div class="form-group col-4">
                                <label for="startDate">From Date</label><br>
                                <input type="text" class="w-100 form-control" id="startDate" name="startDate" placeholder="From Date" autocomplete="off" >
                            </div>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="form-group col-4">
                                <label for="endDate">To Date</label><br>
                                <input type="text" class="w-100 form-control" id="endDate" name="endDate" placeholder="To Date" autocomplete="off" >
                            </div>
                        </div>
                        <label for="batches" style="display: block;">Report</label>
                        <select name="report" id="report" class="form-control col-4" style="">
                            <option value="">Select Report</option>
                            <option value="login">Registered Users Report</option>
                            <option value="usagebookview">Usage Report By Books view</option>
                            <option value="usagesresview">Usage Report By Student Resource view</option>
                            <option value="usagesresinsview">Usage Report By Instructor Resource view</option>
                        </select>

                        <button onclick="getReport()" class="btn btn-lg btn-primary mt-4">Get Report</button>

                    </div>



                    <div style="margin-top: 10px;">
                        <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                        <div id="successmsg" style="display: none"></div>
                        <div id="batchUsers" style="display: none;"></div>
                        <div style="margin-right: 25px; display: none;" id="download">
                            <div class="form-group">
                                <button type="button" id="download-btn" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                            </div>
                        </div>
                        <div style="margin-right: 25px; display: none;" id="download1">
                            <div class="form-group">
                                <button type="button" id="download-btn1" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                            </div>
                        </div>
                        <div style="margin-right: 25px; display: none;" id="download2">
                            <div class="form-group">
                                <button type="button" id="download-btn2" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                            </div>
                        </div>
                        <div style="margin-right: 25px; display: none;" id="download3">
                            <div class="form-group">
                                <button type="button" id="download-btn3" class="btn btn-primary " style="border-width: 0px;" >Download</button>
                            </div>
                        </div>
                    </div>

                </div>


            </div>
        </div>



    </div>
</div>

<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
%{--<asset:javascript src="jquery.simple-dtpicker.js"/>--}%
%{--<asset:stylesheet href="jquery.simple-dtpicker.css"/>--}%

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>


<script>

    $('#startDate, #endDate').datepicker({
        format: 'dd-mm-yyyy',
        //startView: 1,
        //todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });



    /*$(function(){
        $('*[name=startDate]').appendDtpicker({
            "futureOnly": false,
            "autodateOnStart": false,
            "dateFormat": "DD-MM-YYYY",
            "dateOnly": true
        });

        $('*[name=endDate]').appendDtpicker({
            "futureOnly": false,
            "autodateOnStart": false,
            "dateFormat": "DD-MM-YYYY",
            "dateOnly": true
        });
    });*/


    function getReport() {
        var StartDate = document.getElementById("startDate").value;
        var EndDate = document.getElementById("endDate").value;
        var inValidStartDate = false;
        if (document.getElementById("startDate").value != "" &&  document.getElementById("endDate").value != ""){
            var  startDate1 = new Date(StartDate.split('-')[2],StartDate.split('-')[1],StartDate.split('-')[0]);
            var  endDate1 = new Date(EndDate.split('-')[2],EndDate.split('-')[1],EndDate.split('-')[0]);
            if(endDate1.getTime() < startDate1.getTime()) inValidStartDate = true;
        }
         if (document.getElementById("startDate").value === "" || document.getElementById("endDate").value === "") {
            document.getElementById("errormsg").innerHTML = "Please Select From Date and To Date."
            $("#errormsg").show();
            $("#batchUsers").hide();
             $('#download').hide();
             $('#download2').hide();
             $('#download1').hide();
        } else if (inValidStartDate){
            document.getElementById("errormsg").innerHTML="Please enter valid From Date. From Date cannot be greater then To date";
            $("#errormsg").show();
            $("#batchUsers").hide();
             $('#download').hide();
             $('#download2').hide();
             $('#download1').hide();
        }else if (document.getElementById("report")[document.getElementById("report").selectedIndex].value==""){
             document.getElementById("errormsg").innerHTML="Please select report";
             $("#errormsg").show();
             $("#batchUsers").hide();
             $('#download').hide();
             $('#download2').hide();
             $('#download1').hide();
         }else {
             $("#errormsg").hide();
             $('#download').hide();
             $('#download2').hide();
             $('#download1').hide();



             var report = document.getElementById("report")[document.getElementById("report").selectedIndex].value;
             if(report=="login") {
                 <g:remoteFunction controller="institute" action="registeredUserReportData" params="'startDate='+StartDate+'&endDate='+EndDate" onSuccess = "showLogin(data);"/>
             }
             else if(report=="usagebookview"){
                 <g:remoteFunction controller="institute" action="usageReportBooksView" params="'startDate='+StartDate+'&endDate='+EndDate" onSuccess = "showUsagebookview(data);"/>
             }
             else if(report=="usagesresview"){
                 <g:remoteFunction controller="institute" action="usageReportResourceView" params="'startDate='+StartDate+'&endDate='+EndDate" onSuccess = "showUsageresview(data);"/>
             }
             else  if(report=="usagesresinsview"){
                 <g:remoteFunction controller="institute" action="usageReportInstructorResourceView" params="'startDate='+StartDate+'&endDate='+EndDate" onSuccess = "showUsageinsresview(data);"/>

             }
        }
    }



    function showUsagebookview(data){

        var total=data.userList;
        $('.loading-icon').addClass('hidden');
        var htmlStr= "      <h4 class=\"text-center w-100 mt-3 px-5\">Total : <strong>"+total.length+"</strong></h4><table class='table table-responsive table-striped table-bordered'>\n" +
            "                        <tr>\n" +
            "                            <th>Book Id</th>\n" +
            "                            <th>Isbn</th>\n" +
            "                            <th>Title</th>\n" +
            <% if("sage".equals(session["entryController"])){%>
            "                            <th>UserType</th>\n" +

            <%}%>

            "                            <th>No. of views</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var report = data.userList;
            for(var i=0;i<report.length;i++){
                htmlStr +=   "<td>"+report[i].id+"</td>" +
                    "<td>"+report[i].isbn+"</td>" +
                    "<td>"+report[i].title+"</td>" +
                    <% if("sage".equals(session["entryController"])){%>
                    "<td>"+report[i].userType+"</td>" +
                    <%}%>
                    "<td>"+report[i].views+"</td>" +
                    "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download').hide();
            $('#download2').hide();
            $('#download1').show();
            $('#download3').hide();
        }else{
            document.getElementById("batchUsers").innerHTML= "No Records Found.";
            $('#download').hide();
            $('#download1').hide();
            $('#download2').hide();
            $('#download3').hide();
        }
        $("#batchUsers").show();
    }

    function showLogin(data){
        var total= data.results;
        $('.loading-icon').addClass('hidden');
        var htmlStr= "   <h4 class=\"text-center w-100 mt-3 px-5\">Total : <strong>"+total.length+"</strong></h4><table class='table table-responsive table-striped table-bordered'>\n" +
            "                        <tr>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Email</th>\n" +
            "                            <th>Mobile</th>\n" +
            <% if("sage".equals(session["entryController"])){%>
            "                            <th>UserType</th>\n" +

            <%}%>
            "                            <th>Department </th>\n" +
            "                            <th>Institute </th>\n" +
            "                            <th>Area of interests</th>\n" +
            "                            <th>Country</th>\n" +
            "                            <th>Registered On</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var report = data.results;
            for(var i=0;i<report.length;i++){
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+report[i].name+"</td>"+
                    "<td>"+report[i].email+"</td>" +
                    "<td>"+report[i].mobile+"</td>" +
                    <% if("sage".equals(session["entryController"])){%>
                    "<td>"+report[i].userType+"</td>" +
                    <%}%>
                    "<td>"+report[i].department+"</td>" +
                    "<td>"+report[i].institutions+"</td>" +
                    "<td>"+report[i].interests+"</td>" +
                    "<td>"+report[i].country+"</td>" +
                    "<td>"+report[i].registeredon+"</td>" +
                    "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download1').hide();
            $('#download2').hide();
            $('#download3').hide();
            $('#download').show();
        }else{
            document.getElementById("batchUsers").innerHTML= "No Records Found.";
            $('#download').hide();
            $('#download3').hide();
            $('#download1').hide();
            $('#download2').hide();
        }
        $("#batchUsers").show();
    }

    function showUsageresview(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "   <h4 class=\"text-center w-100 mt-3 px-5\"><table class='table table-responsive table-striped table-bordered'>\n" +
            "                        <tr>\n" +
            "                            <th class=\"text-center \">Book Title</th>\n" +
            "                            <th class=\"text-center \">Chapter Name</th>\n" +
            "                            <th class=\"text-center \">Resource Type</th>\n" +
            "                            <th class=\"text-center \">No. of views</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var report = data.report;

            for(var i=0;i<report.length;i++){
                var resType=report[i].restype;
                if(resType=="QA"){
                    resType="Long Answer"
                }else if(resType=="Short QA"){
                    resType="Short Answer"
                }else if(resType=="KeyValues"){
                    resType="Flash Cards"
                }else{
                    resType= report[i].restype;
                }

                htmlStr +="<td class=\"text-left \">"+report[i].bookTitle+"</td>" +
                    "<td class=\"text-left \">"+report[i].chapterName+"</td>" +
                    "<td class=\"text-left \">"+resType+"</td>" +
                    "<td class=\"text-right \">"+report[i].views+"</td>" +
                    "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download1').hide();
            $('#download2').show();
            $('#download').hide();
            $('#download3').hide();
        }else{
            document.getElementById("batchUsers").innerHTML= "No Records Found.";
            $('#download').hide();
            $('#download1').hide();
            $('#download2').hide();
            $('#download3').hide();
        }
        $("#batchUsers").show();
    }


    function showUsageinsresview(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "   <h4 class=\"text-center w-100 mt-3 px-5\"><table class='table table-responsive table-striped table-bordered'>\n" +
            "                        <tr>\n" +
            "                            <th class=\"text-center \">Book Title</th>\n" +
            "                            <th class=\"text-center \">Resource Type</th>\n" +
            "                            <th class=\"text-center \">No. of views</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var report = data.report;
            for(var i=0;i<report.length;i++){
                var resType=report[i][0].subtab;
                if(resType=="teachingSlides"){
                    resType="Teaching Slides"
                }else if(resType=="teachingNotes"){
                    resType="Teaching Notes"
                }else if(resType=="sampleChapters"){
                    resType="Sample Chapters"
                }else if(resType=="mediaLinks"){
                    resType="Media Links"
                }else if(resType=="exercises"){
                    resType="Exercises"
                }else{
                    resType= report[i][0].subtab;
                }
                console.log(report[i]);
                htmlStr +="<td class=\"text-left \">"+report[i][0].bookTitle+"</td>" +
                    "<td class=\"text-left \">"+resType+"</td>" +
                    "<td class=\"text-right \">"+report[i][0].views+"</td>" +
                    "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download1').hide();
            $('#download2').hide();
            $('#download').hide();
            $('#download3').show();
        }else{
            document.getElementById("batchUsers").innerHTML= "No Records Found.";
            $('#download').hide();
            $('#download1').hide();
            $('#download2').hide();
            $('#download3').hide();
        }
        $("#batchUsers").show();
    }
    // window.onload=showUsageReportData();
    // $(document).ready(function() {$('#selectCheckbox').multiselect(); });

    $('#download-btn').on('click', function() {

        var startDate=document.getElementById("startDate").value
        var endDate=document.getElementById("endDate").value

        window.location.href = "/institute/registeredUserReportData?download=true&startDate="+startDate+"&endDate="+endDate;
    });
    $('#download-btn1').on('click', function() {

        var startDate=document.getElementById("startDate").value
        var endDate=document.getElementById("endDate").value

        window.location.href = "/institute/downloadusageReportBooksView?download=true&startDate="+startDate+"&endDate="+endDate;
    });
    $('#download-btn2').on('click', function() {

        var startDate=document.getElementById("startDate").value
        var endDate=document.getElementById("endDate").value

        window.location.href = "/institute/downloadusageReportResourceView?download=true&startDate="+startDate+"&endDate="+endDate;
    });
    $('#download-btn3').on('click', function() {

        var startDate=document.getElementById("startDate").value
        var endDate=document.getElementById("endDate").value

        window.location.href = "/institute/downloadusageReportInstructorResourceView?download=true&startDate="+startDate+"&endDate="+endDate;
    });
</script>

</body>
</html>
