<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
}

.pagination li {
    display: inline;
}

.pagination li a, .pagination li span {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    text-decoration: none;
}

.pagination li a:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination li.active span {
    z-index: 1;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.pagination li.disabled span {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
    border-color: #dee2e6;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container">
    <br>
    <div class="d-flex justify-content-between align-items-center">
        <button onclick="window.history.back();" class="btn btn-secondary">Back</button>
        <h2 class="text-center flex-grow-1">Manage Books for ${instituteName}</h2>
    </div><br>

    <!-- Tabs -->
    <ul class="nav nav-tabs" id="bookTabs">
        <li class="active nav-link"><a href="#listTab" data-toggle="tab" role="tab">List Books</a></li>
        <li><a href="#addTab" class="nav-link" data-toggle="tab" role="tab">Add Books</a></li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- List Books Tab -->
        <div class="tab-pane fade in active" id="listTab">
            <!-- Search -->
            <form id="searchForm" class="form-inline" style="margin-top: 15px;">
                <input type="hidden" name="batchId" value="${batchId}"/>
                <div class="form-group">
                    <label for="search">Search:</label>
                    <input type="text" id="search" name="search" value="${search}" class="form-control" placeholder="Book Title, ID, ISBN"/>
                </div>
            </form>
            <br/>

            <!-- Books Table -->
            <div id="bookList">
                <table class="table table-striped">
                    <thead>
                    <tr>
                        <th>Book ID</th>
                        <th>Title</th>
                        <th>ISBN</th>
                        <th>Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    <g:if test="${books}">
                        <g:each in="${books}" var="book">
                            <tr>
                                <td>${book.id}</td>
                                <td>${book.title}</td>
                                <td>${book.isbn}</td>
                                <td>
                                    <g:link controller="institute" action="removeBookFromBatchSage" params="[batchId: batchId, bookId: book.id]" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to remove this book from the batch?');">Remove</g:link>
                                </td>
                            </tr>
                        </g:each>
                    </g:if>
                    <g:else>
                        <tr>
                            <td colspan="4">No books found in this batch.</td>
                        </tr>
                    </g:else>
                    </tbody>
                </table>
                <!-- Pagination Controls -->

            </div>
            <div id="paginationControls">
                <g:paginate total="${totalCount}" params="${params}"/>
            </div>
        </div>

        <!-- Add Books Tab -->
        <div class="tab-pane fade" id="addTab"><br>
            <div id="addBooks">
                <div class="form-group col-md-5">
                    Select input type:&nbsp;&nbsp;&nbsp;
                    <input type="radio" value="true" name="bookIdMethod">&nbsp;BookId&nbsp;&nbsp;&nbsp;
                    <input type="radio" value="false" name="bookIdMethod">&nbsp;ISBNs
                </div>
                <div class="addBooks-inner">
                    <div class="form-group col-md-5 ">
                        <textarea class="form-control admin" name="bookId" id="bookId"  placeholder="Comma seperated book ids, or isbns"></textarea><br>
                        <button class="btn btn-primary btadd" onclick="addBook();">Add Book/s</button>
                    </div>
                </div>
                <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;word-wrap: break-word;"></div>
                <div id="successmsg" style="display: none"></div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
<script type="text/javascript">
    $(document).ready(function() {
        // Activate the first tab by default
        $('#bookTabs a[href="#listTab"]').tab('show');

        var max = ${max};

        // Function to update book list
        function updateBookList(offsetValue) {
            var search = $('#search').val();
            var offsetParam = offsetValue !== undefined ? offsetValue : 0;
            $.ajax({
                url: '${createLink(controller: 'instManager', action: 'searchBooksInBatch')}',
                data: {
                    batchId: '${batchId}',
                    search: search,
                    max: max,
                    offset: offsetParam
                },
                dataType: 'json',
                success: function(data) {
                    var html = '<table class="table table-striped"><thead><tr><th>Book ID</th><th>Title</th><th>ISBN</th><th>Actions</th></tr></thead><tbody>';
                    if (data.books.length > 0) {
                        $.each(data.books, function(index, book) {
                            html += '<tr>';
                            html += '<td>' + book.id + '</td>';
                            html += '<td>' + book.title + '</td>';
                            html += '<td>' + book.isbn + '</td>';
                            html += '<td><a href="javascript:removeBook('+book.id+')" class="btn btn-danger btn-sm" onclick="return confirm(\'Are you sure you want to remove this book from the batch?\');">Remove</a></td>';
                            html += '</tr>';
                        });
                    } else {
                        html += '<tr><td colspan="4">No books found in this batch.</td></tr>';
                    }
                    html += '</tbody></table>';
                    $('#bookList').html(html);

                    // Update pagination controls
                    var totalCount = data.totalCount;
                    var totalPages = Math.ceil(totalCount / max);
                    var currentPage = Math.floor(offsetParam / max) + 1;

                    var paginationHtml = '<ul class="pagination">';
                    paginationHtml += '<li class="disabled"><span>Page ' + currentPage + ' of ' + totalPages + '</span></li>';
                    if (currentPage > 1) {
                        paginationHtml += '<li><a href="#" data-offset="' + (offsetParam - max) + '">&laquo;</a></li>';
                    } else {
                        paginationHtml += '<li class="disabled"><span>&laquo;</span></li>';
                    }

                    for (var i = 1; i <= totalPages; i++) {
                        var pageOffset = (i - 1) * max;
                        if (i == currentPage) {
                            paginationHtml += '<li class="active"><span>' + i + '</span></li>';
                        } else {
                            paginationHtml += '<li><a href="#" data-offset="' + pageOffset + '">' + i + '</a></li>';
                        }
                    }

                    if (currentPage < totalPages) {
                        paginationHtml += '<li><a href="#" data-offset="' + (offsetParam + max) + '">&raquo;</a></li>';
                    } else {
                        paginationHtml += '<li class="disabled"><span>&raquo;</span></li>';
                    }

                    paginationHtml += '</ul>';
                    console.log("paginationHtml: ", paginationHtml);
                    $('#paginationControls').html(paginationHtml);
                }
            });
        }

        // Bind keyup event on search input
        $('#search').on('keyup', function() {
            updateBookList();
        });

        // Handle pagination clicks
        $(document).on('click', '#paginationControls a', function(e) {
            e.preventDefault();
            var offsetValue = $(this).data('offset');
            updateBookList(offsetValue);
        });

        // Handle tab show event
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            var target = $(e.target).attr("href"); // activated tab
            if (target == '#listTab') {
                // If necessary, reload the list tab content
                // updateBookList();
            } else if (target == '#addTab') {
                // If necessary, load the add tab content
            }
        });
        // Initial load
        updateBookList(${offset});
    });

    function addBook(){
        $('.loading-icon').addClass('hidden');
        $("#errormsg").hide();
        $("#successmsg").hide();

        if($("input[name='bookIdMethod']:checked"). val()==undefined){
            document.getElementById("errormsg").innerHTML="Please book input type";
            $("#errormsg").show();

        }else if(document.getElementById("bookId").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the book ids/isbns."
            $("#errormsg").show();
        }
        else{
            $('.loading-icon').removeClass('hidden');
            var bookId = document.getElementById("bookId").value;
            var bookIdMethod=$("input[name='bookIdMethod']:checked"). val();

            <g:remoteFunction controller="institute" action="addBooks" params="'instituteId=${instituteId}&bookIds='+bookId+'&bookIdMethod='+bookIdMethod" onSuccess = "bookAdded(data);"/>
        }
    }

    function bookAdded(data){
        $("#errormsg").hide();
        $("#successmsg").hide();
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK"){
            document.getElementById("successmsg").innerHTML="Book added";
            document.getElementById("bookId").value="";
            if(data.booksReAdded){
                document.getElementById("successmsg").innerHTML+= "  <br>"+ data.booksReAdded+ " is already present"
            }
            $("#successmsg").show();


        }
        else{
            $("#successmsg").hide();
            document.getElementById("errormsg").innerHTML=data.booksNotAdded+"  was not added. Either the Book Id/ISBN is incorrect";
            if(data.booksReAdded){
                document.getElementById("errormsg").innerHTML+= "<br>"+ data.booksReAdded+ " is already present"
            }
            $("#errormsg").show();
        }
    }

    function removeBook(bookId){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="institute" action="removeBookFromBatchSage" params="'batchId=${batchId}&bookId='+bookId" onSuccess = "bookRemoved(data);"/>
    }

    function bookRemoved(data){
       //reload the page
        window.location.reload();
    }


</script>
